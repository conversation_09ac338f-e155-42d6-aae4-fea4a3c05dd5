import { ReactNode } from "react";
import { requireUser } from "../../utils/hooks";
import { Toaster } from "@/components/ui/sonner";
import prisma from "../../utils/db";
import { redirect } from "next/navigation";
import { WEGAccountingLayoutClient } from "../../components/weg-accounting/WEGAccountingLayoutClient";
import { Providers } from "../../providers";

async function getUser(userId: string) {
  const data = await prisma.user.findUnique({
    where: {
      id: userId,
    },
    select: {
      firstName: true,
      lastName: true,
      address: true,
      settings: true,
    },
  });

  if (!data?.firstName || !data.lastName || !data.address) {
    // Wenn Ben<PERSON>erdaten unvollständig sind, zum Onboarding weiterleiten,
    // aber mit Rückleitung zum WEG-Accounting-Dashboard
    redirect("/onboarding?callbackUrl=/weg-accounting/dashboard");
  }

  return data;
}

export default async function WEGAccountingDashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  try {
    const session = await requireUser({ redirectToWegLogin: true });
    const data = await getUser(session.user?.id as string);

    // Get user settings from database or use defaults
    const initialTheme = data.settings?.theme || "system";
    const initialLanguage = data.settings?.language || "en";

    return (
      <Providers initialTheme={initialTheme} initialLanguage={initialLanguage}>
        <WEGAccountingLayoutClient>
          {children}
        </WEGAccountingLayoutClient>
        <Toaster richColors closeButton theme="light" />
      </Providers>
    );
  } catch (error) {
    // If authentication fails, redirect to the WEG-Accounting login page
    redirect("/weg-accounting/login?callbackUrl=/weg-accounting/dashboard");
  }
}
