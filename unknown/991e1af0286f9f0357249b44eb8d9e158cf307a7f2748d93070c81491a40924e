"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/app/utils/clientHooks";
import { OwnersTab } from "@/app/components/weg-accounting/owners/OwnersTab";
import { LoadingState } from "@/app/components/LoadingState";
import { Property, Owner } from "@/app/lib/wegTypes";

export default function OwnersPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [owners, setOwners] = useState<Owner[]>([]);
  const [properties, setProperties] = useState<Property[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Only load data if the user is authenticated
    if (isAuthenticated) {
      const loadData = async () => {
        try {
          setIsLoading(true);
          try {
            // Fetch properties from API
            const propertiesResponse = await fetch('/api/weg-accounting/properties');
            if (!propertiesResponse.ok) {
              throw new Error(`Failed to fetch properties: ${propertiesResponse.statusText}`);
            }
            const propertiesData = await propertiesResponse.json();
            setProperties(propertiesData);
            console.log("Properties loaded:", propertiesData);
          } catch (propertiesError) {
            console.error("Failed to load properties:", propertiesError);
          }

          try {
            // Fetch owners from API
            const ownersResponse = await fetch('/api/weg-accounting/owners');
            if (!ownersResponse.ok) {
              throw new Error(`Failed to fetch owners: ${ownersResponse.statusText}`);
            }
            const ownersData = await ownersResponse.json();
            setOwners(ownersData);
            console.log("Owners loaded:", ownersData);
          } catch (ownersError) {
            console.error("Failed to load owners:", ownersError);
          }
        } catch (error) {
          console.error("Failed to load data:", error);
        } finally {
          setIsLoading(false);
        }
      };

      loadData();
    }
  }, [isAuthenticated]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Eigentümer</h1>
        <p className="text-muted-foreground">
          Verwalten Sie die Eigentümer Ihrer Immobilien
        </p>
      </div>

      {authLoading || isLoading ? (
        <LoadingState
          title="Eigentümer werden geladen..."
          description="Bitte warten Sie, während die Daten geladen werden."
        />
      ) : (
        <OwnersTab initialOwners={owners} initialProperties={properties} />
      )}
    </div>
  );
}
