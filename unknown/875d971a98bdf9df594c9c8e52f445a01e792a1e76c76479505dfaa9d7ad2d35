"use client"

import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import Image from "next/image";
import PaidGif from "@/public/paid-gif.gif";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { SubmitButton } from "@/app/components/SubmitButtons";
import { useLanguage } from '@/app/contexts/LanguageContext';
import { useEffect, useState } from 'react';
import { MarkAsPaidAction } from "@/app/actions/markAsPaid";

interface MarkAsPaidClientProps {
  invoiceId: string;
}

export function MarkAsPaidClient({ invoiceId }: MarkAsPaidClientProps) {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="flex flex-1 justify-center items-center">
      <Card className="max-w-[500px]">
        <CardHeader>
          <CardTitle>
            {mounted ? t('markAsPaid') : "Als bezahlt markieren?"}
          </CardTitle>
          <CardDescription>
            {mounted ? t('markAsPaidDesc') : "Sind Sie sicher, dass Sie diese Rechnung als bezahlt markieren möchten?"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Image src={PaidGif} alt="Paid Gif" className="rounded-lg" />
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <Link
            className={buttonVariants({ variant: "outline" })}
            href="/dashboard/invoices"
          >
            {mounted ? t('cancel') : "Abbrechen"}
          </Link>
          <form
            action={async () => {
              await MarkAsPaidAction(invoiceId);
            }}
          >
            <SubmitButton text={mounted ? t('markAsPaidButton') : "Als bezahlt markieren!"} />
          </form>
        </CardFooter>
      </Card>
    </div>
  );
}
