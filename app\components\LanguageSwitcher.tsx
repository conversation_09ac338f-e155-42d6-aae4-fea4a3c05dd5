"use client";

import { useState, useRef, useEffect } from "react";
import { translations } from "@/i18n";
import type { LanguageKey } from "@/i18n";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Languages, Check, ChevronDown, Globe } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";

const languages = [
  { 
    key: "de" as LanguageKey, 
    label: "Deutsch", 
    flag: "🇩🇪",
    shortLabel: "DE"
  },
  { 
    key: "en" as LanguageKey, 
    label: "English", 
    flag: "🇬🇧",
    shortLabel: "EN"
  }
];

export function LanguageSwitcher() {
  const [isOpen, setIsOpen] = useState(false);
  const { language, setLanguage, t } = useLanguage();
  const dropdownRef = useRef<HTMLDivElement>(null);

  const currentLanguage = languages.find(lang => lang.key === language);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const switchLanguage = (lang: LanguageKey) => {
    setLanguage(lang);
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleDropdown}
        className="group relative overflow-hidden bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/40 dark:hover:to-purple-900/40 border border-blue-200/50 dark:border-blue-800/50 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-blue-400/10"
      >
        <div className="flex items-center gap-2">
          <div className="p-1 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <Globe className="h-3 w-3" />
          </div>
          <span className="font-medium text-slate-700 dark:text-slate-300">
            {currentLanguage?.shortLabel}
          </span>
          <ChevronDown 
            className={`h-4 w-4 text-slate-500 transition-transform duration-200 ${
              isOpen ? 'rotate-180' : ''
            }`} 
          />
        </div>
        
        {/* Hover effect overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </Button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="absolute right-0 mt-2 w-56 rounded-xl bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border border-slate-200/50 dark:border-slate-700/50 shadow-2xl shadow-black/10 dark:shadow-black/40 overflow-hidden z-50"
          >
            <div className="p-2">
              <div className="px-3 py-2 mb-2">
                <div className="flex items-center gap-2 text-sm font-medium text-slate-600 dark:text-slate-400">
                  <Languages className="h-4 w-4" />
                  {language === 'de' ? 'Sprache wählen' : 'Select Language'}
                </div>
              </div>
              
              {languages.map((lang) => (
                <motion.button
                  key={lang.key}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => switchLanguage(lang.key)}
                  className={`w-full px-3 py-3 rounded-lg text-left transition-all duration-200 flex items-center justify-between group ${
                    language === lang.key
                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md'
                      : 'hover:bg-slate-50 dark:hover:bg-slate-800/50 text-slate-700 dark:text-slate-300'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-lg">{lang.flag}</span>
                    <div>
                      <p className="font-medium">{lang.label}</p>
                      <p className={`text-xs ${
                        language === lang.key 
                          ? 'text-white/80' 
                          : 'text-slate-500 dark:text-slate-400'
                      }`}>
                        {lang.shortLabel}
                      </p>
                    </div>
                  </div>
                  
                  {language === lang.key && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.1 }}
                    >
                      <Check className="h-4 w-4" />
                    </motion.div>
                  )}
                </motion.button>
              ))}
            </div>
            
            <div className="px-3 py-2 bg-slate-50/50 dark:bg-slate-800/30 border-t border-slate-200/50 dark:border-slate-700/50">
              <p className="text-xs text-slate-500 dark:text-slate-400 text-center">
                {language === 'de' 
                  ? 'Sprache wird automatisch gespeichert'
                  : 'Language is saved automatically'
                }
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 