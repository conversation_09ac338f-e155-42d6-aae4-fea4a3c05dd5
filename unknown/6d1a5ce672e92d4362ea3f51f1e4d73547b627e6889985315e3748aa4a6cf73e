"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, MoreHorizontal, Plus, Search, Edit, Trash2, Users, Calculator } from "lucide-react";

// Typen für die Datenstruktur
export type DistributionKey = {
  id: string;
  name: string;
  type: "squareMeters" | "units" | "consumption" | "personDays" | "custom";
  description: string;
};

// Import the Property type from wegTypes.ts to ensure consistency
import { Property as WegProperty } from "@/app/lib/wegTypes";

// Re-export the Property type from wegTypes.ts
export type Property = WegProperty;

interface PropertyListProps {
  properties: Property[];
  onSelectProperty: (property: Property) => void;
  onUpdateProperties: (properties: Property[]) => void;
}

export function PropertyList({ properties, onSelectProperty, onUpdateProperties }: PropertyListProps) {
  const { language } = useLanguage();

  // State für das Hinzufügen/Bearbeiten von Objekten
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentProperty, setCurrentProperty] = useState<Property | null>(null);
  const [newProperty, setNewProperty] = useState<Omit<Property, "id" | "distributionKeys">>({
    name: "",
    address: "",
    units: 0,
    totalArea: 0,
    yearOfConstruction: new Date().getFullYear(),
  });

  // State für die Suche
  const [searchQuery, setSearchQuery] = useState("");

  // Gefilterte Objekte basierend auf der Suche
  const filteredProperties = properties.filter(property =>
    property.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    property.address.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handler für das Hinzufügen eines neuen Objekts
  const handleAddProperty = async () => {
    try {
      // Importiere die addProperty-Funktion dynamisch
      const { addProperty } = await import("@/app/actions/wegAccounting");

      // Füge das neue Objekt hinzu
      await addProperty({
        name: newProperty.name,
        address: newProperty.address,
        units: newProperty.units,
        totalArea: newProperty.totalArea,
        yearOfConstruction: newProperty.yearOfConstruction
      });

      // Setze das Formular zurück
      setNewProperty({
        name: "",
        address: "",
        units: 0,
        totalArea: 0,
        yearOfConstruction: new Date().getFullYear(),
      });
      setIsAddDialogOpen(false);

      // Lade die Seite neu, um die aktualisierten Daten vom Server zu laden
      window.location.reload();
    } catch (error) {
      console.error("Fehler beim Hinzufügen des Objekts:", error);
      // Hier könnte eine Fehlerbehandlung hinzugefügt werden
    }
  };

  // Handler für das Bearbeiten eines Objekts
  const handleEditProperty = async () => {
    if (!currentProperty) return;

    try {
      // Importiere die updateProperty-Funktion dynamisch
      const { updateProperty } = await import("@/app/actions/wegAccounting");

      // Aktualisiere das Objekt
      const updatedProperty = await updateProperty(currentProperty.id, {
        name: currentProperty.name,
        address: currentProperty.address,
        units: currentProperty.units,
        totalArea: currentProperty.totalArea,
        yearOfConstruction: currentProperty.yearOfConstruction
      });

      // Aktualisiere die lokale Liste
      onUpdateProperties(properties.map(property =>
        property.id === updatedProperty.id ? updatedProperty : property
      ));

      setCurrentProperty(null);
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Fehler beim Aktualisieren des Objekts:", error);
      // Hier könnte eine Fehlerbehandlung hinzugefügt werden
    }
  };

  // Handler für das Löschen eines Objekts
  const handleDeleteProperty = async (id: string) => {
    try {
      // Importiere die deleteProperty-Funktion dynamisch
      const { deleteProperty } = await import("@/app/actions/wegAccounting");

      // Lösche das Objekt
      await deleteProperty(id);

      // Aktualisiere die lokale Liste
      onUpdateProperties(properties.filter(property => property.id !== id));
    } catch (error) {
      console.error("Fehler beim Löschen des Objekts:", error);
      // Hier könnte eine Fehlerbehandlung hinzugefügt werden
    }
  };

  // Funktion zum Öffnen des Bearbeitungsdialogs
  const openEditDialog = (property: Property) => {
    setCurrentProperty({...property});
    setIsEditDialogOpen(true);
  };

  // Funktion zum Übersetzen des Verteilerschlüsseltyps
  const translateKeyType = (type: string) => {
    switch (type) {
      case "squareMeters":
        return language === "de" ? "Quadratmeter" : "Square Meters";
      case "units":
        return language === "de" ? "Einheiten" : "Units";
      case "consumption":
        return language === "de" ? "Verbrauch" : "Consumption";
      case "personDays":
        return language === "de" ? "Personentage" : "Person Days";
      case "custom":
        return language === "de" ? "Individuell" : "Custom";
      default:
        return type;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">
          {language === "de" ? "WEG-Objekte" : "Property Associations"}
        </h2>

        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder={language === "de" ? "Objekte suchen..." : "Search properties..."}
              className="pl-8 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {language === "de" ? "Neues Objekt" : "New Property"}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {language === "de" ? "Neues WEG-Objekt hinzufügen" : "Add New Property Association"}
                </DialogTitle>
                <DialogDescription>
                  {language === "de"
                    ? "Geben Sie die Details des neuen Objekts ein."
                    : "Enter the details of the new property."}
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    {language === "de" ? "Name" : "Name"}
                  </Label>
                  <Input
                    id="name"
                    value={newProperty.name}
                    onChange={(e) => setNewProperty({...newProperty, name: e.target.value})}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="address" className="text-right">
                    {language === "de" ? "Adresse" : "Address"}
                  </Label>
                  <Input
                    id="address"
                    value={newProperty.address}
                    onChange={(e) => setNewProperty({...newProperty, address: e.target.value})}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="units" className="text-right">
                    {language === "de" ? "Einheiten" : "Units"}
                  </Label>
                  <Input
                    id="units"
                    type="number"
                    value={newProperty.units}
                    onChange={(e) => setNewProperty({...newProperty, units: parseInt(e.target.value) || 0})}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="totalArea" className="text-right">
                    {language === "de" ? "Gesamtfläche (m²)" : "Total Area (m²)"}
                  </Label>
                  <Input
                    id="totalArea"
                    type="number"
                    value={newProperty.totalArea}
                    onChange={(e) => setNewProperty({...newProperty, totalArea: parseFloat(e.target.value) || 0})}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="yearOfConstruction" className="text-right">
                    {language === "de" ? "Baujahr" : "Year of Construction"}
                  </Label>
                  <Input
                    id="yearOfConstruction"
                    type="number"
                    value={newProperty.yearOfConstruction || ''}
                    onChange={(e) => setNewProperty({...newProperty, yearOfConstruction: e.target.value ? parseInt(e.target.value) : null})}
                    className="col-span-3"
                  />
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  {language === "de" ? "Abbrechen" : "Cancel"}
                </Button>
                <Button onClick={handleAddProperty}>
                  {language === "de" ? "Hinzufügen" : "Add"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Bearbeitungsdialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {language === "de" ? "WEG-Objekt bearbeiten" : "Edit Property Association"}
            </DialogTitle>
            <DialogDescription>
              {language === "de"
                ? "Bearbeiten Sie die Details des Objekts."
                : "Edit the details of the property."}
            </DialogDescription>
          </DialogHeader>

          {currentProperty && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  {language === "de" ? "Name" : "Name"}
                </Label>
                <Input
                  id="edit-name"
                  value={currentProperty.name}
                  onChange={(e) => setCurrentProperty({...currentProperty, name: e.target.value})}
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-address" className="text-right">
                  {language === "de" ? "Adresse" : "Address"}
                </Label>
                <Input
                  id="edit-address"
                  value={currentProperty.address}
                  onChange={(e) => setCurrentProperty({...currentProperty, address: e.target.value})}
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-units" className="text-right">
                  {language === "de" ? "Einheiten" : "Units"}
                </Label>
                <Input
                  id="edit-units"
                  type="number"
                  value={currentProperty.units}
                  onChange={(e) => setCurrentProperty({...currentProperty, units: parseInt(e.target.value) || 0})}
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-totalArea" className="text-right">
                  {language === "de" ? "Gesamtfläche (m²)" : "Total Area (m²)"}
                </Label>
                <Input
                  id="edit-totalArea"
                  type="number"
                  value={currentProperty.totalArea}
                  onChange={(e) => setCurrentProperty({...currentProperty, totalArea: parseFloat(e.target.value) || 0})}
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-yearOfConstruction" className="text-right">
                  {language === "de" ? "Baujahr" : "Year of Construction"}
                </Label>
                <Input
                  id="edit-yearOfConstruction"
                  type="number"
                  value={currentProperty.yearOfConstruction || ''}
                  onChange={(e) => setCurrentProperty({...currentProperty, yearOfConstruction: e.target.value ? parseInt(e.target.value) : null})}
                  className="col-span-3"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button onClick={handleEditProperty}>
              {language === "de" ? "Speichern" : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Objektliste */}
      {filteredProperties.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredProperties.map((property) => (
            <Card
              key={property.id}
              className="overflow-hidden cursor-pointer hover:border-primary transition-colors relative"
              onClick={() => onSelectProperty(property)}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{property.name}</CardTitle>
                    <CardDescription className="mt-1">{property.address}</CardDescription>
                  </div>
                  <DropdownMenu modal={false}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0 relative z-10 hover:bg-accent hover:text-accent-foreground"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent 
                      align="end" 
                      side="bottom"
                      className="z-[9999] min-w-[160px] bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-lg border border-gray-200 dark:border-gray-700 rounded-md p-1"
                      sideOffset={5}
                      avoidCollisions={true}
                      onCloseAutoFocus={(e) => e.preventDefault()}
                    >
                      <DropdownMenuLabel className="px-2 py-1.5 text-sm font-semibold text-gray-700 dark:text-gray-300">
                        {language === "de" ? "Aktionen" : "Actions"}
                      </DropdownMenuLabel>
                      <DropdownMenuItem 
                        className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 px-2 py-1.5 text-sm rounded-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          openEditDialog(property);
                        }}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        {language === "de" ? "Bearbeiten" : "Edit"}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="cursor-pointer hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-700 dark:hover:text-red-300 px-2 py-1.5 text-sm text-red-600 dark:text-red-400 rounded-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteProperty(property.id);
                        }}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        {language === "de" ? "Löschen" : "Delete"}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator className="my-1 bg-gray-200 dark:bg-gray-600" />
                      <DropdownMenuItem 
                        className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 px-2 py-1.5 text-sm rounded-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onSelectProperty(property);
                        }}
                      >
                        <Users className="h-4 w-4 mr-2" />
                        {language === "de" ? "Eigentümer verwalten" : "Manage Owners"}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 px-2 py-1.5 text-sm rounded-sm"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Calculator className="h-4 w-4 mr-2" />
                        {language === "de" ? "Abrechnung erstellen" : "Create Statement"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex flex-col">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Einheiten" : "Units"}
                    </span>
                    <span className="font-medium">{property.units}</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Gesamtfläche" : "Total Area"}
                    </span>
                    <span className="font-medium">{property.totalArea} m²</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Baujahr" : "Year Built"}
                    </span>
                    <span className="font-medium">{property.yearOfConstruction || '-'}</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Verteilerschlüssel" : "Distribution Keys"}
                    </span>
                    <span className="font-medium">{property.distributionKeys.length}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-muted/50 p-3">
                <div className="w-full">
                  <h4 className="text-sm font-medium mb-2">
                    {language === "de" ? "Verteilerschlüssel" : "Distribution Keys"}
                  </h4>
                  <div className="space-y-1">
                    {property.distributionKeys.map((key) => (
                      <div key={key.id} className="flex justify-between text-xs">
                        <span>{key.name}</span>
                        <span className="text-muted-foreground">{translateKeyType(key.type)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-10">
          <Building className="h-10 w-10 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">
            {language === "de" ? "Keine Objekte gefunden" : "No properties found"}
          </h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {language === "de"
              ? "Beginnen Sie damit, ein neues WEG-Objekt hinzuzufügen."
              : "Start by adding a new property association."}
          </p>
        </div>
      )}
    </div>
  );
}
