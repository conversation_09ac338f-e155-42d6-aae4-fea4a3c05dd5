"use client"

import { useEffect, useState, useTransition } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useLanguage } from '@/app/contexts/LanguageContext'
import { TranslationKey } from "@/i18n"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { saveVisibleTabs, getUserSettings } from "@/app/actions/settings"
import { useToast } from "@/app/context/ToastContext"

// Definiere die verfügbaren Tabs
const availableTabs = [
  { id: "employees", nameKey: "employees" as <PERSON><PERSON><PERSON> },
  { id: "income-expenses", nameKey: "income-expenses" as TranslationKey },
  { id: "ai", nameKey: "ai" as TranslationKey },
  { id: "taxReturn", nameKey: "taxReturn" as Translation<PERSON><PERSON> },
  { id: "wegAccounting", nameKey: "wegAccounting" as <PERSON><PERSON><PERSON> },
];

export function TabVisibilitySettings() {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [selectedTabs, setSelectedTabs] = useState<string[]>([]);
  const { toast } = useToast();

  // Lade die aktuellen Einstellungen
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // Versuche zuerst, die Einstellungen aus dem localStorage zu laden
        const savedTabs = localStorage.getItem('visibleTabs');
        if (savedTabs) {
          setSelectedTabs(savedTabs.split(','));
        } else {
          // Versuche, die Einstellungen aus der Datenbank zu laden
          const settings = await getUserSettings();
          if (settings?.visibleTabs) {
            setSelectedTabs(settings.visibleTabs.split(','));
          } else {
            // Standardmäßig alle Tabs auswählen
            setSelectedTabs(availableTabs.map(tab => tab.id).concat(['dashboard', 'invoices']));
          }
        }
      } catch (error) {
        console.error('Fehler beim Laden der Tab-Einstellungen:', error);
        // Standardmäßig alle Tabs auswählen
        setSelectedTabs(availableTabs.map(tab => tab.id).concat(['dashboard', 'invoices']));
      } finally {
        setMounted(true);
      }
    };

    loadSettings();
  }, []);

  // Checkbox-Handler
  const handleCheckboxChange = (tabId: string, checked: boolean) => {
    if (checked) {
      setSelectedTabs(prev => [...prev, tabId]);
    } else {
      setSelectedTabs(prev => prev.filter(id => id !== tabId));
    }
  };

  // Speichern der Einstellungen
  const handleSaveSettings = () => {
    // Stelle sicher, dass Dashboard und Invoices immer enthalten sind
    let tabsToSave = [...selectedTabs];
    if (!tabsToSave.includes('dashboard')) tabsToSave.push('dashboard');
    if (!tabsToSave.includes('invoices')) tabsToSave.push('invoices');

    startTransition(async () => {
      try {
        // Speichere die Einstellungen im localStorage
        localStorage.setItem('visibleTabs', tabsToSave.join(','));

        // Versuche auch, die Einstellungen in der Datenbank zu speichern
        try {
          await saveVisibleTabs(tabsToSave.join(','));
        } catch (dbError) {
          console.error('Fehler beim Speichern in der Datenbank:', dbError);
          // Ignoriere den Fehler, da wir die Einstellungen bereits im localStorage gespeichert haben
        }

        toast.success(t('settings' as TranslationKey), { description: t('settingsSaved' as TranslationKey) });
      } catch (error) {
        console.error('Fehler beim Speichern der Tab-Einstellungen:', error);
        toast.error(t('error' as TranslationKey), { description: t('settingsError' as TranslationKey) });
      }
    });
  };

  if (!mounted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('navigationTabs' as TranslationKey)}</CardTitle>
          <CardDescription>
            {t('chooseVisibleTabs' as TranslationKey)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="h-6 w-full animate-pulse rounded-lg bg-muted"></div>
            <div className="h-6 w-full animate-pulse rounded-lg bg-muted"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('navigationTabs' as TranslationKey)}</CardTitle>
        <CardDescription>
          {t('chooseVisibleTabs' as TranslationKey)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-2 opacity-50">
              <Checkbox id="dashboard" checked={true} disabled />
              <Label htmlFor="dashboard">{t('dashboard' as TranslationKey)} ({t('alwaysVisible' as TranslationKey)})</Label>
            </div>
            <div className="flex items-center space-x-2 opacity-50">
              <Checkbox id="invoices" checked={true} disabled />
              <Label htmlFor="invoices">{t('invoices' as TranslationKey)} ({t('alwaysVisible' as TranslationKey)})</Label>
            </div>
          </div>

          <div className="flex flex-col space-y-2">
            {availableTabs.map((tab) => (
              <div key={tab.id} className="flex items-center space-x-2">
                <Checkbox
                  id={tab.id}
                  checked={selectedTabs.includes(tab.id)}
                  onCheckedChange={(checked) => handleCheckboxChange(tab.id, checked === true)}
                />
                <Label htmlFor={tab.id}>{t(tab.nameKey)}</Label>
              </div>
            ))}
          </div>

          <Button
            onClick={handleSaveSettings}
            disabled={isPending}
            className="mt-4"
          >
            {isPending ? t('saving' as TranslationKey) : t('saveChanges' as TranslationKey)}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
