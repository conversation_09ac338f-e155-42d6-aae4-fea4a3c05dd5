"use server";

import { revalidatePath } from "next/cache";
import { requireUser } from "@/app/utils/hooks";
import prisma from "@/app/utils/db";

export interface Reserve {
  id: string;
  userId: string;
  propertyId: string;
  name: string;
  description: string;
  balance: number;
  targetAmount: number | null;
  yearlyContribution: number;
  createdAt: Date;
  updatedAt: Date;
  property?: {
    id: string;
    name: string;
  };
  transactions?: ReserveTransaction[];
}

export interface ReserveTransaction {
  id: string;
  reserveId: string;
  date: Date;
  type: string;
  amount: number;
  description: string;
  documentId: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export async function getReserves(): Promise<Reserve[]> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const reserves = await prisma.reserve.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        transactions: {
          orderBy: {
            date: "desc",
          },
        },
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return reserves;
  } catch (error) {
    console.error("Failed to fetch reserves:", error);
    return [];
  }
}

export async function getReserveById(reserveId: string): Promise<Reserve | null> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const reserve = await prisma.reserve.findFirst({
      where: {
        id: reserveId,
        userId: session.user.id,
      },
      include: {
        transactions: {
          orderBy: {
            date: "desc",
          },
        },
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return reserve;
  } catch (error) {
    console.error("Failed to fetch reserve:", error);
    return null;
  }
}

export async function addReserve(reserveData: {
  propertyId: string;
  name: string;
  description: string;
  targetAmount?: number;
  yearlyContribution?: number;
}): Promise<Reserve> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const property = await prisma.property.findFirst({
      where: {
        id: reserveData.propertyId,
        userId: session.user.id,
      },
    });

    if (!property) {
      throw new Error("Property not found");
    }

    const reserve = await prisma.reserve.create({
      data: {
        userId: session.user.id,
        propertyId: reserveData.propertyId,
        name: reserveData.name,
        description: reserveData.description,
        targetAmount: reserveData.targetAmount || null,
        yearlyContribution: reserveData.yearlyContribution || 0,
      },
      include: {
        transactions: true,
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    revalidatePath("/weg-accounting/dashboard");
    return reserve;
  } catch (error) {
    console.error("Failed to create reserve:", error);
    throw new Error("Failed to create reserve");
  }
}

export async function updateReserve(
  reserveId: string,
  reserveData: {
    name?: string;
    description?: string;
    targetAmount?: number;
    yearlyContribution?: number;
  }
): Promise<Reserve> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const existingReserve = await prisma.reserve.findFirst({
      where: {
        id: reserveId,
        userId: session.user.id,
      },
    });

    if (!existingReserve) {
      throw new Error("Reserve not found");
    }

    const reserve = await prisma.reserve.update({
      where: {
        id: reserveId,
      },
      data: {
        name: reserveData.name || existingReserve.name,
        description: reserveData.description || existingReserve.description,
        targetAmount: reserveData.targetAmount !== undefined ? reserveData.targetAmount : existingReserve.targetAmount,
        yearlyContribution: reserveData.yearlyContribution !== undefined ? reserveData.yearlyContribution : existingReserve.yearlyContribution,
      },
      include: {
        transactions: {
          orderBy: {
            date: "desc",
          },
        },
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    revalidatePath("/weg-accounting/dashboard");
    return reserve;
  } catch (error) {
    console.error("Failed to update reserve:", error);
    throw new Error("Failed to update reserve");
  }
}

export async function deleteReserve(reserveId: string): Promise<void> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const existingReserve = await prisma.reserve.findFirst({
      where: {
        id: reserveId,
        userId: session.user.id,
      },
    });

    if (!existingReserve) {
      throw new Error("Reserve not found");
    }

    await prisma.reserve.delete({
      where: {
        id: reserveId,
      },
    });

    revalidatePath("/weg-accounting/dashboard");
  } catch (error) {
    console.error("Failed to delete reserve:", error);
    throw new Error("Failed to delete reserve");
  }
}

export async function addReserveTransaction(transactionData: {
  reserveId: string;
  date: string;
  type: string;
  amount: number;
  description: string;
  documentId?: string;
}): Promise<ReserveTransaction> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const reserve = await prisma.reserve.findFirst({
      where: {
        id: transactionData.reserveId,
        userId: session.user.id,
      },
    });

    if (!reserve) {
      throw new Error("Reserve not found");
    }

    const transaction = await prisma.reserveTransaction.create({
      data: {
        reserveId: transactionData.reserveId,
        date: new Date(transactionData.date),
        type: transactionData.type,
        amount: transactionData.amount,
        description: transactionData.description,
        documentId: transactionData.documentId || null,
      },
    });

    const balanceChange = transactionData.type === "withdrawal" ? -transactionData.amount : transactionData.amount;
    await prisma.reserve.update({
      where: {
        id: transactionData.reserveId,
      },
      data: {
        balance: {
          increment: balanceChange,
        },
      },
    });

    revalidatePath("/weg-accounting/dashboard");
    return transaction;
  } catch (error) {
    console.error("Failed to create reserve transaction:", error);
    throw new Error("Failed to create reserve transaction");
  }
}
