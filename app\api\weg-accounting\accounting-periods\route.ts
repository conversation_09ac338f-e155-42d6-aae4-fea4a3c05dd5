import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: Request) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const propertyId = searchParams.get('propertyId');

    let whereClause: any = {
      userId: session.user.id,
    };

    if (propertyId) {
      whereClause.propertyId = propertyId;
    }

    const accountingPeriods = await prisma.accountingPeriod.findMany({
      where: whereClause,
      include: {
        property: true,
        expenses: {
          include: {
            category: true,
          },
        },
      },
      orderBy: [
        { year: 'desc' },
        { property: { name: 'asc' } },
      ],
    });

    return NextResponse.json(accountingPeriods);
  } catch (error) {
    console.error("Failed to fetch accounting periods:", error);
    return NextResponse.json({ error: "Failed to fetch accounting periods" }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await request.json();
    
    // Check if property belongs to user
    const property = await prisma.property.findFirst({
      where: {
        id: data.propertyId,
        userId: session.user.id,
      },
    });

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Check if accounting period already exists for this property and year
    const existingPeriod = await prisma.accountingPeriod.findUnique({
      where: {
        userId_propertyId_year: {
          userId: session.user.id,
          propertyId: data.propertyId,
          year: data.year,
        },
      },
    });

    if (existingPeriod) {
      return NextResponse.json({ error: "Accounting period for this year already exists" }, { status: 409 });
    }

    const accountingPeriod = await prisma.accountingPeriod.create({
      data: {
        userId: session.user.id,
        propertyId: data.propertyId,
        year: data.year,
        startDate: new Date(data.startDate),
        endDate: new Date(data.endDate),
        status: data.status || "draft",
        openingBalance: data.openingBalance || 0,
        closingBalance: data.closingBalance || 0,
        maintenanceReserveOpening: data.maintenanceReserveOpening || 0,
        maintenanceReserveClosing: data.maintenanceReserveClosing || 0,
        maintenanceReserveContribution: data.maintenanceReserveContribution || 0,
      },
      include: {
        property: true,
        expenses: {
          include: {
            category: true,
          },
        },
      },
    });
    
    return NextResponse.json(accountingPeriod);
  } catch (error) {
    console.error("Failed to create accounting period:", error);
    return NextResponse.json({ error: "Failed to create accounting period" }, { status: 500 });
  }
}
