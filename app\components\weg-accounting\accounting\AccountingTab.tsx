"use client";

import { useState } from "react";
import { AccountingList, AccountingPeriod, ExpenseCategory } from "./AccountingList";
import { AccountingDetail } from "./AccountingDetail";
import { Property } from "../properties/PropertyList";
import { Owner } from "../owners/OwnerList";

interface AccountingTabProps {
  properties: Property[];
  owners: Owner[];
  expenseCategories?: ExpenseCategory[];
  accountingPeriods?: AccountingPeriod[];
  onRefreshAccountingPeriods?: () => Promise<void>;
}

export function AccountingTab({ properties, owners, expenseCategories: propExpenseCategories, accountingPeriods: propAccountingPeriods, onRefreshAccountingPeriods }: AccountingTabProps) {
  const [selectedAccounting, setSelectedAccounting] = useState<AccountingPeriod | null>(null);

  // Ausgabenkategorien werden jetzt über Props übergeben
  const [localExpenseCategories, setLocalExpenseCategories] = useState<ExpenseCategory[]>([]);

  // Verwende entweder die übergebenen ExpenseCategories oder die lokalen ExpenseCategories
  const expenseCategories = propExpenseCategories || localExpenseCategories;

  // Lokale Abrechnungsperioden (werden durch Props überschrieben wenn verfügbar)
  const [localAccountingPeriods, setLocalAccountingPeriods] = useState<AccountingPeriod[]>([]);

  // Verwende entweder die übergebenen AccountingPeriods oder die lokalen AccountingPeriods
  const accountingPeriods = propAccountingPeriods || localAccountingPeriods;

  // Handler für die Auswahl einer Abrechnung
  const handleSelectAccounting = (accounting: AccountingPeriod) => {
    setSelectedAccounting(accounting);
  };

  // Handler für die Aktualisierung einer Abrechnung
  const handleUpdateAccounting = (updatedAccounting: AccountingPeriod) => {
    setLocalAccountingPeriods(accountingPeriods.map(accounting =>
      accounting.id === updatedAccounting.id ? updatedAccounting : accounting
    ));

    // Wenn die aktuell ausgewählte Abrechnung aktualisiert wurde, aktualisiere auch diese
    if (selectedAccounting && selectedAccounting.id === updatedAccounting.id) {
      setSelectedAccounting(updatedAccounting);
    }
  };

  // Funktion zum Abrufen des Objekts anhand der ID
  const getProperty = (propertyId: string) => {
    return properties.find(property => property.id === propertyId) || properties[0];
  };

  return (
    <div>
      {selectedAccounting ? (
        <AccountingDetail
          accounting={selectedAccounting}
          property={getProperty(selectedAccounting.propertyId)}
          owners={owners}
          expenseCategories={expenseCategories}
          onBack={() => setSelectedAccounting(null)}
          onUpdate={handleUpdateAccounting}
        />
      ) : (
        <AccountingList
          accountingPeriods={accountingPeriods}
          properties={properties}
          expenseCategories={expenseCategories}
          onSelectAccounting={handleSelectAccounting}
          onUpdateAccountings={setLocalAccountingPeriods}
          onRefreshAccountingPeriods={onRefreshAccountingPeriods}
        />
      )}
    </div>
  );
}
