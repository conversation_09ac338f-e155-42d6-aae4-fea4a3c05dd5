import { redirect } from "next/navigation";
import prisma from "./db";

export async function verifyAdvisorToken(token: string) {
  if (!token) {
    redirect("/weg-accounting/login");
  }

  try {
    // Find the UserAdvisor record with this token
    const userAdvisor = await prisma.userAdvisor.findUnique({
      where: {
        accessToken: token,
      },
      include: {
        advisor: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!userAdvisor) {
      redirect("/weg-accounting/login");
    }

    // Check if the token has expired
    if (userAdvisor.expiresAt && new Date() > userAdvisor.expiresAt) {
      redirect("/weg-accounting/login");
    }

    // Check if the advisor is active
    if (!userAdvisor.advisor.isActive) {
      redirect("/weg-accounting/login");
    }

    return userAdvisor;
  } catch (error) {
    console.error("Error verifying advisor token:", error);
    redirect("/weg-accounting/login");
  }
}
