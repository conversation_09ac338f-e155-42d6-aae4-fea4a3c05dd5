"use client";

import { useState, useEffect } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Archive, 
  Clock,
  Search
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/app/context/ToastContext";

// Mock data for projects
const mockProjects = [
  {
    id: "1",
    name: "Client Website",
    description: "Website development for client XYZ",
    color: "#4f46e5",
    isArchived: false,
    totalHours: 270 // 4.5 hours in minutes
  },
  {
    id: "2",
    name: "Internal Project",
    description: "Internal tool development",
    color: "#10b981",
    isArchived: false,
    totalHours: 120 // 2 hours in minutes
  },
  {
    id: "3",
    name: "Marketing Campaign",
    description: "Q2 Marketing Campaign",
    color: "#f59e0b",
    isArchived: false,
    totalHours: 180 // 3 hours in minutes
  },
  {
    id: "4",
    name: "Legacy Project",
    description: "Maintenance of legacy systems",
    color: "#ef4444",
    isArchived: true,
    totalHours: 60 // 1 hour in minutes
  }
];

export function TimeProjectsView() {
  const { language, t } = useLanguage();
  const { toast } = useToast();
  const [projects, setProjects] = useState<any[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [showArchived, setShowArchived] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [projectToEdit, setProjectToEdit] = useState<any | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    color: "#4f46e5"
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Available colors for projects
  const projectColors = [
    { value: "#4f46e5", name: language === "de" ? "Blau" : "Blue" },
    { value: "#10b981", name: language === "de" ? "Grün" : "Green" },
    { value: "#f59e0b", name: language === "de" ? "Orange" : "Orange" },
    { value: "#ef4444", name: language === "de" ? "Rot" : "Red" },
    { value: "#8b5cf6", name: language === "de" ? "Lila" : "Purple" },
    { value: "#ec4899", name: language === "de" ? "Pink" : "Pink" },
    { value: "#06b6d4", name: language === "de" ? "Türkis" : "Teal" },
    { value: "#71717a", name: language === "de" ? "Grau" : "Gray" }
  ];

  // Format duration
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}:${mins.toString().padStart(2, '0')}`;
  };

  // Load data
  useEffect(() => {
    // Simulate loading data from API
    setTimeout(() => {
      setProjects(mockProjects);
    }, 500);
  }, []);

  // Apply filters
  useEffect(() => {
    let filtered = [...projects];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project => 
        project.name.toLowerCase().includes(query) ||
        (project.description && project.description.toLowerCase().includes(query))
      );
    }
    
    // Apply archived filter
    if (!showArchived) {
      filtered = filtered.filter(project => !project.isArchived);
    }
    
    setFilteredProjects(filtered);
  }, [projects, searchQuery, showArchived]);

  // Handle form input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle color selection
  const handleColorChange = (color: string) => {
    setFormData(prev => ({ ...prev, color }));
  };

  // Open add dialog
  const openAddDialog = () => {
    setFormData({
      name: "",
      description: "",
      color: "#4f46e5"
    });
    setIsAddDialogOpen(true);
  };

  // Open edit dialog
  const openEditDialog = (project: any) => {
    setProjectToEdit(project);
    setFormData({
      name: project.name,
      description: project.description || "",
      color: project.color
    });
    setIsEditDialogOpen(true);
  };

  // Add new project
  const handleAddProject = async () => {
    setIsSubmitting(true);
    
    try {
      // Validate form
      if (!formData.name) {
        throw new Error(language === "de" ? "Bitte geben Sie einen Projektnamen ein." : "Please enter a project name.");
      }

      // Create new project
      const newProject = {
        id: (projects.length + 1).toString(),
        name: formData.name,
        description: formData.description,
        color: formData.color,
        isArchived: false,
        totalHours: 0
      };
      
      // Add to projects
      setProjects(prev => [...prev, newProject]);
      
      // Close dialog
      setIsAddDialogOpen(false);
      
      // Show success message
      toast.success(
        language === "de" ? "Projekt erstellt" : "Project created", 
        { description: language === "de" ? "Das Projekt wurde erfolgreich erstellt." : "The project was successfully created." }
      );
    } catch (error) {
      console.error("Error adding project:", error);
      toast.error(
        language === "de" ? "Fehler" : "Error", 
        { description: error instanceof Error ? error.message : "An unknown error occurred" }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update project
  const handleUpdateProject = async () => {
    if (!projectToEdit) return;
    
    setIsSubmitting(true);
    
    try {
      // Validate form
      if (!formData.name) {
        throw new Error(language === "de" ? "Bitte geben Sie einen Projektnamen ein." : "Please enter a project name.");
      }

      // Update project
      setProjects(prev => prev.map(project => 
        project.id === projectToEdit.id 
          ? { 
              ...project, 
              name: formData.name, 
              description: formData.description, 
              color: formData.color 
            } 
          : project
      ));
      
      // Close dialog
      setIsEditDialogOpen(false);
      setProjectToEdit(null);
      
      // Show success message
      toast.success(
        language === "de" ? "Projekt aktualisiert" : "Project updated", 
        { description: language === "de" ? "Das Projekt wurde erfolgreich aktualisiert." : "The project was successfully updated." }
      );
    } catch (error) {
      console.error("Error updating project:", error);
      toast.error(
        language === "de" ? "Fehler" : "Error", 
        { description: error instanceof Error ? error.message : "An unknown error occurred" }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Archive/unarchive project
  const toggleArchiveProject = (projectId: string, currentStatus: boolean) => {
    // Update project
    setProjects(prev => prev.map(project => 
      project.id === projectId 
        ? { ...project, isArchived: !currentStatus } 
        : project
    ));
    
    // Show success message
    toast.success(
      currentStatus 
        ? (language === "de" ? "Projekt wiederhergestellt" : "Project unarchived")
        : (language === "de" ? "Projekt archiviert" : "Project archived"), 
      { description: currentStatus 
          ? (language === "de" ? "Das Projekt wurde erfolgreich wiederhergestellt." : "The project was successfully unarchived.")
          : (language === "de" ? "Das Projekt wurde erfolgreich archiviert." : "The project was successfully archived.")
      }
    );
  };

  // Delete project
  const handleDeleteProject = (projectId: string) => {
    // Confirm deletion
    if (confirm(language === "de" ? "Möchten Sie dieses Projekt wirklich löschen?" : "Are you sure you want to delete this project?")) {
      setProjects(prev => prev.filter(project => project.id !== projectId));
      
      toast.success(
        language === "de" ? "Projekt gelöscht" : "Project deleted", 
        { description: language === "de" ? "Das Projekt wurde erfolgreich gelöscht." : "The project was successfully deleted." }
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Projects Header */}
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div className="relative w-full md:w-auto md:flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder={language === "de" ? "Projekte suchen..." : "Search projects..."}
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="flex items-center gap-4 w-full md:w-auto">
          <div className="flex items-center space-x-2">
            <Switch
              id="show-archived"
              checked={showArchived}
              onCheckedChange={setShowArchived}
            />
            <Label htmlFor="show-archived">
              {language === "de" ? "Archivierte anzeigen" : "Show archived"}
            </Label>
          </div>
          
          <Button 
            onClick={openAddDialog}
            className="flex items-center gap-2 ml-auto"
          >
            <Plus className="h-4 w-4" />
            <span>{language === "de" ? "Neues Projekt" : "New Project"}</span>
          </Button>
        </div>
      </div>

      {/* Projects List */}
      {filteredProjects.length > 0 ? (
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[30%]">
                  {language === "de" ? "Projektname" : "Project Name"}
                </TableHead>
                <TableHead className="w-[40%]">
                  {language === "de" ? "Beschreibung" : "Description"}
                </TableHead>
                <TableHead className="w-[15%]">
                  {language === "de" ? "Gesamtzeit" : "Total Time"}
                </TableHead>
                <TableHead className="w-[15%] text-right">
                  {language === "de" ? "Aktionen" : "Actions"}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProjects.map(project => (
                <TableRow key={project.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center">
                      <div 
                        className="w-4 h-4 rounded-full mr-2" 
                        style={{ backgroundColor: project.color }}
                      />
                      <span>{project.name}</span>
                      {project.isArchived && (
                        <Badge variant="outline" className="ml-2">
                          {language === "de" ? "Archiviert" : "Archived"}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {project.description || (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                      {formatDuration(project.totalHours)}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditDialog(project)}>
                          <Edit className="h-4 w-4 mr-2" />
                          {language === "de" ? "Bearbeiten" : "Edit"}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => toggleArchiveProject(project.id, project.isArchived)}>
                          <Archive className="h-4 w-4 mr-2" />
                          {project.isArchived 
                            ? (language === "de" ? "Wiederherstellen" : "Unarchive")
                            : (language === "de" ? "Archivieren" : "Archive")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteProject(project.id)}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          {language === "de" ? "Löschen" : "Delete"}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      ) : (
        <div className="text-center py-10">
          <Clock className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">
            {language === "de" ? "Keine Projekte gefunden" : "No projects found"}
          </h3>
          <p className="text-muted-foreground mt-2">
            {searchQuery
              ? (language === "de" ? "Versuchen Sie, Ihre Suche anzupassen." : "Try adjusting your search.")
              : (language === "de" ? "Erstellen Sie ein neues Projekt, um mit der Zeiterfassung zu beginnen." : "Create a new project to start tracking time.")}
          </p>
          {!searchQuery && (
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={openAddDialog}
            >
              <Plus className="h-4 w-4 mr-2" />
              {language === "de" ? "Neues Projekt" : "New Project"}
            </Button>
          )}
        </div>
      )}

      {/* Add Project Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {language === "de" ? "Neues Projekt" : "New Project"}
            </DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            {/* Project Name */}
            <div className="grid gap-2">
              <Label htmlFor="name">
                {language === "de" ? "Projektname" : "Project Name"}
                <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={language === "de" ? "Projektname eingeben" : "Enter project name"}
              />
            </div>

            {/* Project Description */}
            <div className="grid gap-2">
              <Label htmlFor="description">
                {language === "de" ? "Beschreibung" : "Description"}
              </Label>
              <Input
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder={language === "de" ? "Projektbeschreibung eingeben" : "Enter project description"}
              />
            </div>

            {/* Project Color */}
            <div className="grid gap-2">
              <Label>
                {language === "de" ? "Farbe" : "Color"}
              </Label>
              <div className="flex flex-wrap gap-2">
                {projectColors.map(color => (
                  <button
                    key={color.value}
                    type="button"
                    className={`w-8 h-8 rounded-full border-2 ${formData.color === color.value ? 'border-primary' : 'border-transparent'}`}
                    style={{ backgroundColor: color.value }}
                    onClick={() => handleColorChange(color.value)}
                    title={color.name}
                  />
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)} disabled={isSubmitting}>
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button onClick={handleAddProject} disabled={isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-transparent rounded-full"></div>
                  {language === "de" ? "Speichern..." : "Saving..."}
                </div>
              ) : (
                language === "de" ? "Speichern" : "Save"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Project Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {language === "de" ? "Projekt bearbeiten" : "Edit Project"}
            </DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            {/* Project Name */}
            <div className="grid gap-2">
              <Label htmlFor="edit-name">
                {language === "de" ? "Projektname" : "Project Name"}
                <span className="text-red-500 ml-1">*</span>
              </Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={language === "de" ? "Projektname eingeben" : "Enter project name"}
              />
            </div>

            {/* Project Description */}
            <div className="grid gap-2">
              <Label htmlFor="edit-description">
                {language === "de" ? "Beschreibung" : "Description"}
              </Label>
              <Input
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder={language === "de" ? "Projektbeschreibung eingeben" : "Enter project description"}
              />
            </div>

            {/* Project Color */}
            <div className="grid gap-2">
              <Label>
                {language === "de" ? "Farbe" : "Color"}
              </Label>
              <div className="flex flex-wrap gap-2">
                {projectColors.map(color => (
                  <button
                    key={color.value}
                    type="button"
                    className={`w-8 h-8 rounded-full border-2 ${formData.color === color.value ? 'border-primary' : 'border-transparent'}`}
                    style={{ backgroundColor: color.value }}
                    onClick={() => handleColorChange(color.value)}
                    title={color.name}
                  />
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} disabled={isSubmitting}>
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button onClick={handleUpdateProject} disabled={isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-transparent rounded-full"></div>
                  {language === "de" ? "Speichern..." : "Saving..."}
                </div>
              ) : (
                language === "de" ? "Speichern" : "Save"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
