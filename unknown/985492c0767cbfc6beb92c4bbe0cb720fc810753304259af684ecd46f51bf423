"use client";

import { useState, useEffect } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Advisor } from "@/app/lib/wegTypes";
import { updateAdvisor, getAdvisorPermissions } from "@/app/actions/advisors";

interface AdvisorPermissionsProps {
  advisors: Advisor[];
  onUpdateAdvisors: (advisors: Advisor[]) => void;
}

export function AdvisorPermissions({ advisors, onUpdateAdvisors }: AdvisorPermissionsProps) {
  const { language } = useLanguage();
  const [permissions, setPermissions] = useState<Record<string, {
    canViewProperties: boolean;
    canViewOwners: boolean;
    canViewAccounting: boolean;
    canViewInvoices: boolean;
    canViewReports: boolean;
    canDownloadDocuments: boolean;
  }>>({});

  // Initialisiere die Berechtigungen für jeden Berater
  useEffect(() => {
    const loadPermissions = async () => {
      const initialPermissions: Record<string, any> = {};

      for (const advisor of advisors) {
        try {
          // Lade die aktuellen Berechtigungen aus der Datenbank
          const userAdvisor = await getAdvisorPermissions(advisor.id);

          if (userAdvisor) {
            initialPermissions[advisor.id] = {
              canViewProperties: userAdvisor.canViewProperties || false,
              canViewOwners: userAdvisor.canViewOwners || false,
              canViewAccounting: userAdvisor.canViewAccounting || false,
              canViewInvoices: userAdvisor.canViewInvoices || false,
              canViewReports: userAdvisor.canViewReports || false,
              canDownloadDocuments: userAdvisor.canDownload || false,
            };
          } else {
            // Fallback zu den Advisor-Permissions
            initialPermissions[advisor.id] = {
              canViewProperties: advisor.permissions?.canViewProperties || false,
              canViewOwners: advisor.permissions?.canViewOwners || false,
              canViewAccounting: advisor.permissions?.canViewAccounting || false,
              canViewInvoices: advisor.permissions?.canViewInvoices || false,
              canViewReports: advisor.permissions?.canViewReports || false,
              canDownloadDocuments: advisor.permissions?.canDownloadDocuments || false,
            };
          }
        } catch (error) {
          console.error(`Failed to load permissions for advisor ${advisor.id}:`, error);
          // Fallback zu den Advisor-Permissions
          initialPermissions[advisor.id] = {
            canViewProperties: advisor.permissions?.canViewProperties || false,
            canViewOwners: advisor.permissions?.canViewOwners || false,
            canViewAccounting: advisor.permissions?.canViewAccounting || false,
            canViewInvoices: advisor.permissions?.canViewInvoices || false,
            canViewReports: advisor.permissions?.canViewReports || false,
            canDownloadDocuments: advisor.permissions?.canDownloadDocuments || false,
          };
        }
      }

      setPermissions(initialPermissions);
    };

    if (advisors.length > 0) {
      loadPermissions();
    }
  }, [advisors]);

  // Handler für das Ändern einer Berechtigung
  const handlePermissionChange = async (advisorId: string, permission: string, value: boolean) => {
    try {
      // Optimistische UI-Aktualisierung
      setPermissions(prev => ({
        ...prev,
        [advisorId]: {
          ...prev[advisorId],
          [permission]: value
        }
      }));

      const currentPermissions = permissions[advisorId] || {};
      let permissionParams: any = {};

      // Erstelle die Parameter für das Backend-Update
      if (
        permission === 'canViewProperties' ||
        permission === 'canViewOwners' ||
        permission === 'canViewAccounting' ||
        permission === 'canViewReports'
      ) {
        const updatedPermissions = {
          ...currentPermissions,
          [permission]: value
        };
        const anyWEGPermissionActive =
          updatedPermissions.canViewProperties ||
          updatedPermissions.canViewOwners ||
          updatedPermissions.canViewAccounting ||
          updatedPermissions.canViewReports;

        permissionParams = {
          canViewProperties: updatedPermissions.canViewProperties,
          canViewOwners: updatedPermissions.canViewOwners,
          canViewAccounting: updatedPermissions.canViewAccounting,
          canViewReports: updatedPermissions.canViewReports,
          canViewReserves: updatedPermissions.canViewAccounting, // Reserves folgen der Accounting-Berechtigung
          canViewWEG: anyWEGPermissionActive
        };
      } else if (permission === 'canViewInvoices') {
        permissionParams = { canViewInvoices: value };
      } else if (permission === 'canDownloadDocuments') {
        permissionParams = { canDownload: value };
      }

      console.log(`Updating permissions for advisor ${advisorId}:`, permissionParams);

      if (Object.keys(permissionParams).length > 0) {
        await updateAdvisor(advisorId, permissionParams);
      }

      // Lade die aktualisierten Berechtigungen nach dem Update
      const updatedUserAdvisor = await getAdvisorPermissions(advisorId);

      const updatedAdvisors = advisors.map(advisor => {
        if (advisor.id === advisorId) {
          const updatedPermissions = { ...advisor.permissions };

          if (updatedUserAdvisor) {
            // Aktualisiere die Permissions basierend auf den UserAdvisor-Daten
            updatedPermissions.canViewProperties = updatedUserAdvisor.canViewProperties;
            updatedPermissions.canViewOwners = updatedUserAdvisor.canViewOwners;
            updatedPermissions.canViewAccounting = updatedUserAdvisor.canViewAccounting;
            updatedPermissions.canViewInvoices = updatedUserAdvisor.canViewInvoices;
            updatedPermissions.canViewReports = updatedUserAdvisor.canViewReports;
            updatedPermissions.canDownloadDocuments = updatedUserAdvisor.canDownload;
          } else {
            // Fallback: Aktualisiere nur die geänderte Berechtigung
            (updatedPermissions as any)[permission] = value;
          }

          return {
            ...advisor,
            permissions: updatedPermissions
          };
        }
        return advisor;
      });

      onUpdateAdvisors(updatedAdvisors);

      // Aktualisiere auch den lokalen Permissions-State
      if (updatedUserAdvisor) {
        setPermissions(prev => ({
          ...prev,
          [advisorId]: {
            canViewProperties: updatedUserAdvisor.canViewProperties,
            canViewOwners: updatedUserAdvisor.canViewOwners,
            canViewAccounting: updatedUserAdvisor.canViewAccounting,
            canViewInvoices: updatedUserAdvisor.canViewInvoices,
            canViewReports: updatedUserAdvisor.canViewReports,
            canDownloadDocuments: updatedUserAdvisor.canDownload,
          }
        }));
      }

      toast.success(
        language === "de"
          ? "Berechtigungen aktualisiert"
          : "Permissions updated"
      );
    } catch (error) {
      console.error("Failed to update permissions:", error);

      // Rollback der optimistischen UI-Aktualisierung
      setPermissions(prev => ({
        ...prev,
        [advisorId]: {
          ...prev[advisorId],
          [permission]: !value // Zurück zum vorherigen Wert
        }
      }));

      toast.error(
        language === "de"
          ? "Fehler beim Aktualisieren der Berechtigungen"
          : "Failed to update permissions"
      );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {language === "de" ? "Berechtigungen verwalten" : "Manage Permissions"}
        </CardTitle>
        <CardDescription>
          {language === "de"
            ? "Legen Sie fest, welche Bereiche Ihre Steuerberater einsehen können"
            : "Define which areas your tax advisors can view"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {advisors.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              {language === "de"
                ? "Keine Berater vorhanden. Fügen Sie einen Berater hinzu, um Berechtigungen zu verwalten."
                : "No advisors available. Add an advisor to manage permissions."}
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "de" ? "Berater" : "Advisor"}</TableHead>
                <TableHead>{language === "de" ? "Immobilien" : "Properties"}</TableHead>
                <TableHead>{language === "de" ? "Eigentümer" : "Owners"}</TableHead>
                <TableHead>{language === "de" ? "Abrechnung" : "Accounting"}</TableHead>
                <TableHead>{language === "de" ? "Rechnungen" : "Invoices"}</TableHead>
                <TableHead>{language === "de" ? "Berichte" : "Reports"}</TableHead>
                <TableHead>{language === "de" ? "Dokumente" : "Documents"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {advisors.map((advisor) => (
                <TableRow key={advisor.id}>
                  <TableCell className="font-medium">{advisor.name}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`${advisor.id}-properties`}
                        checked={permissions[advisor.id]?.canViewProperties}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(advisor.id, "canViewProperties", checked)
                        }
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`${advisor.id}-owners`}
                        checked={permissions[advisor.id]?.canViewOwners}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(advisor.id, "canViewOwners", checked)
                        }
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`${advisor.id}-accounting`}
                        checked={permissions[advisor.id]?.canViewAccounting}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(advisor.id, "canViewAccounting", checked)
                        }
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`${advisor.id}-invoices`}
                        checked={permissions[advisor.id]?.canViewInvoices}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(advisor.id, "canViewInvoices", checked)
                        }
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`${advisor.id}-reports`}
                        checked={permissions[advisor.id]?.canViewReports}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(advisor.id, "canViewReports", checked)
                        }
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`${advisor.id}-documents`}
                        checked={permissions[advisor.id]?.canDownloadDocuments}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(advisor.id, "canDownloadDocuments", checked)
                        }
                      />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
