{"name": "tax-mate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "set NODE_ENV=production && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@conform-to/react": "^1.6.0", "@conform-to/zod": "^1.6.0", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.8", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "glob": "^11.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "mailtrap": "^4.1.0", "next": "15.3.2", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "nodemailer": "^6.9.16", "react": "19.1.0", "react-day-picker": "^9.7.0", "react-dom": "19.1.0", "recharts": "^2.15.3", "rimraf": "^6.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.36"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^22.15.24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "postcss": "^8.5.4", "prisma": "^6.8.2", "tailwindcss": "^4.1.8", "typescript": "^5"}}