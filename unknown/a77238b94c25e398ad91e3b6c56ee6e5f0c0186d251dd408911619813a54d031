"use client";

import { useState } from "react";
import { OwnerList, Owner } from "./OwnerList";
import { OwnerDetail } from "./OwnerDetail";
import { Property } from "../properties/PropertyList";

interface OwnersTabProps {
  properties?: Property[];
  owners?: Owner[];
  initialProperties?: Property[];
  initialOwners?: Owner[];
  onUpdateOwners?: (owners: Owner[]) => void;
}

export function OwnersTab({
  properties: propProperties,
  owners: propOwners,
  initialProperties,
  initialOwners,
  onUpdateOwners
}: OwnersTabProps) {
  const [selectedOwner, setSelectedOwner] = useState<Owner | null>(null);
  // Start with empty array instead of example data
  const [localOwners, setLocalOwners] = useState<Owner[]>(initialOwners || []);

  const [localProperties, setLocalProperties] = useState<Property[]>(initialProperties || []);

  // Verwende entweder die übergebenen Owners oder die lokalen Owners
  const owners = propOwners || localOwners;

  // Verwende entweder die übergebenen Properties oder die lokalen Properties
  const properties = propProperties || localProperties;

  // Funktion zum Aktualisieren der Owners
  const updateOwners = (newOwners: Owner[]) => {
    if (onUpdateOwners) {
      onUpdateOwners(newOwners);
    } else {
      setLocalOwners(newOwners);
    }
  };

  // Handler für die Auswahl eines Eigentümers
  const handleSelectOwner = (owner: Owner) => {
    setSelectedOwner(owner);
  };

  // Handler für die Aktualisierung eines Eigentümers
  const handleUpdateOwner = (updatedOwner: Owner) => {
    const newOwners = owners.map(owner =>
      owner.id === updatedOwner.id ? updatedOwner : owner
    );

    updateOwners(newOwners);

    // Wenn der aktuell ausgewählte Eigentümer aktualisiert wurde, aktualisiere auch diesen
    if (selectedOwner && selectedOwner.id === updatedOwner.id) {
      setSelectedOwner(updatedOwner);
    }
  };

  return (
    <div>
      {selectedOwner ? (
        <OwnerDetail
          owner={selectedOwner}
          properties={properties}
          onBack={() => setSelectedOwner(null)}
          onUpdate={handleUpdateOwner}
        />
      ) : (
        <OwnerList
          owners={owners}
          properties={properties}
          onSelectOwner={handleSelectOwner}
          onUpdateOwners={updateOwners}
        />
      )}
    </div>
  );
}
