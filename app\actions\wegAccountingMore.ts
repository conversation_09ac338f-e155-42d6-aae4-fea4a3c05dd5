"use server";

import { revalidatePath } from "next/cache";
import { requireUser } from "@/app/utils/hooks";
import { AccountingPeriod, Expense, ExpenseCategory } from "../lib/wegTypes";
import prisma from "@/app/utils/db";

// ExpenseCategory-Funktionen
export async function getExpenseCategories(): Promise<ExpenseCategory[]> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    // Ausgabenkategorien aus der Datenbank laden
    const categories = await prisma.expenseCategory.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        name: "asc",
      },
    });

    // Transform to match the expected ExpenseCategory interface
    return categories.map((category: any) => ({
      id: category.id,
      name: category.name,
      isAllocatable: category.isAllocatable,
      isHouseholdRelated: category.isHouseholdRelated,
      isCraftsman: category.isCraftsman,
    }));
  } catch (error) {
    console.error("Failed to fetch expense categories:", error);
    // Falls die Datenbank noch nicht bereit ist, leeres Array zurückgeben
    return [];
  }
}

export async function addExpenseCategory(categoryData: Omit<ExpenseCategory, "id">): Promise<ExpenseCategory> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const newCategory = await prisma.expenseCategory.create({
      data: {
        userId: session.user.id,
        name: categoryData.name,
        isAllocatable: categoryData.isAllocatable,
        isHouseholdRelated: categoryData.isHouseholdRelated,
        isCraftsman: categoryData.isCraftsman,
      },
    });

    revalidatePath("/weg-accounting/dashboard/accounting");

    return {
      id: newCategory.id,
      name: newCategory.name,
      isAllocatable: newCategory.isAllocatable,
      isHouseholdRelated: newCategory.isHouseholdRelated,
      isCraftsman: newCategory.isCraftsman,
    };
  } catch (error) {
    console.error("Failed to add expense category:", error);
    throw new Error("Failed to add expense category");
  }
}

export async function updateExpenseCategory(categoryId: string, categoryData: Partial<ExpenseCategory>): Promise<ExpenseCategory> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const updatedCategory = await prisma.expenseCategory.update({
      where: {
        id: categoryId,
        userId: session.user.id, // Ensure user can only update their own categories
      },
      data: {
        name: categoryData.name,
        isAllocatable: categoryData.isAllocatable,
        isHouseholdRelated: categoryData.isHouseholdRelated,
        isCraftsman: categoryData.isCraftsman,
      },
    });

    revalidatePath("/weg-accounting/dashboard/accounting");

    return {
      id: updatedCategory.id,
      name: updatedCategory.name,
      isAllocatable: updatedCategory.isAllocatable,
      isHouseholdRelated: updatedCategory.isHouseholdRelated,
      isCraftsman: updatedCategory.isCraftsman,
    };
  } catch (error) {
    console.error("Failed to update expense category:", error);
    throw new Error("Failed to update expense category");
  }
}

export async function deleteExpenseCategory(categoryId: string): Promise<void> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    await prisma.expenseCategory.delete({
      where: {
        id: categoryId,
        userId: session.user.id, // Ensure user can only delete their own categories
      },
    });

    revalidatePath("/weg-accounting/dashboard/accounting");
  } catch (error) {
    console.error("Failed to delete expense category:", error);
    throw new Error("Failed to delete expense category");
  }
}

// AccountingPeriod-Funktionen
export async function getAccountingPeriods(): Promise<AccountingPeriod[]> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const accountingPeriods = await prisma.accountingPeriod.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        property: {
          select: {
            id: true,
            name: true,
          },
        },
        expenses: {
          include: {
            category: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            date: "desc",
          },
        },
      },
      orderBy: [
        {
          year: "desc",
        },
        {
          startDate: "desc",
        },
      ],
    });

    return accountingPeriods.map((period) => ({
      id: period.id,
      propertyId: period.propertyId,
      year: period.year,
      startDate: period.startDate.toISOString().split("T")[0],
      endDate: period.endDate.toISOString().split("T")[0],
      status: period.status as "draft" | "completed" | "approved",
      expenses: period.expenses.map((expense) => ({
        id: expense.id,
        accountingId: expense.accountingPeriodId,
        categoryId: expense.categoryId,
        description: expense.description,
        amount: expense.amount,
        date: expense.date.toISOString().split("T")[0],
        distributionKeyId: expense.distributionKeyId || "",
        householdRelatedAmount: expense.householdRelatedAmount,
        craftsmanAmount: expense.craftsmanAmount,
      })),
      openingBalance: period.openingBalance,
      closingBalance: period.closingBalance,
      maintenanceReserveOpening: period.maintenanceReserveOpening,
      maintenanceReserveClosing: period.maintenanceReserveClosing,
      maintenanceReserveContribution: period.maintenanceReserveContribution,
    }));
  } catch (error) {
    console.error("Failed to fetch accounting periods:", error);
    return [];
  }
}

export async function addAccountingPeriod(accountingData: Omit<AccountingPeriod, "id" | "expenses">): Promise<AccountingPeriod> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const existingPeriod = await prisma.accountingPeriod.findFirst({
      where: {
        userId: session.user.id,
        propertyId: accountingData.propertyId,
        year: accountingData.year,
      },
    });

    if (existingPeriod) {
      throw new Error("Accounting period for this year already exists");
    }

    const accountingPeriod = await prisma.accountingPeriod.create({
      data: {
        userId: session.user.id,
        propertyId: accountingData.propertyId,
        year: accountingData.year,
        startDate: new Date(accountingData.startDate),
        endDate: new Date(accountingData.endDate),
        status: accountingData.status || "draft",
        openingBalance: accountingData.openingBalance || 0,
        closingBalance: accountingData.closingBalance || 0,
        maintenanceReserveOpening: accountingData.maintenanceReserveOpening || 0,
        maintenanceReserveClosing: accountingData.maintenanceReserveClosing || 0,
        maintenanceReserveContribution: accountingData.maintenanceReserveContribution || 0,
      },
      include: {
        property: {
          select: {
            id: true,
            name: true,
          },
        },
        expenses: {
          include: {
            category: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    const result: AccountingPeriod = {
      id: accountingPeriod.id,
      propertyId: accountingPeriod.propertyId,
      year: accountingPeriod.year,
      startDate: accountingPeriod.startDate.toISOString().split("T")[0],
      endDate: accountingPeriod.endDate.toISOString().split("T")[0],
      status: accountingPeriod.status as "draft" | "completed" | "approved",
      expenses: [],
      openingBalance: accountingPeriod.openingBalance,
      closingBalance: accountingPeriod.closingBalance,
      maintenanceReserveOpening: accountingPeriod.maintenanceReserveOpening,
      maintenanceReserveClosing: accountingPeriod.maintenanceReserveClosing,
      maintenanceReserveContribution: accountingPeriod.maintenanceReserveContribution,
    };

    revalidatePath("/weg-accounting/dashboard/accounting");
    return result;
  } catch (error) {
    console.error("Failed to create accounting period:", error);
    throw new Error("Failed to create accounting period");
  }
}

export async function updateAccountingPeriod(accountingId: string, accountingData: Partial<AccountingPeriod>): Promise<AccountingPeriod> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const existingPeriod = await prisma.accountingPeriod.findFirst({
      where: {
        id: accountingId,
        userId: session.user.id,
      },
    });

    if (!existingPeriod) {
      throw new Error("Accounting period not found");
    }

    const updateData: any = {};
    if (accountingData.year !== undefined) updateData.year = accountingData.year;
    if (accountingData.startDate !== undefined) updateData.startDate = new Date(accountingData.startDate);
    if (accountingData.endDate !== undefined) updateData.endDate = new Date(accountingData.endDate);
    if (accountingData.status !== undefined) updateData.status = accountingData.status;
    if (accountingData.openingBalance !== undefined) updateData.openingBalance = accountingData.openingBalance;
    if (accountingData.closingBalance !== undefined) updateData.closingBalance = accountingData.closingBalance;
    if (accountingData.maintenanceReserveOpening !== undefined) updateData.maintenanceReserveOpening = accountingData.maintenanceReserveOpening;
    if (accountingData.maintenanceReserveClosing !== undefined) updateData.maintenanceReserveClosing = accountingData.maintenanceReserveClosing;
    if (accountingData.maintenanceReserveContribution !== undefined) updateData.maintenanceReserveContribution = accountingData.maintenanceReserveContribution;

    const updatedPeriod = await prisma.accountingPeriod.update({
      where: {
        id: accountingId,
      },
      data: updateData,
      include: {
        property: {
          select: {
            id: true,
            name: true,
          },
        },
        expenses: {
          include: {
            category: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            date: "desc",
          },
        },
      },
    });

    const result: AccountingPeriod = {
      id: updatedPeriod.id,
      propertyId: updatedPeriod.propertyId,
      year: updatedPeriod.year,
      startDate: updatedPeriod.startDate.toISOString().split("T")[0],
      endDate: updatedPeriod.endDate.toISOString().split("T")[0],
      status: updatedPeriod.status as "draft" | "completed" | "approved",
      expenses: updatedPeriod.expenses.map((expense) => ({
        id: expense.id,
        accountingId: expense.accountingPeriodId,
        categoryId: expense.categoryId,
        description: expense.description,
        amount: expense.amount,
        date: expense.date.toISOString().split("T")[0],
        distributionKeyId: expense.distributionKeyId || "",
        householdRelatedAmount: expense.householdRelatedAmount,
        craftsmanAmount: expense.craftsmanAmount,
      })),
      openingBalance: updatedPeriod.openingBalance,
      closingBalance: updatedPeriod.closingBalance,
      maintenanceReserveOpening: updatedPeriod.maintenanceReserveOpening,
      maintenanceReserveClosing: updatedPeriod.maintenanceReserveClosing,
      maintenanceReserveContribution: updatedPeriod.maintenanceReserveContribution,
    };

    revalidatePath("/weg-accounting/dashboard/accounting");
    return result;
  } catch (error) {
    console.error("Failed to update accounting period:", error);
    throw new Error("Failed to update accounting period");
  }
}

export async function deleteAccountingPeriod(accountingId: string): Promise<void> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const existingPeriod = await prisma.accountingPeriod.findFirst({
      where: {
        id: accountingId,
        userId: session.user.id,
      },
    });

    if (!existingPeriod) {
      throw new Error("Accounting period not found");
    }

    await prisma.accountingPeriod.delete({
      where: {
        id: accountingId,
      },
    });

    revalidatePath("/weg-accounting/dashboard/accounting");
  } catch (error) {
    console.error("Failed to delete accounting period:", error);
    throw new Error("Failed to delete accounting period");
  }
}
