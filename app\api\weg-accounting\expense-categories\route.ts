import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import prisma from "@/app/utils/db";

// Default expense categories that will be created for new users
const defaultExpenseCategories = [
  {
    name: "<PERSON><PERSON><PERSON>kos<PERSON>",
    isAllocatable: true,
    isHouseholdRelated: true,
    isCraftsman: false,
  },
  {
    name: "Verwaltungskosten",
    isAllocatable: true,
    isHouseholdRelated: false,
    isCraftsman: false,
  },
  {
    name: "Versicherungen",
    isAllocatable: true,
    isHouseholdRelated: false,
    isCraftsman: false,
  },
  {
    name: "<PERSON>ara<PERSON><PERSON>",
    isAllocatable: true,
    isHouseholdRelated: false,
    isCraftsman: true,
  },
  {
    name: "Reinigung",
    isAllocatable: true,
    isHouseholdRelated: true,
    isCraftsman: false,
  },
  {
    name: "Heizung",
    isAllocatable: true,
    isHouseholdRelated: false,
    isCraftsman: false,
  },
  {
    name: "<PERSON><PERSON>",
    isAllocatable: true,
    isHouseholdRelated: false,
    isCraftsman: false,
  },
  {
    name: "<PERSON><PERSON>",
    isAllocatable: true,
    isHouseholdRelated: false,
    is<PERSON><PERSON><PERSON>: false,
  },
  {
    name: "<PERSON><PERSON><PERSON>lage",
    isAllocatable: false,
    isHouseholdRelated: false,
    isCraftsman: false,
  },
];

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

    // Get existing expense categories for the user
    let expenseCategories = await prisma.expenseCategory.findMany({
      where: {
        userId: userId,
      },
      orderBy: {
        name: 'asc',
      },
    });

    // If no categories exist, create default ones
    if (expenseCategories.length === 0) {
      const createdCategories = await prisma.$transaction(
        defaultExpenseCategories.map((category) =>
          prisma.expenseCategory.create({
            data: {
              ...category,
              userId: userId,
            },
          })
        )
      );
      expenseCategories = createdCategories;
    }

    return NextResponse.json(expenseCategories);
  } catch (error) {
    console.error("Failed to fetch expense categories:", error);
    return NextResponse.json({ error: "Failed to fetch expense categories" }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id; // Extract to ensure TypeScript knows it's defined
    const data = await request.json();

    const expenseCategory = await prisma.expenseCategory.create({
      data: {
        name: data.name,
        isAllocatable: data.isAllocatable ?? true,
        isHouseholdRelated: data.isHouseholdRelated ?? false,
        isCraftsman: data.isCraftsman ?? false,
        userId: userId,
      },
    });

    return NextResponse.json(expenseCategory);
  } catch (error) {
    console.error("Failed to create expense category:", error);
    return NextResponse.json({ error: "Failed to create expense category" }, { status: 500 });
  }
}
