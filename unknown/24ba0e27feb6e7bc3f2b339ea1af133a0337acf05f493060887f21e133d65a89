"use client";

import { useState } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Info } from "lucide-react";

export function PersonalInfoStep() {
  const { language } = useLanguage();

  // Beispieldaten für einen Benutzer
  const [formData, setFormData] = useState({
    firstName: "Max",
    lastName: "Mustermann",
    svnr: "1234 010190",
    address: "Musterstraße 1, 1010 Wien",
    maritalStatus: "single",
    children: "2",
    hasDisability: false,
    disabilityPercentage: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Persönliche Daten */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">
            {language === "de" ? "Persönliche Daten" : "Personal Data"}
          </h3>

          <div className="space-y-2">
            <Label htmlFor="firstName">
              {language === "de" ? "Vorname" : "First Name"}
            </Label>
            <Input
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName">
              {language === "de" ? "Nachname" : "Last Name"}
            </Label>
            <Input
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="svnr">
              {language === "de" ? "Sozialversicherungsnummer" : "Social Security Number"}
            </Label>
            <Input
              id="svnr"
              name="svnr"
              value={formData.svnr}
              onChange={handleInputChange}
            />
            <p className="text-xs text-muted-foreground">
              {language === "de"
                ? "Format: XXXX TTMMJJ (z.B. 1234 010190)"
                : "Format: XXXX DDMMYY (e.g. 1234 010190)"}
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">
              {language === "de" ? "Adresse" : "Address"}
            </Label>
            <Input
              id="address"
              name="address"
              value={formData.address}
              onChange={handleInputChange}
            />
          </div>
        </div>

        {/* Familienstatus */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">
            {language === "de" ? "Familienstatus" : "Family Status"}
          </h3>

          <div className="space-y-2">
            <Label>
              {language === "de" ? "Familienstand" : "Marital Status"}
            </Label>
            <RadioGroup
              value={formData.maritalStatus}
              onValueChange={(value) => handleSelectChange("maritalStatus", value)}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="single" id="single" />
                <Label htmlFor="single" className="cursor-pointer">
                  {language === "de" ? "Ledig" : "Single"}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="married" id="married" />
                <Label htmlFor="married" className="cursor-pointer">
                  {language === "de" ? "Verheiratet" : "Married"}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="divorced" id="divorced" />
                <Label htmlFor="divorced" className="cursor-pointer">
                  {language === "de" ? "Geschieden" : "Divorced"}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="widowed" id="widowed" />
                <Label htmlFor="widowed" className="cursor-pointer">
                  {language === "de" ? "Verwitwet" : "Widowed"}
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label htmlFor="children">
              {language === "de" ? "Anzahl der Kinder" : "Number of Children"}
            </Label>
            <Select
              value={formData.children}
              onValueChange={(value) => handleSelectChange("children", value)}
            >
              <SelectTrigger id="children">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">0</SelectItem>
                <SelectItem value="1">1</SelectItem>
                <SelectItem value="2">2</SelectItem>
                <SelectItem value="3">3</SelectItem>
                <SelectItem value="4">4</SelectItem>
                <SelectItem value="5+">5+</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {language === "de"
                ? "Relevant für den Familienbonus Plus"
                : "Relevant for the Family Bonus Plus"}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasDisability"
                checked={formData.hasDisability}
                onCheckedChange={(checked) =>
                  handleCheckboxChange("hasDisability", checked as boolean)
                }
              />
              <Label htmlFor="hasDisability" className="cursor-pointer">
                {language === "de" ? "Behinderung" : "Disability"}
              </Label>
            </div>

            {formData.hasDisability && (
              <div className="pl-6 pt-2 space-y-2">
                <Label htmlFor="disabilityPercentage">
                  {language === "de" ? "Grad der Behinderung (%)" : "Degree of Disability (%)"}
                </Label>
                <Input
                  id="disabilityPercentage"
                  name="disabilityPercentage"
                  value={formData.disabilityPercentage}
                  onChange={handleInputChange}
                  placeholder="z.B. 50"
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* FinanzOnline Hinweis */}
      <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-amber-800 dark:text-amber-300">
              {language === "de" ? "Wo finde ich das in FinanzOnline?" : "Where do I find this in FinanzOnline?"}
            </h4>
            <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
              {language === "de"
                ? "Diese Informationen werden in FinanzOnline unter 'Persönliche Daten' und 'Familiensituation' eingetragen. Die persönlichen Daten sind meist bereits vorausgefüllt, sollten aber auf Richtigkeit überprüft werden."
                : "This information is entered in FinanzOnline under 'Personal Data' and 'Family Situation'. The personal data is usually pre-filled, but should be checked for accuracy."}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
