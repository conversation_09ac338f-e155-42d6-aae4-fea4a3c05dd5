"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Trash2 } from "lucide-react";
import { Property, DistributionKey } from "../properties/PropertyList";
import { AccountingPeriod, Expense, ExpenseCategory } from "./AccountingList";

interface ExpenseListProps {
  accounting: AccountingPeriod;
  property: Property;
  expenseCategories: ExpenseCategory[];
  onUpdate: (accounting: AccountingPeriod) => void;
}

export function ExpenseList({ accounting, property, expenseCategories, onUpdate }: ExpenseListProps) {
  const { language } = useLanguage();
  
  // State für das Hinzufügen einer neuen Ausgabe
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newExpense, setNewExpense] = useState<Omit<Expense, "id">>({
    accountingId: accounting.id,
    categoryId: expenseCategories.length > 0 ? expenseCategories[0].id : "",
    description: "",
    amount: 0,
    date: new Date().toISOString().split("T")[0],
    distributionKeyId: property.distributionKeys.length > 0 ? property.distributionKeys[0].id : "",
    householdRelatedAmount: 0,
    craftsmanAmount: 0,
  });
  
  // State für die Suche
  const [searchQuery, setSearchQuery] = useState("");
  
  // Gefilterte Ausgaben basierend auf der Suche
  const filteredExpenses = accounting.expenses.filter(expense => 
    expense.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    getCategoryName(expense.categoryId).toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Handler für das Hinzufügen einer neuen Ausgabe
  const handleAddExpense = () => {
    const newId = (accounting.expenses.length + 1).toString();
    const expenseToAdd: Expense = {
      id: newId,
      ...newExpense,
    };
    
    const updatedAccounting = {
      ...accounting,
      expenses: [...accounting.expenses, expenseToAdd],
    };
    
    onUpdate(updatedAccounting);
    setNewExpense({
      accountingId: accounting.id,
      categoryId: expenseCategories.length > 0 ? expenseCategories[0].id : "",
      description: "",
      amount: 0,
      date: new Date().toISOString().split("T")[0],
      distributionKeyId: property.distributionKeys.length > 0 ? property.distributionKeys[0].id : "",
      householdRelatedAmount: 0,
      craftsmanAmount: 0,
    });
    setIsAddDialogOpen(false);
  };
  
  // Handler für das Löschen einer Ausgabe
  const handleDeleteExpense = (id: string) => {
    const updatedAccounting = {
      ...accounting,
      expenses: accounting.expenses.filter(expense => expense.id !== id),
    };
    
    onUpdate(updatedAccounting);
  };
  
  // Funktion zum Abrufen des Kategorienamens anhand der ID
  const getCategoryName = (categoryId: string) => {
    const category = expenseCategories.find(c => c.id === categoryId);
    return category ? category.name : "Unbekannte Kategorie";
  };
  
  // Funktion zum Abrufen des Verteilerschlüsselnamens anhand der ID
  const getDistributionKeyName = (keyId: string) => {
    const key = property.distributionKeys.find(k => k.id === keyId);
    return key ? key.name : "Unbekannter Schlüssel";
  };
  
  // Funktion zum Formatieren eines Betrags als Währung
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "de" ? "de-AT" : "en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };
  
  // Funktion zum Formatieren eines Datums
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === "de" ? "de-AT" : "en-US");
  };
  
  // Funktion zum Überprüfen, ob eine Kategorie haushaltsnahe Dienstleistungen enthält
  const isCategoryHouseholdRelated = (categoryId: string) => {
    const category = expenseCategories.find(c => c.id === categoryId);
    return category ? category.isHouseholdRelated : false;
  };
  
  // Funktion zum Überprüfen, ob eine Kategorie Handwerkerleistungen enthält
  const isCategoryCraftsman = (categoryId: string) => {
    const category = expenseCategories.find(c => c.id === categoryId);
    return category ? category.isCraftsman : false;
  };
  
  // Funktion zum Überprüfen, ob eine Kategorie umlagefähig ist
  const isCategoryAllocatable = (categoryId: string) => {
    const category = expenseCategories.find(c => c.id === categoryId);
    return category ? category.isAllocatable : true;
  };
  
  // Berechne die Gesamtsumme der Ausgaben
  const totalExpenses = accounting.expenses.reduce((total, expense) => total + expense.amount, 0);
  
  // Berechne die Summe der umlagefähigen Ausgaben
  const allocatableExpenses = accounting.expenses
    .filter(expense => isCategoryAllocatable(expense.categoryId))
    .reduce((total, expense) => total + expense.amount, 0);
  
  // Berechne die Summe der nicht umlagefähigen Ausgaben
  const nonAllocatableExpenses = accounting.expenses
    .filter(expense => !isCategoryAllocatable(expense.categoryId))
    .reduce((total, expense) => total + expense.amount, 0);
  
  // Berechne die Summe der haushaltsnahen Dienstleistungen
  const householdRelatedExpenses = accounting.expenses
    .reduce((total, expense) => total + expense.householdRelatedAmount, 0);
  
  // Berechne die Summe der Handwerkerleistungen
  const craftsmanExpenses = accounting.expenses
    .reduce((total, expense) => total + expense.craftsmanAmount, 0);
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">
          {language === "de" ? "Ausgaben" : "Expenses"}
        </h3>
        
        <div className="flex items-center gap-2">
          <Input
            type="search"
            placeholder={language === "de" ? "Ausgaben suchen..." : "Search expenses..."}
            className="w-[250px]"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {language === "de" ? "Neue Ausgabe" : "New Expense"}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {language === "de" ? "Neue Ausgabe hinzufügen" : "Add New Expense"}
                </DialogTitle>
                <DialogDescription>
                  {language === "de" 
                    ? "Geben Sie die Details der neuen Ausgabe ein." 
                    : "Enter the details of the new expense."}
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="categoryId">
                    {language === "de" ? "Kategorie" : "Category"}
                  </Label>
                  <Select
                    value={newExpense.categoryId}
                    onValueChange={(value) => {
                      setNewExpense({
                        ...newExpense, 
                        categoryId: value,
                        // Setze haushaltsnahe Dienstleistungen und Handwerkerleistungen auf 0, wenn die Kategorie diese nicht unterstützt
                        householdRelatedAmount: isCategoryHouseholdRelated(value) ? newExpense.householdRelatedAmount : 0,
                        craftsmanAmount: isCategoryCraftsman(value) ? newExpense.craftsmanAmount : 0,
                      });
                    }}
                  >
                    <SelectTrigger id="categoryId">
                      <SelectValue placeholder={language === "de" ? "Kategorie auswählen" : "Select category"} />
                    </SelectTrigger>
                    <SelectContent>
                      {expenseCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">
                    {language === "de" ? "Beschreibung" : "Description"}
                  </Label>
                  <Input
                    id="description"
                    value={newExpense.description}
                    onChange={(e) => setNewExpense({...newExpense, description: e.target.value})}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">
                      {language === "de" ? "Betrag (€)" : "Amount (€)"}
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      value={newExpense.amount}
                      onChange={(e) => setNewExpense({...newExpense, amount: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="date">
                      {language === "de" ? "Datum" : "Date"}
                    </Label>
                    <Input
                      id="date"
                      type="date"
                      value={newExpense.date}
                      onChange={(e) => setNewExpense({...newExpense, date: e.target.value})}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="distributionKeyId">
                    {language === "de" ? "Verteilerschlüssel" : "Distribution Key"}
                  </Label>
                  <Select
                    value={newExpense.distributionKeyId}
                    onValueChange={(value) => setNewExpense({...newExpense, distributionKeyId: value})}
                  >
                    <SelectTrigger id="distributionKeyId">
                      <SelectValue placeholder={language === "de" ? "Schlüssel auswählen" : "Select key"} />
                    </SelectTrigger>
                    <SelectContent>
                      {property.distributionKeys.map((key) => (
                        <SelectItem key={key.id} value={key.id}>
                          {key.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {isCategoryHouseholdRelated(newExpense.categoryId) && (
                  <div className="space-y-2">
                    <Label htmlFor="householdRelatedAmount">
                      {language === "de" ? "Haushaltsnahe Dienstleistungen (€)" : "Household-Related Services (€)"}
                    </Label>
                    <Input
                      id="householdRelatedAmount"
                      type="number"
                      step="0.01"
                      value={newExpense.householdRelatedAmount}
                      onChange={(e) => setNewExpense({...newExpense, householdRelatedAmount: parseFloat(e.target.value) || 0})}
                    />
                    <p className="text-xs text-muted-foreground">
                      {language === "de" 
                        ? "Betrag der haushaltsnahen Dienstleistungen (ohne Material)" 
                        : "Amount of household-related services (without materials)"}
                    </p>
                  </div>
                )}
                
                {isCategoryCraftsman(newExpense.categoryId) && (
                  <div className="space-y-2">
                    <Label htmlFor="craftsmanAmount">
                      {language === "de" ? "Handwerkerleistungen (€)" : "Craftsman Services (€)"}
                    </Label>
                    <Input
                      id="craftsmanAmount"
                      type="number"
                      step="0.01"
                      value={newExpense.craftsmanAmount}
                      onChange={(e) => setNewExpense({...newExpense, craftsmanAmount: parseFloat(e.target.value) || 0})}
                    />
                    <p className="text-xs text-muted-foreground">
                      {language === "de" 
                        ? "Betrag der Handwerkerleistungen (ohne Material)" 
                        : "Amount of craftsman services (without materials)"}
                    </p>
                  </div>
                )}
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isAllocatable"
                    checked={isCategoryAllocatable(newExpense.categoryId)}
                    disabled
                  />
                  <Label htmlFor="isAllocatable" className={isCategoryAllocatable(newExpense.categoryId) ? "" : "text-muted-foreground"}>
                    {language === "de" ? "Umlagefähig" : "Allocatable"}
                  </Label>
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  {language === "de" ? "Abbrechen" : "Cancel"}
                </Button>
                <Button onClick={handleAddExpense}>
                  {language === "de" ? "Hinzufügen" : "Add"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      {/* Zusammenfassung */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Gesamtausgaben" : "Total Expenses"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalExpenses)}</div>
            <div className="text-sm text-muted-foreground mt-1">
              {language === "de" ? "Anzahl Ausgaben: " : "Number of expenses: "} {accounting.expenses.length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Umlagefähige Ausgaben" : "Allocatable Expenses"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(allocatableExpenses)}</div>
            <div className="text-sm text-muted-foreground mt-1">
              {language === "de" ? "Nicht umlagefähig: " : "Non-allocatable: "} {formatCurrency(nonAllocatableExpenses)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Steuerlich absetzbar" : "Tax Deductible"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <div className="text-sm text-muted-foreground">
                  {language === "de" ? "Haushaltsnahe Dienstleistungen" : "Household-Related Services"}
                </div>
                <div className="font-medium">{formatCurrency(householdRelatedExpenses)}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">
                  {language === "de" ? "Handwerkerleistungen" : "Craftsman Services"}
                </div>
                <div className="font-medium">{formatCurrency(craftsmanExpenses)}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Ausgabenliste */}
      {filteredExpenses.length > 0 ? (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "de" ? "Datum" : "Date"}</TableHead>
                <TableHead>{language === "de" ? "Kategorie" : "Category"}</TableHead>
                <TableHead>{language === "de" ? "Beschreibung" : "Description"}</TableHead>
                <TableHead>{language === "de" ? "Verteilerschlüssel" : "Distribution Key"}</TableHead>
                <TableHead className="text-right">{language === "de" ? "Betrag" : "Amount"}</TableHead>
                <TableHead className="w-[80px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredExpenses.map((expense) => (
                <TableRow key={expense.id}>
                  <TableCell>{formatDate(expense.date)}</TableCell>
                  <TableCell>{getCategoryName(expense.categoryId)}</TableCell>
                  <TableCell>{expense.description}</TableCell>
                  <TableCell>{getDistributionKeyName(expense.distributionKeyId)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(expense.amount)}</TableCell>
                  <TableCell>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => handleDeleteExpense(expense.id)}
                      className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center py-6 border rounded-md">
          <p className="text-sm text-muted-foreground">
            {language === "de" 
              ? "Keine Ausgaben gefunden. Fügen Sie eine neue Ausgabe hinzu." 
              : "No expenses found. Add a new expense."}
          </p>
        </div>
      )}
    </div>
  );
}
