"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { useLanguage } from '@/app/contexts/LanguageContext'
import { TranslationKey } from "@/i18n"
import { useToast } from "@/app/context/ToastContext"
import { Edit } from "lucide-react"

// Definiere die verfügbaren Tabs
const availableTabs = [
  { id: "employees", nameKey: "employees" as Translation<PERSON><PERSON> },
  { id: "income-expenses", nameKey: "income-expenses" as TranslationKey },
  { id: "ai", nameKey: "ai" as TranslationKey },
  { id: "taxReturn", nameKey: "taxReturn" as TranslationKey },
  { id: "wegAccounting", nameKey: "wegAccounting" as <PERSON><PERSON><PERSON> },
  { id: "timeTracking", nameKey: "timeTracking" as <PERSON><PERSON><PERSON> },
];

export function TabEditDialog() {
  const { t } = useLanguage();
  const [open, setOpen] = useState(false);
  const [selectedTabs, setSelectedTabs] = useState<string[]>([]);
  const [mounted, setMounted] = useState(false);
  const { toast } = useToast();

  // Lade die aktuellen Einstellungen
  useEffect(() => {
    setMounted(true);
    const loadSettings = () => {
      try {
        // Versuche, die Einstellungen aus dem localStorage zu laden
        const savedTabs = localStorage.getItem('visibleTabs');
        if (savedTabs) {
          setSelectedTabs(savedTabs.split(','));
        } else {
          // Standardmäßig alle Tabs auswählen
          setSelectedTabs(availableTabs.map(tab => tab.id).concat(['dashboard', 'invoices']));
        }
      } catch (error) {
        console.error('Fehler beim Laden der Tab-Einstellungen:', error);
        // Standardmäßig alle Tabs auswählen
        setSelectedTabs(availableTabs.map(tab => tab.id).concat(['dashboard', 'invoices']));
      }
    };

    if (open) {
      loadSettings();
    }
  }, [open]);

  // Checkbox-Handler
  const handleCheckboxChange = (tabId: string, checked: boolean) => {
    if (checked) {
      setSelectedTabs(prev => [...prev, tabId]);
    } else {
      setSelectedTabs(prev => prev.filter(id => id !== tabId));
    }
  };

  // Speichern der Einstellungen
  const handleSaveSettings = () => {
    // Stelle sicher, dass Dashboard und Invoices immer enthalten sind
    let tabsToSave = [...selectedTabs];
    if (!tabsToSave.includes('dashboard')) tabsToSave.push('dashboard');
    if (!tabsToSave.includes('invoices')) tabsToSave.push('invoices');

    try {
      // Speichere die Einstellungen im localStorage
      localStorage.setItem('visibleTabs', tabsToSave.join(','));

      toast.success(t('settings' as TranslationKey), { description: t('settingsSaved' as TranslationKey) });

      // Schließe den Dialog
      setOpen(false);

      // Lade die Seite neu, um die Änderungen anzuwenden
      window.location.reload();
    } catch (error) {
      console.error('Fehler beim Speichern der Tab-Einstellungen:', error);
      toast.error(t('error' as TranslationKey), { description: t('settingsError' as TranslationKey) });
    }
  };

  if (!mounted) return null;

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setOpen(true)}
        className="w-full flex items-center justify-between"
        title={t('editTabs' as TranslationKey)}
      >
        <span>{t('editTabs' as TranslationKey)}</span>
        <Edit className="h-4 w-4 ml-2" />
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t('editTabs' as TranslationKey)}</DialogTitle>
            <DialogDescription>
              {t('chooseVisibleTabs' as TranslationKey)}
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-2 opacity-50">
                <Checkbox id="dialog-dashboard" checked={true} disabled />
                <Label htmlFor="dialog-dashboard">{t('dashboard' as TranslationKey)} ({t('alwaysVisible' as TranslationKey)})</Label>
              </div>
              <div className="flex items-center space-x-2 opacity-50">
                <Checkbox id="dialog-invoices" checked={true} disabled />
                <Label htmlFor="dialog-invoices">{t('invoices' as TranslationKey)} ({t('alwaysVisible' as TranslationKey)})</Label>
              </div>
            </div>

            <div className="flex flex-col space-y-2">
              {availableTabs.map((tab) => (
                <div key={tab.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`dialog-${tab.id}`}
                    checked={selectedTabs.includes(tab.id)}
                    onCheckedChange={(checked) => handleCheckboxChange(tab.id, checked === true)}
                  />
                  <Label htmlFor={`dialog-${tab.id}`}>{t(tab.nameKey)}</Label>
                </div>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              {t('cancel' as TranslationKey)}
            </Button>
            <Button onClick={handleSaveSettings}>
              {t('saveChanges' as TranslationKey)}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
