import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import prisma from "@/app/utils/db";

export async function POST(request: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { reserveId, date, type, amount, description, documentId } = body;

    // Validate required fields
    if (!reserveId || !date || !type || amount === undefined || !description) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Check if reserve belongs to user
    const reserve = await prisma.reserve.findFirst({
      where: {
        id: reserveId,
        userId: session.user.id,
      },
    });

    if (!reserve) {
      return NextResponse.json({ error: "Reserve not found" }, { status: 404 });
    }

    // Calculate new balance
    let newBalance = reserve.balance;
    switch (type) {
      case "contribution":
      case "interest":
        newBalance += amount;
        break;
      case "withdrawal":
        newBalance -= amount;
        break;
      case "transfer":
        // For transfers, the amount could be positive or negative
        newBalance += amount;
        break;
      default:
        return NextResponse.json({ error: "Invalid transaction type" }, { status: 400 });
    }

    // Create transaction and update reserve balance in a transaction
    const [transaction] = await prisma.$transaction([
      prisma.reserveTransaction.create({
        data: {
          reserveId,
          date: new Date(date),
          type,
          amount,
          description,
          documentId: documentId || null,
        },
      }),
      prisma.reserve.update({
        where: {
          id: reserveId,
        },
        data: {
          balance: newBalance,
        },
      }),
    ]);

    return NextResponse.json(transaction);
  } catch (error) {
    console.error("Failed to create transaction:", error);
    return NextResponse.json({ error: "Failed to create transaction" }, { status: 500 });
  }
}
