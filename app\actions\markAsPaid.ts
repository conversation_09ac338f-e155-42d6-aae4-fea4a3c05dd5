"use server";

import prisma from "@/app/utils/db";
import { revalidatePath } from "next/cache";
import { requireUser } from "@/app/utils/hooks";

export async function MarkAsPaidAction(invoiceId: string) {
  const session = await requireUser();
  
  if (!session?.user) {
    throw new Error("Unauthorized");
  }
  
  try {
    await prisma.invoice.update({
      where: {
        id: invoiceId,
        userId: session.user.id,
      },
      data: {
        status: "PAID"
      },
    });
    
    revalidatePath("/dashboard/invoices");
  } catch (error) {
    console.error("Failed to mark invoice as paid:", error);
    throw new Error("Failed to mark invoice as paid");
  }
}
