"use client";

import { useState } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Info, Plus, Trash2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

type IncomeSource = {
  id: string;
  employer: string;
  amount: string;
  taxPaid: string;
  socialInsurance: string;
  isMain: boolean;
};

export function IncomeStep() {
  const { language } = useLanguage();
  
  // Beispieldaten für Einkünfte
  const [incomeSources, setIncomeSources] = useState<IncomeSource[]>([
    {
      id: "1",
      employer: "Musterfirma GmbH",
      amount: "42000",
      taxPaid: "12600",
      socialInsurance: "7560",
      isMain: true,
    },
  ]);
  
  const [otherIncome, setOtherIncome] = useState({
    selfEmployment: "",
    rental: "",
    capital: "",
    other: "",
  });
  
  const handleIncomeChange = (id: string, field: keyof IncomeSource, value: string | boolean) => {
    setIncomeSources(
      incomeSources.map((source) =>
        source.id === id ? { ...source, [field]: value } : source
      )
    );
  };
  
  const handleOtherIncomeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setOtherIncome({
      ...otherIncome,
      [name]: value,
    });
  };
  
  const addIncomeSource = () => {
    const newId = (incomeSources.length + 1).toString();
    setIncomeSources([
      ...incomeSources,
      {
        id: newId,
        employer: "",
        amount: "",
        taxPaid: "",
        socialInsurance: "",
        isMain: false,
      },
    ]);
  };
  
  const removeIncomeSource = (id: string) => {
    setIncomeSources(incomeSources.filter((source) => source.id !== id));
  };
  
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">
          {language === "de" ? "Einkünfte aus nichtselbständiger Arbeit" : "Income from Employment"}
        </h3>
        <p className="text-sm text-muted-foreground">
          {language === "de"
            ? "Gib deine Einkünfte aus nichtselbständiger Arbeit an. Diese Informationen findest du auf deinem Jahreslohnzettel (L16)."
            : "Enter your income from employment. You can find this information on your annual pay slip (L16)."}
        </p>
        
        {incomeSources.map((source) => (
          <Card key={source.id} className="border-gray-200 dark:border-gray-800">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`employer-${source.id}`}>
                    {language === "de" ? "Arbeitgeber" : "Employer"}
                  </Label>
                  <Input
                    id={`employer-${source.id}`}
                    value={source.employer}
                    onChange={(e) => handleIncomeChange(source.id, "employer", e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`amount-${source.id}`}>
                    {language === "de" ? "Bruttobezüge (€)" : "Gross Income (€)"}
                  </Label>
                  <Input
                    id={`amount-${source.id}`}
                    value={source.amount}
                    onChange={(e) => handleIncomeChange(source.id, "amount", e.target.value)}
                    type="number"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`taxPaid-${source.id}`}>
                    {language === "de" ? "Einbehaltene Lohnsteuer (€)" : "Withheld Income Tax (€)"}
                  </Label>
                  <Input
                    id={`taxPaid-${source.id}`}
                    value={source.taxPaid}
                    onChange={(e) => handleIncomeChange(source.id, "taxPaid", e.target.value)}
                    type="number"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`socialInsurance-${source.id}`}>
                    {language === "de" ? "Sozialversicherungsbeiträge (€)" : "Social Insurance Contributions (€)"}
                  </Label>
                  <Input
                    id={`socialInsurance-${source.id}`}
                    value={source.socialInsurance}
                    onChange={(e) => handleIncomeChange(source.id, "socialInsurance", e.target.value)}
                    type="number"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`isMain-${source.id}`}
                    checked={source.isMain}
                    onCheckedChange={(checked) => {
                      // Wenn dieser Arbeitgeber als Hauptarbeitgeber markiert wird,
                      // setze alle anderen auf false
                      if (checked) {
                        setIncomeSources(
                          incomeSources.map((s) => ({
                            ...s,
                            isMain: s.id === source.id,
                          }))
                        );
                      } else {
                        handleIncomeChange(source.id, "isMain", false);
                      }
                    }}
                  />
                  <Label htmlFor={`isMain-${source.id}`} className="cursor-pointer">
                    {language === "de" ? "Hauptarbeitgeber" : "Main Employer"}
                  </Label>
                </div>
                
                {incomeSources.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeIncomeSource(source.id)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    {language === "de" ? "Entfernen" : "Remove"}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
        
        <Button
          variant="outline"
          onClick={addIncomeSource}
          className="mt-2"
        >
          <Plus className="h-4 w-4 mr-2" />
          {language === "de" ? "Weiteren Arbeitgeber hinzufügen" : "Add Another Employer"}
        </Button>
      </div>
      
      <div className="space-y-4 mt-8">
        <h3 className="text-lg font-medium">
          {language === "de" ? "Andere Einkünfte" : "Other Income"}
        </h3>
        <p className="text-sm text-muted-foreground">
          {language === "de"
            ? "Gib andere Einkünfte an, falls vorhanden. Lasse die Felder leer, wenn du keine anderen Einkünfte hattest."
            : "Enter other income, if any. Leave fields empty if you had no other income."}
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="selfEmployment">
              {language === "de" ? "Selbständige Tätigkeit (€)" : "Self-Employment (€)"}
            </Label>
            <Input
              id="selfEmployment"
              name="selfEmployment"
              value={otherIncome.selfEmployment}
              onChange={handleOtherIncomeChange}
              type="number"
              placeholder="0"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="rental">
              {language === "de" ? "Vermietung und Verpachtung (€)" : "Rental Income (€)"}
            </Label>
            <Input
              id="rental"
              name="rental"
              value={otherIncome.rental}
              onChange={handleOtherIncomeChange}
              type="number"
              placeholder="0"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="capital">
              {language === "de" ? "Kapitalvermögen (€)" : "Capital Income (€)"}
            </Label>
            <Input
              id="capital"
              name="capital"
              value={otherIncome.capital}
              onChange={handleOtherIncomeChange}
              type="number"
              placeholder="0"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="other">
              {language === "de" ? "Sonstige Einkünfte (€)" : "Other Income (€)"}
            </Label>
            <Input
              id="other"
              name="other"
              value={otherIncome.other}
              onChange={handleOtherIncomeChange}
              type="number"
              placeholder="0"
            />
          </div>
        </div>
      </div>
      
      {/* FinanzOnline Hinweis */}
      <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-amber-800 dark:text-amber-300">
              {language === "de" ? "Wo finde ich das in FinanzOnline?" : "Where do I find this in FinanzOnline?"}
            </h4>
            <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
              {language === "de" 
                ? "Die Einkünfte aus nichtselbständiger Arbeit werden in FinanzOnline unter 'Einkünfte aus nichtselbständiger Arbeit' eingetragen. Die Lohnzettel werden meist automatisch vom Arbeitgeber übermittelt und sind bereits vorausgefüllt. Andere Einkünfte müssen unter den entsprechenden Kategorien eingetragen werden." 
                : "Income from employment is entered in FinanzOnline under 'Income from Employment'. The pay slips are usually automatically transmitted by the employer and are pre-filled. Other income must be entered under the corresponding categories."}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
