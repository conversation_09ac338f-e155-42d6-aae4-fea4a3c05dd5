"use client";

import { useState, useEffect } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Clock,
  Download
} from "lucide-react";
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, addDays, subDays } from "date-fns";
import { de } from "date-fns/locale";
import { cn } from "@/lib/utils";

// Mock data for time entries (same as in other components)
const mockTimeEntries = [
  {
    id: "1",
    description: "Website Development",
    date: new Date(),
    startTime: new Date(new Date().setHours(9, 0, 0, 0)),
    endTime: new Date(new Date().setHours(12, 30, 0, 0)),
    duration: 210, // 3.5 hours in minutes
    projectId: "1",
    projectName: "Client Website",
    projectColor: "#4f46e5",
    employeeId: "",
    employeeName: "",
    isBillable: true
  },
  {
    id: "2",
    description: "Meeting with Client",
    date: new Date(),
    startTime: new Date(new Date().setHours(14, 0, 0, 0)),
    endTime: new Date(new Date().setHours(15, 0, 0, 0)),
    duration: 60, // 1 hour in minutes
    projectId: "1",
    projectName: "Client Website",
    projectColor: "#4f46e5",
    employeeId: "",
    employeeName: "",
    isBillable: true
  },
  {
    id: "3",
    description: "Documentation",
    date: new Date(new Date().setDate(new Date().getDate() - 1)), // Yesterday
    startTime: new Date(new Date().setDate(new Date().getDate() - 1)).setHours(10, 0, 0, 0),
    endTime: new Date(new Date().setDate(new Date().getDate() - 1)).setHours(12, 0, 0, 0),
    duration: 120, // 2 hours in minutes
    projectId: "2",
    projectName: "Internal Project",
    projectColor: "#10b981",
    employeeId: "",
    employeeName: "",
    isBillable: false
  },
  {
    id: "4",
    description: "Design Review",
    date: new Date(new Date().setDate(new Date().getDate() - 2)),
    startTime: new Date(new Date().setDate(new Date().getDate() - 2)).setHours(13, 0, 0, 0),
    endTime: new Date(new Date().setDate(new Date().getDate() - 2)).setHours(15, 30, 0, 0),
    duration: 150, // 2.5 hours in minutes
    projectId: "3",
    projectName: "Marketing Campaign",
    projectColor: "#f59e0b",
    employeeId: "1",
    employeeName: "Max Mustermann",
    isBillable: true
  },
  // Add more entries for better reporting visualization
  {
    id: "5",
    description: "Content Writing",
    date: new Date(new Date().setDate(new Date().getDate() - 3)),
    startTime: new Date(new Date().setDate(new Date().getDate() - 3)).setHours(9, 0, 0, 0),
    endTime: new Date(new Date().setDate(new Date().getDate() - 3)).setHours(11, 30, 0, 0),
    duration: 150, // 2.5 hours in minutes
    projectId: "1",
    projectName: "Client Website",
    projectColor: "#4f46e5",
    employeeId: "",
    employeeName: "",
    isBillable: true
  },
  {
    id: "6",
    description: "Bug Fixing",
    date: new Date(new Date().setDate(new Date().getDate() - 4)),
    startTime: new Date(new Date().setDate(new Date().getDate() - 4)).setHours(13, 0, 0, 0),
    endTime: new Date(new Date().setDate(new Date().getDate() - 4)).setHours(17, 0, 0, 0),
    duration: 240, // 4 hours in minutes
    projectId: "2",
    projectName: "Internal Project",
    projectColor: "#10b981",
    employeeId: "",
    employeeName: "",
    isBillable: false
  },
  {
    id: "7",
    description: "Client Call",
    date: new Date(new Date().setDate(new Date().getDate() - 5)),
    startTime: new Date(new Date().setDate(new Date().getDate() - 5)).setHours(10, 0, 0, 0),
    endTime: new Date(new Date().setDate(new Date().getDate() - 5)).setHours(10, 30, 0, 0),
    duration: 30, // 0.5 hours in minutes
    projectId: "3",
    projectName: "Marketing Campaign",
    projectColor: "#f59e0b",
    employeeId: "1",
    employeeName: "Max Mustermann",
    isBillable: true
  }
];

export function TimeReportsView() {
  const { language, t } = useLanguage();
  const [timeEntries, setTimeEntries] = useState<any[]>([]);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: startOfWeek(new Date(), { weekStartsOn: 1 }),
    to: endOfWeek(new Date(), { weekStartsOn: 1 }),
  });

  // Handle date range selection from Calendar
  const handleDateRangeSelect = (range: { from?: Date; to?: Date } | undefined) => {
    if (range) {
      setDateRange({
        from: range.from,
        to: range.to
      });
    }
  };
  const [reportType, setReportType] = useState("summary");
  const [projectFilter, setProjectFilter] = useState("all");
  const [employeeFilter, setEmployeeFilter] = useState("all");
  const [projects, setProjects] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);

  // Define report data interface
  interface ReportData {
    totalDuration: number;
    billableDuration: number;
    nonBillableDuration: number;
    projectData: Record<string, number>;
    employeeData: Record<string, number>;
    dayData: Record<string, number>;
    entries: any[];
  }

  const [reportData, setReportData] = useState<Partial<ReportData>>({});

  // Format date based on language
  const formatDate = (date: Date | undefined, formatStr: string = "PPP") => {
    if (!date) return "";
    return format(
      date,
      formatStr,
      { locale: language === "de" ? de : undefined }
    );
  };

  // Format duration
  const formatDuration = (minutes: number | undefined) => {
    if (minutes === undefined) return "0:00";
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}:${mins.toString().padStart(2, '0')}`;
  };

  // Set date range to this week
  const setThisWeek = () => {
    setDateRange({
      from: startOfWeek(new Date(), { weekStartsOn: 1 }),
      to: endOfWeek(new Date(), { weekStartsOn: 1 }),
    });
  };

  // Set date range to last week
  const setLastWeek = () => {
    const lastWeekStart = subDays(startOfWeek(new Date(), { weekStartsOn: 1 }), 7);
    const lastWeekEnd = subDays(endOfWeek(new Date(), { weekStartsOn: 1 }), 7);
    setDateRange({
      from: lastWeekStart,
      to: lastWeekEnd,
    });
  };

  // Set date range to this month
  const setThisMonth = () => {
    setDateRange({
      from: startOfMonth(new Date()),
      to: endOfMonth(new Date()),
    });
  };

  // Set date range to last month
  const setLastMonth = () => {
    const lastMonthStart = startOfMonth(subDays(startOfMonth(new Date()), 1));
    const lastMonthEnd = endOfMonth(lastMonthStart);
    setDateRange({
      from: lastMonthStart,
      to: lastMonthEnd,
    });
  };

  // Load data
  useEffect(() => {
    // Simulate loading data from API
    setTimeout(() => {
      setTimeEntries(mockTimeEntries);

      // Extract unique projects and employees
      const uniqueProjects = Array.from(
        new Set(mockTimeEntries.map(entry => entry.projectId))
      ).map(id => {
        const entry = mockTimeEntries.find(e => e.projectId === id);
        return {
          id,
          name: entry?.projectName || "",
          color: entry?.projectColor || "#000000"
        };
      });

      const uniqueEmployees = Array.from(
        new Set(mockTimeEntries.filter(e => e.employeeId).map(entry => entry.employeeId))
      ).map(id => {
        const entry = mockTimeEntries.find(e => e.employeeId === id);
        return {
          id,
          name: entry?.employeeName || ""
        };
      });

      setProjects(uniqueProjects);
      setEmployees(uniqueEmployees);
    }, 500);
  }, []);

  // Generate report data when filters or date range change
  useEffect(() => {
    if (dateRange.from && dateRange.to && timeEntries.length > 0) {
      // Filter entries by date range
      let filtered = timeEntries.filter(entry => {
        const entryDate = new Date(entry.date);
        return entryDate >= dateRange.from! && entryDate <= dateRange.to!;
      });

      // Apply project filter
      if (projectFilter && projectFilter !== "all") {
        filtered = filtered.filter(entry => entry.projectId === projectFilter);
      }

      // Apply employee filter
      if (employeeFilter) {
        if (employeeFilter === "self") {
          filtered = filtered.filter(entry => !entry.employeeId);
        } else if (employeeFilter !== "all") {
          filtered = filtered.filter(entry => entry.employeeId === employeeFilter);
        }
      }

      // Generate summary data
      const totalDuration = filtered.reduce((total, entry) => total + (entry.duration || 0), 0);
      const billableDuration = filtered.filter(entry => entry.isBillable).reduce((total, entry) => total + (entry.duration || 0), 0);
      const nonBillableDuration = totalDuration - billableDuration;

      // Group by project
      const projectData = filtered.reduce((acc: Record<string, number>, entry) => {
        const projectId = entry.projectId || "unassigned";
        if (!acc[projectId]) {
          acc[projectId] = 0;
        }
        acc[projectId] += entry.duration || 0;
        return acc;
      }, {});

      // Group by employee
      const employeeData = filtered.reduce((acc: Record<string, number>, entry) => {
        const employeeId = entry.employeeId || "self";
        if (!acc[employeeId]) {
          acc[employeeId] = 0;
        }
        acc[employeeId] += entry.duration || 0;
        return acc;
      }, {});

      // Group by day
      const dayData: Record<string, number> = {};
      if (dateRange.from && dateRange.to) {
        const days = eachDayOfInterval({ start: dateRange.from, end: dateRange.to });
        days.forEach(day => {
          const dayKey = format(day, "yyyy-MM-dd");
          dayData[dayKey] = 0;
        });

        filtered.forEach(entry => {
          const dayKey = format(new Date(entry.date), "yyyy-MM-dd");
          if (dayData[dayKey] !== undefined) {
            dayData[dayKey] += entry.duration || 0;
          }
        });
      }

      setReportData({
        totalDuration,
        billableDuration,
        nonBillableDuration,
        projectData,
        employeeData,
        dayData,
        entries: filtered
      });
    }
  }, [dateRange, timeEntries, projectFilter, employeeFilter]);

  // Define type for project chart data
  interface ProjectChartData {
    name: string;
    value: number;
    color: string;
  }

  // Generate chart data for projects
  const getProjectChartData = (): ProjectChartData[] => {
    if (!reportData.projectData) return [];

    return Object.entries(reportData.projectData).map(([projectId, duration]) => {
      const project = projects.find(p => p.id === projectId) || { name: "Unassigned", color: "#94a3b8" };
      return {
        name: project.name,
        value: duration as number,
        color: project.color
      };
    }).sort((a, b) => b.value - a.value);
  };

  // Define type for day chart data
  interface DayChartData {
    name: string;
    date: string;
    value: number;
  }

  // Generate chart data for days
  const getDayChartData = (): DayChartData[] => {
    if (!reportData.dayData) return [];

    return Object.entries(reportData.dayData).map(([dayKey, duration]) => {
      const date = new Date(dayKey);
      return {
        name: formatDate(date, "EEE"),
        date: formatDate(date, "MMM d"),
        value: duration as number
      };
    });
  };

  // Download report as CSV
  const downloadReport = () => {
    if (!reportData.entries) return;

    const headers = [
      language === "de" ? "Datum" : "Date",
      language === "de" ? "Beschreibung" : "Description",
      language === "de" ? "Projekt" : "Project",
      language === "de" ? "Startzeit" : "Start Time",
      language === "de" ? "Endzeit" : "End Time",
      language === "de" ? "Dauer (Min)" : "Duration (Min)",
      language === "de" ? "Mitarbeiter" : "Employee",
      language === "de" ? "Abrechenbar" : "Billable"
    ];

    const rows = reportData.entries.map((entry: any) => [
      formatDate(new Date(entry.date)),
      entry.description,
      entry.projectName || "-",
      format(new Date(entry.startTime), "HH:mm"),
      entry.endTime ? format(new Date(entry.endTime), "HH:mm") : "-",
      entry.duration || "-",
      entry.employeeName || (language === "de" ? "Ich selbst" : "Myself"),
      entry.isBillable ? (language === "de" ? "Ja" : "Yes") : (language === "de" ? "Nein" : "No")
    ]);

    const csvContent = [
      headers.join(","),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(","))
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `time-report-${format(new Date(), "yyyy-MM-dd")}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      {/* Report Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 space-y-2">
              <label className="text-sm font-medium">
                {language === "de" ? "Zeitraum" : "Date Range"}
              </label>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={setThisWeek}
                  className={cn(
                    dateRange.from &&
                    dateRange.to &&
                    isSameDay(dateRange.from, startOfWeek(new Date(), { weekStartsOn: 1 })) &&
                    isSameDay(dateRange.to, endOfWeek(new Date(), { weekStartsOn: 1 })) &&
                    "bg-primary/10"
                  )}
                >
                  {language === "de" ? "Diese Woche" : "This Week"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={setLastWeek}
                >
                  {language === "de" ? "Letzte Woche" : "Last Week"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={setThisMonth}
                >
                  {language === "de" ? "Dieser Monat" : "This Month"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={setLastMonth}
                >
                  {language === "de" ? "Letzter Monat" : "Last Month"}
                </Button>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <CalendarIcon className="h-4 w-4" />
                      {dateRange.from && dateRange.to ? (
                        <>
                          {formatDate(dateRange.from, "MMM d")} - {formatDate(dateRange.to, "MMM d, yyyy")}
                        </>
                      ) : (
                        <span>{language === "de" ? "Benutzerdefiniert" : "Custom"}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={dateRange}
                      onSelect={handleDateRangeSelect}
                      numberOfMonths={2}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {/* Project Filter */}
              <Select value={projectFilter} onValueChange={setProjectFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder={language === "de" ? "Projekt" : "Project"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {language === "de" ? "Alle Projekte" : "All Projects"}
                  </SelectItem>
                  {projects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: project.color }}
                        />
                        {project.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Employee Filter */}
              <Select value={employeeFilter} onValueChange={setEmployeeFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder={language === "de" ? "Mitarbeiter" : "Employee"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {language === "de" ? "Alle Mitarbeiter" : "All Employees"}
                  </SelectItem>
                  <SelectItem value="self">
                    {language === "de" ? "Ich selbst" : "Myself"}
                  </SelectItem>
                  {employees.map(employee => (
                    <SelectItem key={employee.id} value={employee.id}>
                      {employee.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Download Report */}
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={downloadReport}
              >
                <Download className="h-4 w-4" />
                <span>{language === "de" ? "Exportieren" : "Export"}</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Summary */}
      {reportData.totalDuration !== undefined && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {language === "de" ? "Gesamtzeit" : "Total Time"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold flex items-center">
                <Clock className="h-5 w-5 mr-2 text-primary" />
                {formatDuration(reportData.totalDuration)}
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  {language === "de" ? "Stunden" : "hours"}
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {language === "de" ? "Abrechenbare Zeit" : "Billable Time"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold flex items-center">
                <Clock className="h-5 w-5 mr-2 text-green-500" />
                {formatDuration(reportData.billableDuration)}
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  {language === "de" ? "Stunden" : "hours"}
                </span>
                {reportData.totalDuration > 0 && reportData.billableDuration !== undefined && (
                  <span className="text-sm font-normal text-muted-foreground ml-2">
                    ({Math.round((reportData.billableDuration / reportData.totalDuration) * 100)}%)
                  </span>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {language === "de" ? "Nicht abrechenbare Zeit" : "Non-Billable Time"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold flex items-center">
                <Clock className="h-5 w-5 mr-2 text-orange-500" />
                {formatDuration(reportData.nonBillableDuration)}
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  {language === "de" ? "Stunden" : "hours"}
                </span>
                {reportData.totalDuration > 0 && reportData.nonBillableDuration !== undefined && (
                  <span className="text-sm font-normal text-muted-foreground ml-2">
                    ({Math.round((reportData.nonBillableDuration / reportData.totalDuration) * 100)}%)
                  </span>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Report Charts */}
      <Tabs defaultValue="daily" className="w-full">
        <TabsList>
          <TabsTrigger value="daily" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            <span>{language === "de" ? "Tägliche Übersicht" : "Daily Overview"}</span>
          </TabsTrigger>
          <TabsTrigger value="projects" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            <span>{language === "de" ? "Projekte" : "Projects"}</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="daily" className="mt-4">
          <Card>
            <CardContent className="p-4">
              {reportData.dayData ? (
                <div>
                  {/* Simple bar chart visualization */}
                  <div className="h-64 flex items-end gap-2">
                    {getDayChartData().map((day, index) => (
                      <div key={index} className="flex flex-col items-center flex-1">
                        <div
                          className="w-full bg-primary/80 rounded-t-sm"
                          style={{
                            height: `${Math.max(5, (day.value / (reportData.dayData ? Math.max(...Object.values(reportData.dayData)) : 1)) * 200)}px`
                          }}
                        ></div>
                        <div className="text-xs mt-2 font-medium">{day.name}</div>
                        <div className="text-xs text-muted-foreground">{day.date}</div>
                        <div className="text-xs font-medium mt-1">{formatDuration(day.value)}</div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-10">
                  <BarChart className="h-12 w-12 mx-auto text-muted-foreground" />
                  <p className="mt-2 text-muted-foreground">
                    {language === "de"
                      ? "Keine Daten für den ausgewählten Zeitraum verfügbar."
                      : "No data available for the selected time period."}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="projects" className="mt-4">
          <Card>
            <CardContent className="p-4">
              {reportData.projectData && Object.keys(reportData.projectData).length > 0 ? (
                <div className="space-y-4">
                  {/* Project breakdown */}
                  {getProjectChartData().map((project, index) => (
                    <div key={index} className="flex items-center">
                      <div
                        className="w-4 h-4 rounded-full mr-3"
                        style={{ backgroundColor: project.color }}
                      ></div>
                      <div className="flex-1">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium">{project.name}</span>
                          <span className="text-sm text-muted-foreground">{formatDuration(project.value)}</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div
                            className="h-2 rounded-full"
                            style={{
                              width: `${reportData.totalDuration ? (project.value / reportData.totalDuration) * 100 : 0}%`,
                              backgroundColor: project.color
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-10">
                  <PieChart className="h-12 w-12 mx-auto text-muted-foreground" />
                  <p className="mt-2 text-muted-foreground">
                    {language === "de"
                      ? "Keine Projektdaten für den ausgewählten Zeitraum verfügbar."
                      : "No project data available for the selected time period."}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
