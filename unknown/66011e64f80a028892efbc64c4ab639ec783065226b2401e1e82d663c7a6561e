import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/utils/db";
import { requireUser } from "@/app/utils/hooks";

export async function GET() {
  try {
    const session = await requireUser();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;    // Fetch expenses (invoices) with related data
    const expenses = await prisma.expense.findMany({
      where: {
        accountingPeriod: {
          userId: userId,
        },
      },
      include: {
        category: true,
        accountingPeriod: {
          include: {
            property: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    });

    // Transform to match the expected Invoice interface
    const invoices = expenses.map((expense) => ({
      id: expense.id,
      propertyId: expense.accountingPeriod.propertyId,
      number: `RE-${expense.date.getFullYear()}-${expense.id.slice(-3)}`,
      date: expense.date.toISOString().split("T")[0],
      dueDate: new Date(expense.date.getTime() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0], // 30 days after expense date
      vendorName: expense.description.split(" - ")[0] || "Unknown Vendor",
      description: expense.description,
      amount: expense.amount,
      status: "pending" as const,
      categoryId: expense.categoryId,
      notes: "",
    }));

    return NextResponse.json(invoices);
  } catch (error) {
    console.error("Failed to fetch invoices:", error);
    return NextResponse.json(
      { error: "Failed to fetch invoices" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {  try {
    const session = await requireUser();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    const invoiceData = await request.json();

    // First, find or create an accounting period for the property and current year
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1);
    const endDate = new Date(currentYear, 11, 31);    let accountingPeriod = await prisma.accountingPeriod.findFirst({
      where: {
        userId: userId,
        propertyId: invoiceData.propertyId,
        year: currentYear,
      },
    });

    if (!accountingPeriod) {
      accountingPeriod = await prisma.accountingPeriod.create({
        data: {
          userId: userId,
          propertyId: invoiceData.propertyId,
          year: currentYear,
          startDate,
          endDate,
          status: "draft",
        },
      });
    }

    // Create the expense (invoice)
    const expense = await prisma.expense.create({
      data: {
        accountingPeriodId: accountingPeriod.id,
        categoryId: invoiceData.categoryId,
        description: `${invoiceData.vendorName} - ${invoiceData.description}`,
        amount: parseFloat(invoiceData.amount),
        date: new Date(invoiceData.date),
      },
      include: {
        category: true,
        accountingPeriod: {
          include: {
            property: true,
          },
        },
      },
    });

    // Transform to match the expected Invoice interface
    const invoice = {
      id: expense.id,
      propertyId: expense.accountingPeriod.propertyId,
      number: `RE-${expense.date.getFullYear()}-${expense.id.slice(-3)}`,
      date: expense.date.toISOString().split("T")[0],
      dueDate: new Date(expense.date.getTime() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      vendorName: invoiceData.vendorName,
      description: invoiceData.description,
      amount: expense.amount,
      status: "pending" as const,
      categoryId: expense.categoryId,
      notes: invoiceData.notes || "",
    };

    return NextResponse.json(invoice, { status: 201 });
  } catch (error) {
    console.error("Failed to create invoice:", error);
    return NextResponse.json(
      { error: "Failed to create invoice" },
      { status: 500 }
    );
  }
}
