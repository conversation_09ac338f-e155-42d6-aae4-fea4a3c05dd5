"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Info, Plus, Trash2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

type Expense = {
  id: string;
  category: string;
  description: string;
  amount: string;
  date: string;
  hasReceipt: boolean;
};

export function ExpensesStep() {
  const { language } = useLanguage();
  
  // Beispieldaten für Werbungskosten
  const [expenses, setExpenses] = useState<Expense[]>([
    {
      id: "1",
      category: "commuting",
      description: "Pendlerpauschale",
      amount: "1476",
      date: "2023-12-31",
      hasReceipt: false,
    },
    {
      id: "2",
      category: "workEquipment",
      description: "Laptop für Homeoffice",
      amount: "1200",
      date: "2023-06-15",
      hasReceipt: true,
    },
  ]);
  
  const [commutingInfo, setCommutingInfo] = useState({
    distance: "35",
    daysPerWeek: "5",
    publicTransport: "no",
  });
  
  const [homeOfficeInfo, setHomeOfficeInfo] = useState({
    daysTotal: "120",
    hasHomeOfficeDeduction: true,
  });
  
  const handleExpenseChange = (id: string, field: keyof Expense, value: string | boolean) => {
    setExpenses(
      expenses.map((expense) =>
        expense.id === id ? { ...expense, [field]: value } : expense
      )
    );
  };
  
  const handleCommutingChange = (field: string, value: string) => {
    setCommutingInfo({
      ...commutingInfo,
      [field]: value,
    });
  };
  
  const handleHomeOfficeChange = (field: string, value: string | boolean) => {
    setHomeOfficeInfo({
      ...homeOfficeInfo,
      [field]: value,
    });
  };
  
  const addExpense = () => {
    const newId = (expenses.length + 1).toString();
    setExpenses([
      ...expenses,
      {
        id: newId,
        category: "",
        description: "",
        amount: "",
        date: "",
        hasReceipt: false,
      },
    ]);
  };
  
  const removeExpense = (id: string) => {
    setExpenses(expenses.filter((expense) => expense.id !== id));
  };
  
  // Berechne die Pendlerpauschale basierend auf den Eingaben
  const calculateCommutingAllowance = () => {
    const distance = parseInt(commutingInfo.distance);
    const daysPerWeek = parseInt(commutingInfo.daysPerWeek);
    
    // Vereinfachte Berechnung (in einer echten Anwendung wäre dies komplexer)
    let monthlyAmount = 0;
    
    if (commutingInfo.publicTransport === "yes") {
      // Kleine Pendlerpauschale (öffentliche Verkehrsmittel zumutbar)
      if (distance >= 20 && distance < 40) {
        monthlyAmount = 58;
      } else if (distance >= 40 && distance < 60) {
        monthlyAmount = 113;
      } else if (distance >= 60) {
        monthlyAmount = 168;
      }
    } else {
      // Große Pendlerpauschale (öffentliche Verkehrsmittel nicht zumutbar)
      if (distance >= 2 && distance < 20) {
        monthlyAmount = 31;
      } else if (distance >= 20 && distance < 40) {
        monthlyAmount = 123;
      } else if (distance >= 40 && distance < 60) {
        monthlyAmount = 214;
      } else if (distance >= 60) {
        monthlyAmount = 306;
      }
    }
    
    // Anpassung basierend auf Arbeitstagen pro Woche
    if (daysPerWeek < 5) {
      monthlyAmount = monthlyAmount * daysPerWeek / 5;
    }
    
    // Jahresbetrag
    return Math.round(monthlyAmount * 12);
  };
  
  // Berechne die Homeoffice-Pauschale
  const calculateHomeOfficeAllowance = () => {
    const daysTotal = parseInt(homeOfficeInfo.daysTotal);
    // 3€ pro Homeoffice-Tag, maximal 300€ pro Jahr
    return Math.min(daysTotal * 3, 300);
  };
  
  return (
    <div className="space-y-6">
      <Tabs defaultValue="categories" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="categories">
            {language === "de" ? "Kategorien" : "Categories"}
          </TabsTrigger>
          <TabsTrigger value="commuting">
            {language === "de" ? "Pendlerpauschale" : "Commuter Allowance"}
          </TabsTrigger>
          <TabsTrigger value="homeOffice">
            {language === "de" ? "Homeoffice" : "Home Office"}
          </TabsTrigger>
        </TabsList>
        
        {/* Kategorien Tab */}
        <TabsContent value="categories" className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Werbungskosten" : "Work-Related Expenses"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Gib deine berufsbedingten Ausgaben an. Diese können deine Steuerlast reduzieren."
                : "Enter your work-related expenses. These can reduce your tax burden."}
            </p>
          </div>
          
          {expenses.map((expense) => (
            <Card key={expense.id} className="border-gray-200 dark:border-gray-800">
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`category-${expense.id}`}>
                      {language === "de" ? "Kategorie" : "Category"}
                    </Label>
                    <Select
                      value={expense.category}
                      onValueChange={(value) => handleExpenseChange(expense.id, "category", value)}
                    >
                      <SelectTrigger id={`category-${expense.id}`}>
                        <SelectValue placeholder={language === "de" ? "Kategorie auswählen" : "Select category"} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="commuting">
                          {language === "de" ? "Pendlerpauschale" : "Commuter Allowance"}
                        </SelectItem>
                        <SelectItem value="workEquipment">
                          {language === "de" ? "Arbeitsmittel" : "Work Equipment"}
                        </SelectItem>
                        <SelectItem value="homeOffice">
                          {language === "de" ? "Homeoffice-Pauschale" : "Home Office Allowance"}
                        </SelectItem>
                        <SelectItem value="professionalDevelopment">
                          {language === "de" ? "Fortbildung" : "Professional Development"}
                        </SelectItem>
                        <SelectItem value="workClothes">
                          {language === "de" ? "Arbeitskleidung" : "Work Clothes"}
                        </SelectItem>
                        <SelectItem value="professionalLiterature">
                          {language === "de" ? "Fachliteratur" : "Professional Literature"}
                        </SelectItem>
                        <SelectItem value="other">
                          {language === "de" ? "Sonstiges" : "Other"}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor={`description-${expense.id}`}>
                      {language === "de" ? "Beschreibung" : "Description"}
                    </Label>
                    <Input
                      id={`description-${expense.id}`}
                      value={expense.description}
                      onChange={(e) => handleExpenseChange(expense.id, "description", e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor={`amount-${expense.id}`}>
                      {language === "de" ? "Betrag (€)" : "Amount (€)"}
                    </Label>
                    <Input
                      id={`amount-${expense.id}`}
                      value={expense.amount}
                      onChange={(e) => handleExpenseChange(expense.id, "amount", e.target.value)}
                      type="number"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor={`date-${expense.id}`}>
                      {language === "de" ? "Datum" : "Date"}
                    </Label>
                    <Input
                      id={`date-${expense.id}`}
                      value={expense.date}
                      onChange={(e) => handleExpenseChange(expense.id, "date", e.target.value)}
                      type="date"
                    />
                  </div>
                </div>
                
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`hasReceipt-${expense.id}`}
                      checked={expense.hasReceipt}
                      onCheckedChange={(checked) => 
                        handleExpenseChange(expense.id, "hasReceipt", checked as boolean)
                      }
                    />
                    <Label htmlFor={`hasReceipt-${expense.id}`} className="cursor-pointer">
                      {language === "de" ? "Beleg vorhanden" : "Receipt Available"}
                    </Label>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeExpense(expense.id)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    {language === "de" ? "Entfernen" : "Remove"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
          
          <Button
            variant="outline"
            onClick={addExpense}
            className="mt-2"
          >
            <Plus className="h-4 w-4 mr-2" />
            {language === "de" ? "Weitere Werbungskosten hinzufügen" : "Add More Expenses"}
          </Button>
        </TabsContent>
        
        {/* Pendlerpauschale Tab */}
        <TabsContent value="commuting" className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Pendlerpauschale berechnen" : "Calculate Commuter Allowance"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Berechne deine Pendlerpauschale basierend auf der Entfernung zwischen Wohnung und Arbeit."
                : "Calculate your commuter allowance based on the distance between home and work."}
            </p>
          </div>
          
          <Card>
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="distance">
                    {language === "de" ? "Entfernung (km)" : "Distance (km)"}
                  </Label>
                  <Input
                    id="distance"
                    value={commutingInfo.distance}
                    onChange={(e) => handleCommutingChange("distance", e.target.value)}
                    type="number"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="daysPerWeek">
                    {language === "de" ? "Arbeitstage pro Woche" : "Work Days per Week"}
                  </Label>
                  <Select
                    value={commutingInfo.daysPerWeek}
                    onValueChange={(value) => handleCommutingChange("daysPerWeek", value)}
                  >
                    <SelectTrigger id="daysPerWeek">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="6">6</SelectItem>
                      <SelectItem value="7">7</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2 md:col-span-2">
                  <Label>
                    {language === "de" ? "Öffentliche Verkehrsmittel zumutbar?" : "Public Transport Reasonable?"}
                  </Label>
                  <div className="flex space-x-4 mt-1">
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="publicTransportYes"
                        name="publicTransport"
                        value="yes"
                        checked={commutingInfo.publicTransport === "yes"}
                        onChange={() => handleCommutingChange("publicTransport", "yes")}
                        className="h-4 w-4"
                      />
                      <Label htmlFor="publicTransportYes" className="cursor-pointer">
                        {language === "de" ? "Ja" : "Yes"}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id="publicTransportNo"
                        name="publicTransport"
                        value="no"
                        checked={commutingInfo.publicTransport === "no"}
                        onChange={() => handleCommutingChange("publicTransport", "no")}
                        className="h-4 w-4"
                      />
                      <Label htmlFor="publicTransportNo" className="cursor-pointer">
                        {language === "de" ? "Nein" : "No"}
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                <h4 className="font-medium text-blue-700 dark:text-blue-300">
                  {language === "de" ? "Berechnete Pendlerpauschale" : "Calculated Commuter Allowance"}
                </h4>
                <p className="text-blue-600 dark:text-blue-400 mt-1">
                  <span className="font-bold text-lg">€{calculateCommutingAllowance()}</span>
                  {language === "de" ? " pro Jahr" : " per year"}
                </p>
                <p className="text-sm text-blue-500 dark:text-blue-400 mt-2">
                  {language === "de"
                    ? "Diese Berechnung ist eine Schätzung. Die genaue Höhe der Pendlerpauschale hängt von weiteren Faktoren ab."
                    : "This calculation is an estimate. The exact amount of the commuter allowance depends on additional factors."}
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Homeoffice Tab */}
        <TabsContent value="homeOffice" className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Homeoffice-Pauschale berechnen" : "Calculate Home Office Allowance"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Berechne deine Homeoffice-Pauschale basierend auf den Tagen, die du im Homeoffice gearbeitet hast."
                : "Calculate your home office allowance based on the days you worked from home."}
            </p>
          </div>
          
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasHomeOfficeDeduction"
                    checked={homeOfficeInfo.hasHomeOfficeDeduction}
                    onCheckedChange={(checked) => 
                      handleHomeOfficeChange("hasHomeOfficeDeduction", checked as boolean)
                    }
                  />
                  <Label htmlFor="hasHomeOfficeDeduction" className="cursor-pointer">
                    {language === "de" ? "Homeoffice-Pauschale beantragen" : "Apply for Home Office Allowance"}
                  </Label>
                </div>
                
                {homeOfficeInfo.hasHomeOfficeDeduction && (
                  <div className="space-y-2">
                    <Label htmlFor="daysTotal">
                      {language === "de" ? "Anzahl der Homeoffice-Tage im Jahr" : "Number of Home Office Days in the Year"}
                    </Label>
                    <Input
                      id="daysTotal"
                      value={homeOfficeInfo.daysTotal}
                      onChange={(e) => handleHomeOfficeChange("daysTotal", e.target.value)}
                      type="number"
                      max="365"
                    />
                    <p className="text-xs text-muted-foreground">
                      {language === "de" 
                        ? "Maximal 3€ pro Homeoffice-Tag, bis zu 300€ pro Jahr." 
                        : "Maximum €3 per home office day, up to €300 per year."}
                    </p>
                  </div>
                )}
                
                {homeOfficeInfo.hasHomeOfficeDeduction && (
                  <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                    <h4 className="font-medium text-blue-700 dark:text-blue-300">
                      {language === "de" ? "Berechnete Homeoffice-Pauschale" : "Calculated Home Office Allowance"}
                    </h4>
                    <p className="text-blue-600 dark:text-blue-400 mt-1">
                      <span className="font-bold text-lg">€{calculateHomeOfficeAllowance()}</span>
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* FinanzOnline Hinweis */}
      <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-amber-800 dark:text-amber-300">
              {language === "de" ? "Wo finde ich das in FinanzOnline?" : "Where do I find this in FinanzOnline?"}
            </h4>
            <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
              {language === "de" 
                ? "Werbungskosten werden in FinanzOnline unter 'Werbungskosten, Pendlerpauschale' eingetragen. Die Pendlerpauschale findest du unter 'Pendlerpauschale/Pendlereuro', die Homeoffice-Pauschale unter 'Homeoffice-Pauschale'." 
                : "Work-related expenses are entered in FinanzOnline under 'Work-Related Expenses, Commuter Allowance'. You can find the commuter allowance under 'Commuter Allowance/Commuter Euro', the home office allowance under 'Home Office Allowance'."}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
