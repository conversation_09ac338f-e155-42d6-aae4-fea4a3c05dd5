import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import { updateOwner, deleteOwner, getOwnerById } from "@/app/lib/ownerStorage";

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id } = await params;
    const data = await request.json();
    
    // Update the owner using shared storage
    const updatedOwner = updateOwner(id, {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      phone: data.phone || "",
      address: data.address || ""
    });
    
    if (!updatedOwner) {
      return NextResponse.json({ error: "Owner not found" }, { status: 404 });
    }
    
    return NextResponse.json(updatedOwner);
  } catch (error) {
    console.error("Failed to update owner:", error);
    return NextResponse.json({ error: "Failed to update owner" }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    const { id } = await params;
    
    // Delete the owner using shared storage
    const success = deleteOwner(id);
    
    if (!success) {
      return NextResponse.json({ error: "Owner not found" }, { status: 404 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete owner:", error);
    return NextResponse.json({ error: "Failed to delete owner" }, { status: 500 });
  }
}
