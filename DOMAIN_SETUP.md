# Domain Setup Guide

## Architektur

### Framer (Marketing Website)
- **Domain**: `tax-mate.at`
- **Inhalt**: Produktvorstellung, Features, Marketing
- **Login-Button**: Verlinkt zu `https://dashboard.tax-mate.at/login`
- **WEG-Abrechnung Button**: Ver<PERSON>t zu `https://dashboard.tax-mate.at/weg-accounting/login`

### Vercel (App/Dashboard)
- **Domain**: `dashboard.tax-mate.at`
- **Inhalt**: Login, Dashboard, Rechnungsstellung, WEG-Abrechnung App
- **Root Route**: Automatische Weiterleitung zu Login oder Dashboard

## DNS Konfiguration

Bei deinem Domain-Provider (z.B. Namecheap, GoDaddy):

```
A Record:
tax-mate.at → Framer IP

CNAME Record:
dashboard.tax-mate.at → dein-vercel-projekt.vercel.app
```

## Vercel Konfiguration

1. **Domain hinzufügen**: `dashboard.tax-mate.at` in Vercel Project Settings
2. **Environment Variables** setzen:
   ```
   NEXTAUTH_URL=https://dashboard.tax-mate.at
   NEXT_PUBLIC_APP_URL=https://dashboard.tax-mate.at
   ```

## Framer Links

### Login Button
```
Link: https://dashboard.tax-mate.at/login
```

### WEG-Abrechnung Button
```
Link: https://dashboard.tax-mate.at/weg-accounting/login
```

## User Flow

1. **Nutzer besucht** `tax-mate.at` (Framer)
2. **Klickt Login** → Weiterleitung zu `dashboard.tax-mate.at/login`
3. **Nach Login** → Automatische Weiterleitung zu `dashboard.tax-mate.at/dashboard`
4. **Direkter Besuch** von `dashboard.tax-mate.at` → Automatische Weiterleitung je nach Login-Status

## Testing

### Lokal testen:
```bash
# .env für lokale Entwicklung
NEXTAUTH_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Produktion testen:
- Framer Links testen
- Login-Flow testen
- E-Mail-Verifikation testen (sollte dashboard.tax-mate.at Links enthalten)
