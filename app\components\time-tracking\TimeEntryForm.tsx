"use client";

import { useState, useEffect } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { CalendarIcon, Clock } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { useToast } from "@/app/context/ToastContext";

interface TimeEntryFormProps {
  isOpen: boolean;
  onClose: () => void;
  entryToEdit?: any; // Replace with proper type when available
}

export function TimeEntryForm({ isOpen, onClose, entryToEdit }: TimeEntryFormProps) {
  const { language, t } = useLanguage();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    description: "",
    date: new Date(),
    startTime: "",
    endTime: "",
    projectId: "",
    employeeId: "",
    isBillable: true
  });
  const [projects, setProjects] = useState<any[]>([]); // Replace with proper type
  const [employees, setEmployees] = useState<any[]>([]); // Replace with proper type
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Format date based on language
  const formatDate = (date: Date) => {
    return format(
      date,
      "PPP",
      { locale: language === "de" ? de : undefined }
    );
  };

  // Load data when form opens
  useEffect(() => {
    if (isOpen) {
      // Reset form when opening
      if (!entryToEdit) {
        setFormData({
          description: "",
          date: new Date(),
          startTime: format(new Date(), "HH:mm"),
          endTime: "",
          projectId: "",
          employeeId: "",
          isBillable: true
        });
      } else {
        // Fill form with data to edit
        setFormData({
          description: entryToEdit.description,
          date: new Date(entryToEdit.date),
          startTime: format(new Date(entryToEdit.startTime), "HH:mm"),
          endTime: entryToEdit.endTime ? format(new Date(entryToEdit.endTime), "HH:mm") : "",
          projectId: entryToEdit.projectId || "",
          employeeId: entryToEdit.employeeId || "",
          isBillable: entryToEdit.isBillable
        });
      }

      // Load projects and employees (mock data for now)
      setProjects([
        { id: "1", name: "Project 1", color: "#4f46e5" },
        { id: "2", name: "Project 2", color: "#10b981" },
        { id: "3", name: "Project 3", color: "#f59e0b" }
      ]);

      setEmployees([
        { id: "1", name: "Max Mustermann" },
        { id: "2", name: "Anna Beispiel" }
      ]);
    }
  }, [isOpen, entryToEdit]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormData(prev => ({ ...prev, date }));
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isBillable: checked }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Validate form
      if (!formData.description || !formData.startTime) {
        throw new Error(language === "de" ? "Bitte füllen Sie alle Pflichtfelder aus." : "Please fill in all required fields.");
      }

      // Calculate duration if both start and end time are provided
      let duration = null;
      if (formData.startTime && formData.endTime) {
        const [startHours, startMinutes] = formData.startTime.split(':').map(Number);
        const [endHours, endMinutes] = formData.endTime.split(':').map(Number);

        const startTotalMinutes = startHours * 60 + startMinutes;
        const endTotalMinutes = endHours * 60 + endMinutes;

        if (endTotalMinutes < startTotalMinutes) {
          throw new Error(language === "de" ? "Die Endzeit muss nach der Startzeit liegen." : "End time must be after start time.");
        }

        duration = endTotalMinutes - startTotalMinutes;
      }

      // Prepare data for submission
      const entryData = {
        ...formData,
        duration,
        // Convert times to full ISO dates
        startTime: (() => {
          const date = new Date(formData.date);
          const [hours, minutes] = formData.startTime.split(':').map(Number);
          date.setHours(hours, minutes, 0, 0);
          return date.toISOString();
        })(),
        endTime: formData.endTime ? (() => {
          const date = new Date(formData.date);
          const [hours, minutes] = formData.endTime.split(':').map(Number);
          date.setHours(hours, minutes, 0, 0);
          return date.toISOString();
        })() : null
      };

      // For now, just log the data that would be submitted
      console.log("Time entry data to submit:", entryData);

      // Mock successful submission
      setTimeout(() => {
        toast.success(
          language === "de" ? "Zeiteintrag gespeichert" : "Time entry saved",
          { description: language === "de" ? "Der Zeiteintrag wurde erfolgreich gespeichert." : "The time entry was successfully saved." }
        );
        onClose();
        setIsSubmitting(false);
      }, 1000);

    } catch (error) {
      console.error("Error submitting time entry:", error);
      toast.error(
        language === "de" ? "Fehler" : "Error",
        { description: error instanceof Error ? error.message : "An unknown error occurred" }
      );
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {entryToEdit
              ? (language === "de" ? "Zeiteintrag bearbeiten" : "Edit Time Entry")
              : (language === "de" ? "Neuer Zeiteintrag" : "New Time Entry")}
          </DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Description */}
          <div className="grid gap-2">
            <Label htmlFor="description">
              {language === "de" ? "Beschreibung" : "Description"}
              <span className="text-red-500 ml-1">*</span>
            </Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder={language === "de" ? "Was haben Sie gemacht?" : "What did you work on?"}
              className="resize-none"
            />
          </div>

          {/* Date */}
          <div className="grid gap-2">
            <Label>
              {language === "de" ? "Datum" : "Date"}
              <span className="text-red-500 ml-1">*</span>
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formatDate(formData.date)}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={formData.date}
                  onSelect={handleDateChange}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Time Range */}
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="startTime">
                {language === "de" ? "Startzeit" : "Start Time"}
                <span className="text-red-500 ml-1">*</span>
              </Label>
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="startTime"
                  name="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={handleChange}
                  className="w-full"
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="endTime">
                {language === "de" ? "Endzeit" : "End Time"}
              </Label>
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="endTime"
                  name="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={handleChange}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Project */}
          <div className="grid gap-2">
            <Label htmlFor="project">
              {language === "de" ? "Projekt" : "Project"}
            </Label>
            <Select
              value={formData.projectId}
              onValueChange={(value) => handleSelectChange("projectId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={language === "de" ? "Projekt auswählen" : "Select project"} />
              </SelectTrigger>
              <SelectContent>
                {projects.map(project => (
                  <SelectItem key={project.id} value={project.id}>
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: project.color }}
                      />
                      {project.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Employee */}
          <div className="grid gap-2">
            <Label htmlFor="employee">
              {language === "de" ? "Mitarbeiter" : "Employee"}
            </Label>
            <Select
              value={formData.employeeId}
              onValueChange={(value) => handleSelectChange("employeeId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={language === "de" ? "Mitarbeiter auswählen" : "Select employee"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="self">
                  {language === "de" ? "Ich selbst" : "Myself"}
                </SelectItem>
                {employees.map(employee => (
                  <SelectItem key={employee.id} value={employee.id}>
                    {employee.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Billable */}
          <div className="flex items-center justify-between">
            <Label htmlFor="billable">
              {language === "de" ? "Abrechenbar" : "Billable"}
            </Label>
            <Switch
              id="billable"
              checked={formData.isBillable}
              onCheckedChange={handleSwitchChange}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            {language === "de" ? "Abbrechen" : "Cancel"}
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-transparent rounded-full"></div>
                {language === "de" ? "Speichern..." : "Saving..."}
              </div>
            ) : (
              language === "de" ? "Speichern" : "Save"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
