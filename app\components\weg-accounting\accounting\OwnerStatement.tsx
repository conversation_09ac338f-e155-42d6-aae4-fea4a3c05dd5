"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Download, FileText, User } from "lucide-react";
import { Property } from "../properties/PropertyList";
import { Owner, OwnerUnit } from "../owners/OwnerList";
import { AccountingPeriod, ExpenseCategory } from "./AccountingList";

interface OwnerStatementProps {
  accounting: AccountingPeriod;
  property: Property;
  owners: Owner[];
  expenseCategories: ExpenseCategory[];
}

export function OwnerStatement({ accounting, property, owners, expenseCategories }: OwnerStatementProps) {
  const { language } = useLanguage();
  
  // State für den ausgewählten Eigentümer
  const [selectedOwnerId, setSelectedOwnerId] = useState<string | null>(null);
  
  // Funktion zum Formatieren eines Betrags als Währung
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "de" ? "de-AT" : "en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };
  
  // Funktion zum Abrufen der umlagefähigen Ausgaben
  const getAllocatableExpenses = () => {
    return accounting.expenses.filter(expense => {
      const category = expenseCategories.find(c => c.id === expense.categoryId);
      return category ? category.isAllocatable : true;
    });
  };
  
  // Funktion zum Abrufen der nicht umlagefähigen Ausgaben
  const getNonAllocatableExpenses = () => {
    return accounting.expenses.filter(expense => {
      const category = expenseCategories.find(c => c.id === expense.categoryId);
      return category ? !category.isAllocatable : false;
    });
  };
  
  // Funktion zum Abrufen der Gesamtsumme der umlagefähigen Ausgaben
  const getTotalAllocatableExpenses = () => {
    return getAllocatableExpenses().reduce((total, expense) => total + expense.amount, 0);
  };
  
  // Funktion zum Abrufen der Gesamtsumme der nicht umlagefähigen Ausgaben
  const getTotalNonAllocatableExpenses = () => {
    return getNonAllocatableExpenses().reduce((total, expense) => total + expense.amount, 0);
  };
  
  // Funktion zum Abrufen des Kategorienamens anhand der ID
  const getCategoryName = (categoryId: string) => {
    const category = expenseCategories.find(c => c.id === categoryId);
    return category ? category.name : "Unbekannte Kategorie";
  };
  
  // Funktion zum Abrufen des Verteilerschlüsselnamens anhand der ID
  const getDistributionKeyName = (keyId: string) => {
    const key = property.distributionKeys.find(k => k.id === keyId);
    return key ? key.name : "Unbekannter Schlüssel";
  };
  
  // Funktion zum Abrufen des Verteilerschlüsseltyps anhand der ID
  const getDistributionKeyType = (keyId: string) => {
    const key = property.distributionKeys.find(k => k.id === keyId);
    return key ? key.type : "squareMeters";
  };
  
  // Funktion zum Berechnen des Anteils eines Eigentümers an einer Ausgabe
  const calculateOwnerShare = (expense: any, ownerUnits: OwnerUnit[]) => {
    const distributionKeyType = getDistributionKeyType(expense.distributionKeyId);
    const relevantUnits = ownerUnits.filter(unit => unit.propertyId === accounting.propertyId);
    
    if (relevantUnits.length === 0) return 0;
    
    let ownerShare = 0;
    
    switch (distributionKeyType) {
      case "squareMeters":
        // Verteilung nach Quadratmetern
        const totalArea = property.totalArea;
        const ownerArea = relevantUnits.reduce((total, unit) => total + unit.area, 0);
        ownerShare = (ownerArea / totalArea) * expense.amount;
        break;
      
      case "units":
        // Gleichmäßige Verteilung pro Wohneinheit
        ownerShare = (relevantUnits.length / property.units) * expense.amount;
        break;
      
      case "consumption":
        // Hier würde eine komplexere Logik für verbrauchsabhängige Verteilung stehen
        // Vereinfacht: Verteilung nach Quadratmetern
        const totalAreaConsumption = property.totalArea;
        const ownerAreaConsumption = relevantUnits.reduce((total, unit) => total + unit.area, 0);
        ownerShare = (ownerAreaConsumption / totalAreaConsumption) * expense.amount;
        break;
      
      case "personDays":
        // Hier würde eine komplexere Logik für personentageabhängige Verteilung stehen
        // Vereinfacht: Verteilung nach Quadratmetern
        const totalAreaPersonDays = property.totalArea;
        const ownerAreaPersonDays = relevantUnits.reduce((total, unit) => total + unit.area, 0);
        ownerShare = (ownerAreaPersonDays / totalAreaPersonDays) * expense.amount;
        break;
      
      case "custom":
        // Hier würde eine komplexere Logik für individuelle Verteilung stehen
        // Vereinfacht: Verteilung nach Eigentumsanteil
        const totalOwnership = 100;
        const ownerOwnership = relevantUnits.reduce((total, unit) => total + unit.ownershipPercentage, 0);
        ownerShare = (ownerOwnership / totalOwnership) * expense.amount;
        break;
      
      default:
        // Standardmäßig Verteilung nach Quadratmetern
        const totalAreaDefault = property.totalArea;
        const ownerAreaDefault = relevantUnits.reduce((total, unit) => total + unit.area, 0);
        ownerShare = (ownerAreaDefault / totalAreaDefault) * expense.amount;
        break;
    }
    
    return ownerShare;
  };
  
  // Funktion zum Berechnen des Anteils eines Eigentümers an der Instandhaltungsrücklage
  const calculateOwnerMaintenanceReserveShare = (owner: Owner) => {
    const relevantUnits = owner.units.filter(unit => unit.propertyId === accounting.propertyId);
    
    if (relevantUnits.length === 0) return 0;
    
    // Verteilung nach Eigentumsanteil
    const totalOwnership = 100;
    const ownerOwnership = relevantUnits.reduce((total, unit) => total + unit.ownershipPercentage, 0);
    
    return (ownerOwnership / totalOwnership) * accounting.maintenanceReserveContribution;
  };
  
  // Funktion zum Berechnen der Gesamtkosten eines Eigentümers
  const calculateOwnerTotalCosts = (owner: Owner) => {
    const allocatableExpenses = getAllocatableExpenses();
    const maintenanceReserveShare = calculateOwnerMaintenanceReserveShare(owner);
    
    let totalCosts = 0;
    
    // Summe der Anteile an den umlagefähigen Ausgaben
    allocatableExpenses.forEach(expense => {
      totalCosts += calculateOwnerShare(expense, owner.units);
    });
    
    // Hinzufügen des Anteils an der Instandhaltungsrücklage
    totalCosts += maintenanceReserveShare;
    
    return totalCosts;
  };
  
  // Funktion zum Berechnen der haushaltsnahen Dienstleistungen eines Eigentümers
  const calculateOwnerHouseholdRelatedServices = (owner: Owner) => {
    const relevantUnits = owner.units.filter(unit => unit.propertyId === accounting.propertyId);
    
    if (relevantUnits.length === 0) return 0;
    
    // Verteilung nach Eigentumsanteil
    const totalOwnership = 100;
    const ownerOwnership = relevantUnits.reduce((total, unit) => total + unit.ownershipPercentage, 0);
    
    const totalHouseholdRelatedServices = accounting.expenses.reduce(
      (total, expense) => total + expense.householdRelatedAmount, 
      0
    );
    
    return (ownerOwnership / totalOwnership) * totalHouseholdRelatedServices;
  };
  
  // Funktion zum Berechnen der Handwerkerleistungen eines Eigentümers
  const calculateOwnerCraftsmanServices = (owner: Owner) => {
    const relevantUnits = owner.units.filter(unit => unit.propertyId === accounting.propertyId);
    
    if (relevantUnits.length === 0) return 0;
    
    // Verteilung nach Eigentumsanteil
    const totalOwnership = 100;
    const ownerOwnership = relevantUnits.reduce((total, unit) => total + unit.ownershipPercentage, 0);
    
    const totalCraftsmanServices = accounting.expenses.reduce(
      (total, expense) => total + expense.craftsmanAmount, 
      0
    );
    
    return (ownerOwnership / totalOwnership) * totalCraftsmanServices;
  };
  
  // Filtere Eigentümer, die Einheiten in diesem Objekt haben
  const relevantOwners = owners.filter(owner => 
    owner.units.some(unit => unit.propertyId === accounting.propertyId)
  );
  
  // Ausgewählter Eigentümer
  const selectedOwner = selectedOwnerId 
    ? relevantOwners.find(owner => owner.id === selectedOwnerId) 
    : null;
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">
          {language === "de" ? "Eigentümerabrechnungen" : "Owner Statements"}
        </h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {relevantOwners.map((owner) => (
          <Card 
            key={owner.id} 
            className={`overflow-hidden cursor-pointer hover:border-primary transition-colors ${selectedOwnerId === owner.id ? 'border-primary' : ''}`}
            onClick={() => setSelectedOwnerId(owner.id === selectedOwnerId ? null : owner.id)}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-base">{owner.firstName} {owner.lastName}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {language === "de" ? "Anteil an Kosten" : "Share of Costs"}
                  </span>
                  <span className="font-medium">{formatCurrency(calculateOwnerTotalCosts(owner))}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {language === "de" ? "Anteil an Rücklage" : "Share of Reserve"}
                  </span>
                  <span className="font-medium">{formatCurrency(calculateOwnerMaintenanceReserveShare(owner))}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {language === "de" ? "Einheiten" : "Units"}
                  </span>
                  <span className="font-medium">{owner.units.filter(unit => unit.propertyId === accounting.propertyId).length}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {selectedOwner && (
        <div className="mt-6 space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Abrechnung für " : "Statement for "} {selectedOwner.firstName} {selectedOwner.lastName}
            </h3>
            
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              {language === "de" ? "Exportieren" : "Export"}
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">
                  {language === "de" ? "Zusammenfassung" : "Summary"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Anteil an Betriebskosten" : "Share of Operating Costs"}
                    </span>
                    <span className="font-medium">
                      {formatCurrency(calculateOwnerTotalCosts(selectedOwner) - calculateOwnerMaintenanceReserveShare(selectedOwner))}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Anteil an Rücklage" : "Share of Reserve"}
                    </span>
                    <span className="font-medium">{formatCurrency(calculateOwnerMaintenanceReserveShare(selectedOwner))}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between font-medium">
                    <span>
                      {language === "de" ? "Gesamtbetrag" : "Total Amount"}
                    </span>
                    <span>{formatCurrency(calculateOwnerTotalCosts(selectedOwner))}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">
                  {language === "de" ? "Einheiten" : "Units"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {selectedOwner.units
                    .filter(unit => unit.propertyId === accounting.propertyId)
                    .map((unit, index) => (
                      <div key={unit.id} className="space-y-1">
                        <div className="font-medium">{unit.unitNumber}</div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">
                            {language === "de" ? "Fläche" : "Area"}
                          </span>
                          <span>{unit.area} m²</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">
                            {language === "de" ? "Eigentumsanteil" : "Ownership"}
                          </span>
                          <span>{unit.ownershipPercentage}%</span>
                        </div>
                        {index < selectedOwner.units.filter(unit => unit.propertyId === accounting.propertyId).length - 1 && (
                          <div className="border-t my-2"></div>
                        )}
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">
                  {language === "de" ? "Steuerlich absetzbar" : "Tax Deductible"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Haushaltsnahe Dienstleistungen" : "Household-Related Services"}
                    </span>
                    <span className="font-medium">{formatCurrency(calculateOwnerHouseholdRelatedServices(selectedOwner))}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Handwerkerleistungen" : "Craftsman Services"}
                    </span>
                    <span className="font-medium">{formatCurrency(calculateOwnerCraftsmanServices(selectedOwner))}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">
                {language === "de" ? "Kostenaufstellung" : "Cost Breakdown"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{language === "de" ? "Kategorie" : "Category"}</TableHead>
                    <TableHead>{language === "de" ? "Beschreibung" : "Description"}</TableHead>
                    <TableHead>{language === "de" ? "Verteilerschlüssel" : "Distribution Key"}</TableHead>
                    <TableHead className="text-right">{language === "de" ? "Gesamtbetrag" : "Total Amount"}</TableHead>
                    <TableHead className="text-right">{language === "de" ? "Ihr Anteil" : "Your Share"}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getAllocatableExpenses().map((expense) => (
                    <TableRow key={expense.id}>
                      <TableCell>{getCategoryName(expense.categoryId)}</TableCell>
                      <TableCell>{expense.description}</TableCell>
                      <TableCell>{getDistributionKeyName(expense.distributionKeyId)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(expense.amount)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(calculateOwnerShare(expense, selectedOwner.units))}</TableCell>
                    </TableRow>
                  ))}
                  <TableRow>
                    <TableCell colSpan={3} className="font-medium">
                      {language === "de" ? "Instandhaltungsrücklage" : "Maintenance Reserve"}
                    </TableCell>
                    <TableCell className="text-right">{formatCurrency(accounting.maintenanceReserveContribution)}</TableCell>
                    <TableCell className="text-right">{formatCurrency(calculateOwnerMaintenanceReserveShare(selectedOwner))}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell colSpan={3} className="font-medium">
                      {language === "de" ? "Gesamtsumme" : "Total"}
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(getTotalAllocatableExpenses() + accounting.maintenanceReserveContribution)}
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(calculateOwnerTotalCosts(selectedOwner))}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
