import { Loader2 } from "lucide-react";

interface LoadingStateProps {
  title?: string;
  description?: string;
  fullPage?: boolean;
}

export function LoadingState({
  title = "Wird geladen...",
  description = "Bitte warten <PERSON>, während die Daten geladen werden.",
  fullPage = false,
}: LoadingStateProps) {
  return (
    <div className={`flex flex-col flex-1 ${fullPage ? 'h-screen' : 'h-[500px]'} items-center justify-center rounded-md border-2 border-dashed border-primary p-8 text-center animate-in fade-in-50 bg-primary/5`}>
      <div className="flex items-center justify-center size-24 rounded-full bg-primary/20">
        <Loader2 className="size-12 text-primary animate-spin" />
      </div>
      <h2 className="mt-6 text-2xl font-semibold text-primary">{title}</h2>
      <p className="mb-8 mt-2 text-lg text-muted-foreground max-w-sm mx-auto text-center">
        {description}
      </p>
    </div>
  );
}
