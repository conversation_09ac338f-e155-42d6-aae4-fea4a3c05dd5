"use server";

import { revalidatePath } from "next/cache";
import { requireUser } from "@/app/utils/hooks";
import prisma from "@/app/utils/db";
import { Property, DistributionKey, Owner, OwnerUnit, AccountingPeriod, Expense, ExpenseCategory } from "../lib/wegTypes";

// Removed temporary storage - all data now comes from database

// Property-Funktionen
export async function getProperties(): Promise<Property[]> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    // Fetch properties from the database
    const dbProperties = await prisma.property.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Fetch distribution keys from database for each property
    const properties: Property[] = await Promise.all(
      dbProperties.map(async (prop) => {
        const distributionKeys = await prisma.distributionKey.findMany({
          where: {
            propertyId: prop.id,
            userId,
          },
          orderBy: {
            createdAt: "asc",
          },
        });

        // If no distribution keys exist, create a default one
        let finalDistributionKeys = distributionKeys;
        if (distributionKeys.length === 0) {
          const defaultKey = await prisma.distributionKey.create({
            data: {
              userId,
              propertyId: prop.id,
              name: "Wohnfläche",
              type: "squareMeters",
              description: "Verteilung nach Quadratmetern der Wohnfläche",
            },
          });
          finalDistributionKeys = [defaultKey];
        }

        return {
          id: prop.id,
          name: prop.name,
          address: prop.address,
          units: prop.unitCount,
          totalArea: prop.totalArea,
          yearOfConstruction: prop.yearOfConstruction,
          distributionKeys: finalDistributionKeys.map((dk) => ({
            id: dk.id,
            name: dk.name,
            type: dk.type as "squareMeters" | "units" | "consumption" | "personDays" | "custom",
            description: dk.description || "",
          })),
        };
      })
    );

    return properties;
  } catch (error) {
    console.error("Failed to fetch properties:", error);
    throw new Error("Failed to fetch properties");
  }
}

export async function addProperty(propertyData: Omit<Property, "id" | "distributionKeys">): Promise<Property> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    // Create property in the database
    const dbProperty = await prisma.property.create({
      data: {
        name: propertyData.name,
        address: propertyData.address,
        unitCount: propertyData.units,
        totalArea: propertyData.totalArea,
        yearOfConstruction: propertyData.yearOfConstruction,
        user: {
          connect: {
            id: userId,
          },
        },
      },
    });

    // Return the property with default distributionKeys
    const newProperty: Property = {
      id: dbProperty.id,
      name: dbProperty.name,
      address: dbProperty.address,
      units: dbProperty.unitCount,
      totalArea: dbProperty.totalArea,
      yearOfConstruction: dbProperty.yearOfConstruction,
      distributionKeys: [
        {
          id: "1",
          name: "Wohnfläche",
          type: "squareMeters",
          description: "Verteilung nach Quadratmetern der Wohnfläche"
        }
      ]
    };

    revalidatePath("/weg-accounting/dashboard/properties");
    return newProperty;
  } catch (error) {
    console.error("Failed to add property:", error);
    throw new Error("Failed to add property");
  }
}

export async function updateProperty(propertyId: string, propertyData: Partial<Property>): Promise<Property> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    // Update property in the database
    const dbProperty = await prisma.property.update({
      where: {
        id: propertyId,
        userId,
      },
      data: {
        name: propertyData.name,
        address: propertyData.address,
        unitCount: propertyData.units,
        totalArea: propertyData.totalArea,
        yearOfConstruction: propertyData.yearOfConstruction,
      },
    });

    // Return the updated property with default distributionKeys
    const updatedProperty: Property = {
      id: dbProperty.id,
      name: dbProperty.name,
      address: dbProperty.address,
      units: dbProperty.unitCount,
      totalArea: dbProperty.totalArea,
      yearOfConstruction: dbProperty.yearOfConstruction,
      distributionKeys: [
        {
          id: "1",
          name: "Wohnfläche",
          type: "squareMeters",
          description: "Verteilung nach Quadratmetern der Wohnfläche"
        }
      ]
    };

    revalidatePath("/weg-accounting/dashboard/properties");
    return updatedProperty;
  } catch (error) {
    console.error("Failed to update property:", error);
    throw new Error("Failed to update property");
  }
}

export async function deleteProperty(propertyId: string): Promise<void> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    // Delete property from the database
    await prisma.property.delete({
      where: {
        id: propertyId,
        userId,
      },
    });

    revalidatePath("/weg-accounting/dashboard/properties");
  } catch (error) {
    console.error("Failed to delete property:", error);
    throw new Error("Failed to delete property");
  }
}

// Owner-Funktionen
export async function getOwners(): Promise<Owner[]> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    // Eigentümer aus der Datenbank laden
    const dbOwners = await prisma.owner.findMany({
      where: {
        userId,
      },
      include: {
        units: {
          include: {
            property: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        lastName: "asc",
      },
    });

    // Transform to match the expected Owner interface
    return dbOwners.map((owner: any) => ({
      id: owner.id,
      firstName: owner.firstName,
      lastName: owner.lastName,
      email: owner.email,
      phone: owner.phone || "",
      address: owner.address || "",
      units: owner.units.map((unit: any) => ({
        id: unit.id,
        propertyId: unit.propertyId,
        unitNumber: unit.unitNumber,
        area: unit.area,
        ownershipPercentage: unit.ownershipPercentage,
      })),
    }));
  } catch (error) {
    console.error("Failed to fetch owners:", error);
    // Falls die Datenbank noch nicht bereit ist, leeres Array zurückgeben
    return [];
  }
}

export async function addOwner(ownerData: Omit<Owner, "id" | "units">): Promise<Owner> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    const newOwner = await prisma.owner.create({
      data: {
        userId,
        firstName: ownerData.firstName,
        lastName: ownerData.lastName,
        email: ownerData.email,
        phone: ownerData.phone || null,
        address: ownerData.address || null,
      },
      include: {
        units: {
          include: {
            property: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    revalidatePath("/weg-accounting/dashboard/owners");

    return {
      id: newOwner.id,
      firstName: newOwner.firstName,
      lastName: newOwner.lastName,
      email: newOwner.email,
      phone: newOwner.phone || "",
      address: newOwner.address || "",
      units: [],
    };
  } catch (error) {
    console.error("Failed to add owner:", error);
    throw new Error("Failed to add owner");
  }
}

export async function updateOwner(ownerId: string, ownerData: Partial<Owner>): Promise<Owner> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    const existingOwner = await prisma.owner.findFirst({
      where: {
        id: ownerId,
        userId,
      },
    });

    if (!existingOwner) {
      throw new Error("Owner not found");
    }

    const updatedOwner = await prisma.owner.update({
      where: {
        id: ownerId,
      },
      data: {
        firstName: ownerData.firstName || existingOwner.firstName,
        lastName: ownerData.lastName || existingOwner.lastName,
        email: ownerData.email || existingOwner.email,
        phone: ownerData.phone || existingOwner.phone,
        address: ownerData.address || existingOwner.address,
      },
      include: {
        units: {
          include: {
            property: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    const result: Owner = {
      id: updatedOwner.id,
      firstName: updatedOwner.firstName,
      lastName: updatedOwner.lastName,
      email: updatedOwner.email,
      phone: updatedOwner.phone || "",
      address: updatedOwner.address || "",
      units: updatedOwner.units.map((unit: any) => ({
        id: unit.id,
        propertyId: unit.propertyId,
        unitNumber: unit.unitNumber,
        area: unit.area,
        ownershipPercentage: unit.ownershipPercentage,
      })),
    };

    revalidatePath("/weg-accounting/dashboard/owners");
    return result;
  } catch (error) {
    console.error("Failed to update owner:", error);
    throw new Error("Failed to update owner");
  }
}

export async function deleteOwner(ownerId: string): Promise<void> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    const existingOwner = await prisma.owner.findFirst({
      where: {
        id: ownerId,
        userId,
      },
    });

    if (!existingOwner) {
      throw new Error("Owner not found");
    }

    await prisma.owner.delete({
      where: {
        id: ownerId,
      },
    });

    revalidatePath("/weg-accounting/dashboard/owners");
  } catch (error) {
    console.error("Failed to delete owner:", error);
    throw new Error("Failed to delete owner");
  }
}

// Invoice-Funktionen
export async function getInvoices(): Promise<any[]> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    // Fetch expenses (invoices) with related data from database
    const expenses = await prisma.expense.findMany({
      where: {
        accountingPeriod: {
          userId,
        },
      },
      include: {
        category: true,
        accountingPeriod: {
          include: {
            property: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    });

    // Transform to match the expected Invoice interface
    const invoices = expenses.map((expense) => ({
      id: expense.id,
      propertyId: expense.accountingPeriod.propertyId,
      number: `RE-${expense.date.getFullYear()}-${expense.id.slice(-3)}`,
      date: expense.date.toISOString().split("T")[0],
      dueDate: new Date(expense.date.getTime() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      vendorName: expense.description.split(" - ")[0] || "Unknown Vendor",
      description: expense.description.split(" - ").slice(1).join(" - ") || expense.description,
      amount: expense.amount,
      status: "pending" as const,
      categoryId: expense.categoryId,
      notes: "",
    }));

    return invoices;
  } catch (error) {
    console.error("Failed to fetch invoices from database:", error);
    return [];
  }
}

export async function addInvoice(invoiceData: any): Promise<any> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

    // First, find or create an accounting period for the property and current year
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1);
    const endDate = new Date(currentYear, 11, 31);

    let accountingPeriod = await prisma.accountingPeriod.findFirst({
      where: {
        userId,
        propertyId: invoiceData.propertyId,
        year: currentYear,
      },
    });

    if (!accountingPeriod) {
      accountingPeriod = await prisma.accountingPeriod.create({
        data: {
          userId,
          propertyId: invoiceData.propertyId,
          year: currentYear,
          startDate,
          endDate,
          status: "draft",
        },
      });
    }

    // Create the expense (invoice) in database
    const expense = await prisma.expense.create({
      data: {
        accountingPeriodId: accountingPeriod.id,
        categoryId: invoiceData.categoryId,
        description: `${invoiceData.vendorName} - ${invoiceData.description}`,
        amount: parseFloat(invoiceData.amount),
        date: new Date(invoiceData.date),
      },
      include: {
        category: true,
        accountingPeriod: {
          include: {
            property: true,
          },
        },
      },
    });

    // Transform to match the expected Invoice interface
    const invoice = {
      id: expense.id,
      propertyId: expense.accountingPeriod.propertyId,
      number: `RE-${expense.date.getFullYear()}-${expense.id.slice(-3)}`,
      date: expense.date.toISOString().split("T")[0],
      dueDate: new Date(expense.date.getTime() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      vendorName: invoiceData.vendorName,
      description: invoiceData.description,
      amount: expense.amount,
      status: "pending" as const,
      categoryId: expense.categoryId,
      notes: invoiceData.notes || "",
    };

    revalidatePath("/weg-accounting/dashboard/invoices");
    return invoice;
  } catch (error) {
    console.error("Failed to add invoice to database:", error);
    throw new Error("Failed to add invoice");
  }
}

export async function updateInvoice(invoiceId: string, invoiceData: any): Promise<any> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    // Update the expense in database
    const expense = await prisma.expense.update({
      where: {
        id: invoiceId,
        accountingPeriod: {
          userId,
        },
      },
      data: {
        categoryId: invoiceData.categoryId,
        description: `${invoiceData.vendorName} - ${invoiceData.description}`,
        amount: parseFloat(invoiceData.amount),
        date: new Date(invoiceData.date),
      },
      include: {
        category: true,
        accountingPeriod: {
          include: {
            property: true,
          },
        },
      },
    });

    // Transform to match the expected Invoice interface
    const invoice = {
      id: expense.id,
      propertyId: expense.accountingPeriod.propertyId,
      number: `RE-${expense.date.getFullYear()}-${expense.id.slice(-3)}`,
      date: expense.date.toISOString().split("T")[0],
      dueDate: new Date(expense.date.getTime() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      vendorName: invoiceData.vendorName,
      description: invoiceData.description,
      amount: expense.amount,
      status: invoiceData.status || "pending",
      categoryId: expense.categoryId,
      notes: invoiceData.notes || "",
    };

    revalidatePath("/weg-accounting/dashboard/invoices");
    return invoice;
  } catch (error) {
    console.error("Failed to update invoice in database:", error);
    throw new Error("Failed to update invoice");
  }
}

export async function deleteInvoice(invoiceId: string): Promise<void> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  const userId = session.user.id; // Extract to ensure TypeScript knows it's defined

  try {
    // Delete the expense from database
    await prisma.expense.delete({
      where: {
        id: invoiceId,
        accountingPeriod: {
          userId,
        },
      },
    });

    revalidatePath("/weg-accounting/dashboard/invoices");
  } catch (error) {
    console.error("Failed to delete invoice from database:", error);
    throw new Error("Failed to delete invoice");
  }
}

// Weitere Funktionen für AccountingPeriod, Expense, ExpenseCategory usw. folgen in der nächsten Datei
