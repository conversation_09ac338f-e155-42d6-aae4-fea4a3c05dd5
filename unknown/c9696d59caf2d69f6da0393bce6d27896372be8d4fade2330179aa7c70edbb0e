import prisma from "../utils/db";
import { requireUser } from "../utils/hooks";
import { RecentInvoicesClient } from "./RecentInvoicesClient";

async function getData(userId: string) {
  const data = await prisma.invoice.findMany({
    where: {
      userId: userId,
    },
    select: {
      id: true,
      clientName: true,
      clientEmail: true,
      total: true,
      currency: true,
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 7,
  });

  return data;
}

export async function RecentInvoices() {
  const session = await requireUser();
  const data = await getData(session.user?.id as string);
  return <RecentInvoicesClient data={data} />;
}
