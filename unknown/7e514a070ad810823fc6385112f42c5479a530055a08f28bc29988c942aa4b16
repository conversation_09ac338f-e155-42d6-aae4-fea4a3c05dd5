"use client";

import { useState } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MoreHorizontal, Search, Edit, Trash2 } from "lucide-react";
import { Employee } from "@/app/lib/definitions";
import { deleteEmployee } from "@/app/actions/employees";
import { useRouter } from "next/navigation";
import { useToast } from "@/app/context/ToastContext";
import { TranslationKey } from "@/i18n";

export function EmployeeList({ employees }: { employees: Employee[] }) {
  const router = useRouter();
  const { t } = useLanguage();
  // We don't need mounted state for this component
  const [searchQuery, setSearchQuery] = useState("");
  const { toast } = useToast();

  // Filter employees based on search query
  const filteredEmployees = employees.filter(employee =>
    employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.department.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle employee deletion
  const handleDeleteEmployee = async (id: string) => {
    if (confirm(t('confirmDelete' as TranslationKey) || "Möchten Sie diesen Mitarbeiter wirklich löschen?")) {
      try {
        await deleteEmployee(id);

        toast.success(
          t('employeeDeleted' as TranslationKey) || "Mitarbeiter gelöscht",
          {
            description: t('employeeDeletedSuccess' as TranslationKey) || "Der Mitarbeiter wurde erfolgreich gelöscht."
          }
        );

        router.refresh();
      } catch (error) {
        console.error("Failed to delete employee:", error);

        toast.error(
          t('error' as TranslationKey) || "Fehler",
          {
            description: t('employeeDeleteFailed' as TranslationKey) || "Mitarbeiter konnte nicht gelöscht werden. Bitte versuchen Sie es erneut."
          }
        );
      }
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('employees' as TranslationKey) || "Mitarbeiter"}</CardTitle>
        <CardDescription>
          {t('manageEmployees' as TranslationKey) || "Verwalten Sie die Mitarbeiter Ihres Unternehmens"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder={t('searchEmployees' as TranslationKey) || "Mitarbeiter suchen..."}
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {filteredEmployees.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('name' as TranslationKey) || "Name"}</TableHead>
                <TableHead>{t('position' as TranslationKey) || "Position"}</TableHead>
                <TableHead>{t('department' as TranslationKey) || "Abteilung"}</TableHead>
                <TableHead>{t('startDate' as TranslationKey) || "Startdatum"}</TableHead>
                <TableHead>{t('salary' as TranslationKey) || "Gehalt"}</TableHead>
                <TableHead className="text-right">{t('actions' as TranslationKey) || "Aktionen"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEmployees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell className="font-medium">{employee.name}</TableCell>
                  <TableCell>{employee.position}</TableCell>
                  <TableCell>{employee.department}</TableCell>
                  <TableCell>
                    {new Date(employee.startDate).toLocaleDateString('de-DE')}
                  </TableCell>
                  <TableCell>
                    {new Intl.NumberFormat('de-DE', {
                      style: 'currency',
                      currency: 'EUR'
                    }).format(typeof employee.salary === 'string' ? parseFloat(employee.salary) : employee.salary)}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>
                          {t('actions' as TranslationKey) || "Aktionen"}
                        </DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => router.push(`/dashboard/employees/${employee.id}`)}>
                          <Edit className="mr-2 h-4 w-4" />
                          {t('edit' as TranslationKey) || "Bearbeiten"}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleDeleteEmployee(employee.id)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          {t('delete' as TranslationKey) || "Löschen"}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-6">
            <p className="text-muted-foreground">
              {searchQuery
                ? (t('noEmployeesFound' as TranslationKey) || "Keine passenden Mitarbeiter gefunden.")
                : (t('noEmployees' as TranslationKey) || "Noch keine Mitarbeiter hinzugefügt.")}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}