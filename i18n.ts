export type LanguageKey = "en" | "de";

export type TranslationKey =
  | "error"
  | "pageNotFound"
  | "goBackHome"
  | "switchLanguage"
  | "totalRevenue"
  | "basedOnTotalVolume"
  | "totalInvoicesIssued"
  | "totalInvoicesIssuedDesc"
  | "paidInvoices"
  | "paidInvoicesDesc"
  | "pendingInvoices"
  | "pendingInvoicesDesc"
  | "currentLanguage"
  | "selectLanguage"
  | "english"
  | "german"
  | "recentInvoices"
  // Navigation
  | "dashboard"
  | "employees"
  | "invoices"
  | "clients"
  | "settings"
  | "logout"
  | "profile"
  | "ai"
  | "aiAssistant"
  | "taxReturn"
  | "taxReturnAssistant"
  | "aiChat"
  | "aiTaxOptimization"
  | "aiChatDescription"
  | "aiTaxOptimizationDescription"
  | "wegAccounting"
  | "timeTracking"
  // Tab Visibility Settings
  | "navigationTabs"
  | "chooseVisibleTabs"
  | "alwaysVisible"
  | "saveChanges"
  | "saving"
  | "editTabs"
  | "cancel"
  | "useDialogEditor"
  | "useShakingEditor"
  // AI Chat
  | "aiChatWelcome"
  | "aiChatPlaceholder"
  | "aiChatSend"
  // Tax Optimization
  | "aiTaxOptimizationReady"
  | "aiTaxOptimizationReadyDesc"
  | "aiTaxOptimizationAnalyze"
  | "aiTaxOptimizationAnalyzing"
  | "aiTaxOptimizationAnalyzingDesc"
  | "aiTaxOptimizationResults"
  | "aiTaxOptimizationRefresh"
  | "aiTaxOptimizationPotentialSavings"
  | "aiTaxOptimizationLearnMore"
  | "aiTaxOptimizationDifficultyEasy"
  | "aiTaxOptimizationDifficultyMedium"
  | "aiTaxOptimizationDifficultyAdvanced"
  // Finance and Income/Expenses
  | "income-expenses"
  | "income"
  | "expenses"
  | "financialOverview"
  | "detailedIncomeOverview"
  | "detailedExpensesOverview"
  | "description"
  | "date"
  | "netAmount"
  | "vatAmount"
  | "grossAmount"
  | "total"
  | "subtotalNet"
  | "vat"
  | "vatRate"
  | "totalGross"
  | "itemQuantity"
  | "unitPrice"
  // Hero section
  | "introducing"
  | "invoicingMade"
  | "superEasy"
  | "creatingInvoices"
  | "getStarted"
  | "learnMore"
  // Auth
  | "signIn"
  | "signUp"
  | "email"
  | "password"
  | "forgotPassword"
  | "noAccount"
  | "myAccount"
  | "alreadyHaveAccount"
  | "manageInvoices"
  | "createInvoice"
  // Invoice actions
  | "markAsPaid"
  | "markAsPaidDesc"
  | "markAsPaidButton"
  | "cancel"
  | "deleteInvoice"
  | "deleteInvoiceDesc"
  | "deleteInvoiceButton"
  // Theme settings
  | "appearance"
  | "theme"
  | "light"
  | "dark"
  | "system"
  | "switchToLight"
  | "switchToDark"
  | "switchToSystem"  | "userSettings"
  | "settingsSaved"
  | "settingsError"
  | "getStarted";

export const translations: Record<LanguageKey, Record<TranslationKey, string>> = {
  en: {
    error: "An error occurred",
    pageNotFound: "The page you're looking for does not exist",
    goBackHome: "Go back home",
    switchLanguage: "Switch Language",
    totalRevenue: "Total Revenue",
    basedOnTotalVolume: "Based on total volume",
    totalInvoicesIssued: "Total Invoices Issued",
    totalInvoicesIssuedDesc: "Total Invoices Issued!",
    paidInvoices: "Paid Invoices",
    paidInvoicesDesc: "Total Invoices which have been paid!",
    pendingInvoices: "Pending Invoices",
    pendingInvoicesDesc: "Invoices which are currently pending!",
    currentLanguage: "Current language",
    selectLanguage: "Select language",
    english: "English",
    german: "German",
    recentInvoices: "Recent Invoices",
    // Navigation
    dashboard: "Dashboard",
    income: "Einnahmen & Ausgaben",
    invoices: "Invoices",
    employees: "Employees",
    clients: "Clients",
    settings: "Settings",
    logout: "Logout",
    profile: "Profile",
    ai: "AI Assistant",
    aiAssistant: "AI Tax Advisor",
    taxReturn: "Tax Return",
    taxReturnAssistant: "Austrian Tax Return Guide",
    wegAccounting: "Property Management",
    aiChat: "Chat",
    aiTaxOptimization: "Tax Optimization",
    aiChatDescription: "Ask questions about taxes, invoicing, or business finances",
    aiTaxOptimizationDescription: "Get personalized suggestions to optimize your tax situation",
    // AI Chat
    aiChatWelcome: "Hello! I'm your AI tax assistant. How can I help you today?",
    aiChatPlaceholder: "Type your message...",
    aiChatSend: "Send",
    // Tax Optimization
    aiTaxOptimizationReady: "Ready to optimize your taxes?",
    aiTaxOptimizationReadyDesc: "Our AI will analyze your financial data and provide personalized tax-saving suggestions.",
    aiTaxOptimizationAnalyze: "Analyze My Tax Situation",
    aiTaxOptimizationAnalyzing: "Analyzing your financial data...",
    aiTaxOptimizationAnalyzingDesc: "This may take a moment",
    aiTaxOptimizationResults: "Your Tax Optimization Suggestions",
    aiTaxOptimizationRefresh: "Refresh Analysis",
    aiTaxOptimizationPotentialSavings: "Potential savings:",
    aiTaxOptimizationLearnMore: "Learn more",
    aiTaxOptimizationDifficultyEasy: "Easy",
    aiTaxOptimizationDifficultyMedium: "Medium",
    aiTaxOptimizationDifficultyAdvanced: "Advanced",
    // Finance and Income/Expenses
    "income-expenses": "Income & Expenses",
    expenses: "Expenses",
    financialOverview: "Financial Overview",
    detailedIncomeOverview: "Detailed Income Overview",
    detailedExpensesOverview: "Detailed Expenses Overview",
    description: "Description",
    date: "Date",
    netAmount: "Net Amount (€)",
    vatAmount: "VAT (€)",
    grossAmount: "Gross Amount (€)",
    total: "Total",
    subtotalNet: "Subtotal (Net)",
    vat: "VAT",
    vatRate: "VAT Rate",
    totalGross: "Total Amount (Gross)",
    itemQuantity: "Quantity",
    unitPrice: "Unit Price",
    // Hero section
    introducing: "Introducing TaxMate 1.0",
    invoicingMade: "Invoicing made",
    superEasy: "super easy!",
    creatingInvoices: "Creating Invoices can be a pain! We at TaxMate make it super easy.",
    getStarted: "Get Started",
    learnMore: "Learn More",
    // Auth
    signIn: "Sign In",
    signUp: "Sign Up",
    email: "Email",
    password: "Password",
    forgotPassword: "Forgot Password?",
    noAccount: "Don't have an account?",
    myAccount: "My Account",
    alreadyHaveAccount: "Already have an account?",
    manageInvoices: "Manage your invoices right here",
    createInvoice: "Create Invoice",
    // Invoice actions
    markAsPaid: "Mark as Paid?",
    markAsPaidDesc: "Are you sure you want to mark this invoice as paid?",
    markAsPaidButton: "Mark as Paid!",
    cancel: "Cancel",
    deleteInvoice: "Delete Invoice",
    deleteInvoiceDesc: "Are you sure that you want to delete this invoice?",
    deleteInvoiceButton: "Delete Invoice",
    // Theme settings
    appearance: "Appearance",
    theme: "Theme",
    light: "Light",
    dark: "Dark",
    system: "System",
    switchToLight: "Switch to light mode",
    switchToDark: "Switch to dark mode",
    switchToSystem: "Switch to system preference",
    userSettings: "User Settings",
    settingsSaved: "Settings saved successfully",
    settingsError: "Failed to save settings",
    // Tab Visibility Settings
    navigationTabs: "Navigation Tabs",
    chooseVisibleTabs: "Choose which tabs to show in the navigation",
    alwaysVisible: "always visible",
    saveChanges: "Save Changes",
    saving: "Saving...",
    editTabs: "Edit Tabs",
    useDialogEditor: "Use Dialog Editor",    useShakingEditor: "Use Shaking Editor",
    // Time Tracking
    timeTracking: "Time Tracking",
  },
  de: {
    error: "Ein Fehler ist aufgetreten",
    pageNotFound: "Die gesuchte Seite existiert nicht",
    goBackHome: "Zurück zur Startseite",
    switchLanguage: "Sprache wechseln",
    totalRevenue: "Gesamtumsatz",
    basedOnTotalVolume: "Basierend auf dem Gesamtvolumen",
    totalInvoicesIssued: "Ausgestellte Rechnungen",
    totalInvoicesIssuedDesc: "Gesamtanzahl der ausgestellten Rechnungen!",
    paidInvoices: "Bezahlte Rechnungen",
    paidInvoicesDesc: "Alle Rechnungen, die bereits bezahlt wurden!",
    pendingInvoices: "Ausstehende Rechnungen",
    pendingInvoicesDesc: "Rechnungen, die derzeit noch ausstehen!",
    currentLanguage: "Aktuelle Sprache",
    selectLanguage: "Sprache auswählen",
    english: "Englisch",
    german: "Deutsch",
    recentInvoices: "Neueste Rechnungen",
    // Navigation
    employees: "Mitarbeiter",
    dashboard: "Dashboard",
    invoices: "Rechnungen",
    clients: "Kunden",
    settings: "Einstellungen",
    logout: "Abmelden",
    profile: "Profil",
    ai: "KI-Assistent",
    aiAssistant: "KI-Steuerberater",
    taxReturn: "Steuerausgleich",
    taxReturnAssistant: "Österreichischer Steuerausgleich Leitfaden",
    wegAccounting: "WEG-Abrechnung",
    aiChat: "Chat",
    aiTaxOptimization: "Steueroptimierung",
    aiChatDescription: "Stellen Sie Fragen zu Steuern, Rechnungsstellung oder Unternehmensfinanzen",
    aiTaxOptimizationDescription: "Erhalten Sie personalisierte Vorschläge zur Optimierung Ihrer Steuersituation",
    // AI Chat
    aiChatWelcome: "Hallo! Ich bin Ihr KI-Steuerassistent. Wie kann ich Ihnen heute helfen?",
    aiChatPlaceholder: "Nachricht eingeben...",
    aiChatSend: "Senden",
    // Tax Optimization
    aiTaxOptimizationReady: "Bereit, Ihre Steuern zu optimieren?",
    aiTaxOptimizationReadyDesc: "Unsere KI analysiert Ihre Finanzdaten und gibt personalisierte Vorschläge zur Steuerersparnis.",
    aiTaxOptimizationAnalyze: "Meine Steuersituation analysieren",
    aiTaxOptimizationAnalyzing: "Analyse Ihrer Finanzdaten...",
    aiTaxOptimizationAnalyzingDesc: "Dies kann einen Moment dauern",
    aiTaxOptimizationResults: "Ihre Steueroptimierungsvorschläge",
    aiTaxOptimizationRefresh: "Analyse aktualisieren",
    aiTaxOptimizationPotentialSavings: "Mögliche Einsparungen:",
    aiTaxOptimizationLearnMore: "Mehr erfahren",
    aiTaxOptimizationDifficultyEasy: "Einfach",
    aiTaxOptimizationDifficultyMedium: "Mittel",
    aiTaxOptimizationDifficultyAdvanced: "Fortgeschritten",
    // Finance and Income/Expenses
    "income-expenses": "Einnahmen & Ausgaben",
    income: "Einnahmen",
    expenses: "Ausgaben",
    financialOverview: "Finanzübersicht",
    detailedIncomeOverview: "Detaillierte Einnahmenübersicht",
    detailedExpensesOverview: "Detaillierte Ausgabenübersicht",
    description: "Beschreibung",
    date: "Datum",
    netAmount: "Netto (€)",
    vatAmount: "USt. (€)",
    grossAmount: "Brutto (€)",
    total: "Gesamt",
    subtotalNet: "Zwischensumme (Netto)",
    vat: "USt.",
    vatRate: "Steuersatz",
    totalGross: "Gesamtbetrag (Brutto)",
    itemQuantity: "Menge",
    unitPrice: "Einzelpreis",
    // Hero section
    introducing: "Wir stellen vor: TaxMate 1.0",
    invoicingMade: "Rechnungsstellung",
    superEasy: "ganz einfach!",
    creatingInvoices: "Rechnungen erstellen kann mühsam sein! Bei TaxMate machen wir es einfach.",
    getStarted: "Jetzt Starten",
    learnMore: "Mehr Erfahren",
    // Auth
    signIn: "Anmelden",
    signUp: "Registrieren",
    email: "E-Mail",
    password: "Passwort",
    forgotPassword: "Passwort vergessen?",
    noAccount: "Noch kein Konto?",
    myAccount: "Mein Konto",
    alreadyHaveAccount: "Bereits ein Konto?",
    manageInvoices: "Verwalten Sie Ihre Rechnungen hier",
    createInvoice: "Rechnung erstellen",
    // Invoice actions
    markAsPaid: "Als bezahlt markieren?",
    markAsPaidDesc: "Sind Sie sicher, dass Sie diese Rechnung als bezahlt markieren möchten?",
    markAsPaidButton: "Als bezahlt markieren!",
    cancel: "Abbrechen",
    deleteInvoice: "Rechnung löschen",
    deleteInvoiceDesc: "Sind Sie sicher, dass Sie diese Rechnung löschen möchten?",
    deleteInvoiceButton: "Rechnung löschen",
    // Theme settings
    appearance: "Erscheinungsbild",
    theme: "Thema",
    light: "Hell",
    dark: "Dunkel",
    system: "System",
    switchToLight: "Zum hellen Modus wechseln",
    switchToDark: "Zum dunklen Modus wechseln",
    switchToSystem: "Zur Systemeinstellung wechseln",
    userSettings: "Benutzereinstellungen",
    settingsSaved: "Einstellungen erfolgreich gespeichert",
    settingsError: "Fehler beim Speichern der Einstellungen",
    // Tab Visibility Settings
    navigationTabs: "Navigationsleiste",
    chooseVisibleTabs: "Wählen Sie aus, welche Tabs in der Navigation angezeigt werden sollen",
    alwaysVisible: "immer sichtbar",
    saveChanges: "Änderungen speichern",
    saving: "Speichern...",
    editTabs: "Tabs bearbeiten",
    useDialogEditor: "Dialog-Editor verwenden",
    useShakingEditor: "Wackel-Editor verwenden",
    // Time Tracking
    timeTracking: "Zeiterfassung"
  },
};
