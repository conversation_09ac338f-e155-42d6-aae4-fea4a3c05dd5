"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { useEffect, useState } from "react";
import { AIChat } from "@/app/components/ai/AIChat";
import { TaxOptimization } from "@/app/components/ai/TaxOptimization";
import { VoiceAssistant } from "@/app/components/VoiceAssistant";

export default function AIPage() {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {mounted ? t("ai") : "AI Assistant"}
        </h1>
        <p className="text-muted-foreground">
          {mounted ? t("aiAssistant") : "AI Tax Advisor"}
        </p>
      </div>

      <Tabs defaultValue="chat" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="chat">
            {mounted ? t("aiChat") : "Chat"}
          </TabsTrigger>
          <TabsTrigger value="tax-optimization">
            {mounted ? t("aiTaxOptimization") : "Tax Optimization"}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="chat" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {mounted ? t("ai") : "AI Chat Assistant"}
              </CardTitle>
              <CardDescription>
                {mounted ? t("aiChatDescription") : "Ask questions about taxes, invoicing, or business finances"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AIChat />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="tax-optimization" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {mounted ? t("aiTaxOptimization") : "Tax Optimization Suggestions"}
              </CardTitle>
              <CardDescription>
                {mounted ? t("aiTaxOptimizationDescription") : "Get personalized suggestions to optimize your tax situation"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TaxOptimization />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Voice Assistant */}
      <VoiceAssistant />
    </div>
  );
}
