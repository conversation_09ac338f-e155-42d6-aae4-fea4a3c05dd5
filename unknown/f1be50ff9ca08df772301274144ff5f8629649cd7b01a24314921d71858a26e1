import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/app/utils/auth";
import prisma from "@/app/utils/db";

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: ownerId } = await params;

    const owner = await prisma.owner.findFirst({
      where: {
        id: ownerId,
        userId: session.user.id,
      },
      include: {
        units: {
          include: {
            property: {
              include: {
                accountingPeriods: {
                  include: {
                    expenses: {
                      include: {
                        category: true,
                      },
                    },
                  },
                  orderBy: {
                    year: "desc",
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!owner) {
      return NextResponse.json({ error: "Owner not found" }, { status: 404 });
    }

    const statements = [];

    for (const unit of owner.units) {
      for (const accountingPeriod of unit.property.accountingPeriods) {
        const totalExpenses = accountingPeriod.expenses.reduce((sum, expense) => sum + expense.amount, 0);

        const ownerShare = totalExpenses * (unit.ownershipPercentage / 100);

        const statement = {
          id: `${accountingPeriod.id}-${unit.id}`,
          year: accountingPeriod.year,
          propertyName: unit.property.name,
          unitNumber: unit.unitNumber,
          ownershipPercentage: unit.ownershipPercentage,
          totalExpenses: totalExpenses,
          ownerShare: ownerShare,
          total: ownerShare,
          createdAt: accountingPeriod.createdAt.toISOString().split("T")[0],
          status: accountingPeriod.status,
          expenses: accountingPeriod.expenses.map(expense => ({
            id: expense.id,
            description: expense.description,
            amount: expense.amount,
            ownerShare: expense.amount * (unit.ownershipPercentage / 100),
            category: expense.category.name,
            date: expense.date.toISOString().split("T")[0],
          })),
        };

        statements.push(statement);
      }
    }

    statements.sort((a, b) => b.year - a.year);

    return NextResponse.json(statements);
  } catch (error) {
    console.error("Failed to fetch owner statements:", error);
    return NextResponse.json({ error: "Failed to fetch statements" }, { status: 500 });
  }
}