"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Edit, Trash2 } from "lucide-react";
import { DistributionKey, Property } from "./PropertyList";

interface DistributionKeyManagerProps {
  property: Property;
  onUpdate: (property: Property) => void;
}

export function DistributionKeyManager({ property, onUpdate }: DistributionKeyManagerProps) {
  const { language } = useLanguage();
  
  // State für das Hinzufügen/Bearbeiten von Verteilerschlüsseln
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentKey, setCurrentKey] = useState<DistributionKey | null>(null);
  const [newKey, setNewKey] = useState<Omit<DistributionKey, "id">>({
    name: "",
    type: "squareMeters",
    description: "",
  });
  
  // Handler für das Hinzufügen eines neuen Verteilerschlüssels
  const handleAddKey = () => {
    const newId = (property.distributionKeys.length + 1).toString();
    const keyToAdd: DistributionKey = {
      id: newId,
      ...newKey,
    };
    
    const updatedProperty = {
      ...property,
      distributionKeys: [...property.distributionKeys, keyToAdd],
    };
    
    onUpdate(updatedProperty);
    setNewKey({
      name: "",
      type: "squareMeters",
      description: "",
    });
    setIsAddDialogOpen(false);
  };
  
  // Handler für das Bearbeiten eines Verteilerschlüssels
  const handleEditKey = () => {
    if (!currentKey) return;
    
    const updatedProperty = {
      ...property,
      distributionKeys: property.distributionKeys.map(key => 
        key.id === currentKey.id ? currentKey : key
      ),
    };
    
    onUpdate(updatedProperty);
    setCurrentKey(null);
    setIsEditDialogOpen(false);
  };
  
  // Handler für das Löschen eines Verteilerschlüssels
  const handleDeleteKey = (id: string) => {
    const updatedProperty = {
      ...property,
      distributionKeys: property.distributionKeys.filter(key => key.id !== id),
    };
    
    onUpdate(updatedProperty);
  };
  
  // Funktion zum Öffnen des Bearbeitungsdialogs
  const openEditDialog = (key: DistributionKey) => {
    setCurrentKey({...key});
    setIsEditDialogOpen(true);
  };
  
  // Funktion zum Übersetzen des Verteilerschlüsseltyps
  const translateKeyType = (type: string) => {
    switch (type) {
      case "squareMeters":
        return language === "de" ? "Quadratmeter" : "Square Meters";
      case "units":
        return language === "de" ? "Einheiten" : "Units";
      case "consumption":
        return language === "de" ? "Verbrauch" : "Consumption";
      case "personDays":
        return language === "de" ? "Personentage" : "Person Days";
      case "custom":
        return language === "de" ? "Individuell" : "Custom";
      default:
        return type;
    }
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-xl">
          {language === "de" ? "Verteilerschlüssel" : "Distribution Keys"}
        </CardTitle>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              {language === "de" ? "Neuer Schlüssel" : "New Key"}
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {language === "de" ? "Neuen Verteilerschlüssel hinzufügen" : "Add New Distribution Key"}
              </DialogTitle>
              <DialogDescription>
                {language === "de" 
                  ? "Geben Sie die Details des neuen Verteilerschlüssels ein." 
                  : "Enter the details of the new distribution key."}
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="key-name" className="text-right">
                  {language === "de" ? "Name" : "Name"}
                </Label>
                <Input
                  id="key-name"
                  value={newKey.name}
                  onChange={(e) => setNewKey({...newKey, name: e.target.value})}
                  className="col-span-3"
                />
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="key-type" className="text-right">
                  {language === "de" ? "Typ" : "Type"}
                </Label>
                <Select
                  value={newKey.type}
                  onValueChange={(value: any) => setNewKey({...newKey, type: value})}
                >
                  <SelectTrigger id="key-type" className="col-span-3">
                    <SelectValue placeholder={language === "de" ? "Typ auswählen" : "Select type"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="squareMeters">
                      {language === "de" ? "Quadratmeter" : "Square Meters"}
                    </SelectItem>
                    <SelectItem value="units">
                      {language === "de" ? "Einheiten" : "Units"}
                    </SelectItem>
                    <SelectItem value="consumption">
                      {language === "de" ? "Verbrauch" : "Consumption"}
                    </SelectItem>
                    <SelectItem value="personDays">
                      {language === "de" ? "Personentage" : "Person Days"}
                    </SelectItem>
                    <SelectItem value="custom">
                      {language === "de" ? "Individuell" : "Custom"}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="key-description" className="text-right">
                  {language === "de" ? "Beschreibung" : "Description"}
                </Label>
                <Textarea
                  id="key-description"
                  value={newKey.description}
                  onChange={(e) => setNewKey({...newKey, description: e.target.value})}
                  className="col-span-3"
                  rows={3}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                {language === "de" ? "Abbrechen" : "Cancel"}
              </Button>
              <Button onClick={handleAddKey}>
                {language === "de" ? "Hinzufügen" : "Add"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {/* Bearbeitungsdialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {language === "de" ? "Verteilerschlüssel bearbeiten" : "Edit Distribution Key"}
              </DialogTitle>
              <DialogDescription>
                {language === "de" 
                  ? "Bearbeiten Sie die Details des Verteilerschlüssels." 
                  : "Edit the details of the distribution key."}
              </DialogDescription>
            </DialogHeader>
            
            {currentKey && (
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-key-name" className="text-right">
                    {language === "de" ? "Name" : "Name"}
                  </Label>
                  <Input
                    id="edit-key-name"
                    value={currentKey.name}
                    onChange={(e) => setCurrentKey({...currentKey, name: e.target.value})}
                    className="col-span-3"
                  />
                </div>
                
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-key-type" className="text-right">
                    {language === "de" ? "Typ" : "Type"}
                  </Label>
                  <Select
                    value={currentKey.type}
                    onValueChange={(value: any) => setCurrentKey({...currentKey, type: value})}
                  >
                    <SelectTrigger id="edit-key-type" className="col-span-3">
                      <SelectValue placeholder={language === "de" ? "Typ auswählen" : "Select type"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="squareMeters">
                        {language === "de" ? "Quadratmeter" : "Square Meters"}
                      </SelectItem>
                      <SelectItem value="units">
                        {language === "de" ? "Einheiten" : "Units"}
                      </SelectItem>
                      <SelectItem value="consumption">
                        {language === "de" ? "Verbrauch" : "Consumption"}
                      </SelectItem>
                      <SelectItem value="personDays">
                        {language === "de" ? "Personentage" : "Person Days"}
                      </SelectItem>
                      <SelectItem value="custom">
                        {language === "de" ? "Individuell" : "Custom"}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-key-description" className="text-right">
                    {language === "de" ? "Beschreibung" : "Description"}
                  </Label>
                  <Textarea
                    id="edit-key-description"
                    value={currentKey.description}
                    onChange={(e) => setCurrentKey({...currentKey, description: e.target.value})}
                    className="col-span-3"
                    rows={3}
                  />
                </div>
              </div>
            )}
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                {language === "de" ? "Abbrechen" : "Cancel"}
              </Button>
              <Button onClick={handleEditKey}>
                {language === "de" ? "Speichern" : "Save"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        {/* Verteilerschlüsselliste */}
        {property.distributionKeys.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "de" ? "Name" : "Name"}</TableHead>
                <TableHead>{language === "de" ? "Typ" : "Type"}</TableHead>
                <TableHead>{language === "de" ? "Beschreibung" : "Description"}</TableHead>
                <TableHead className="w-[100px]">{language === "de" ? "Aktionen" : "Actions"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {property.distributionKeys.map((key) => (
                <TableRow key={key.id}>
                  <TableCell className="font-medium">{key.name}</TableCell>
                  <TableCell>{translateKeyType(key.type)}</TableCell>
                  <TableCell className="max-w-[300px] truncate">{key.description}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" onClick={() => openEditDialog(key)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteKey(key.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">
              {language === "de" 
                ? "Keine Verteilerschlüssel vorhanden. Fügen Sie einen neuen Schlüssel hinzu." 
                : "No distribution keys available. Add a new key."}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
