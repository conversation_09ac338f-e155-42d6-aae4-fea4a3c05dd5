import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import prisma from "@/app/utils/db";
import { randomBytes } from "crypto";
import { NextRequest } from "next/server";

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    try {
      // Prüfen, ob der Berater dem Benutzer gehört
      const advisor = await prisma.advisor.findFirst({
        where: {
          id,
          userId: session.user.id,
        },
        include: {
          userAdvisors: {
            where: {
              userId: session.user.id,
            },
          },
        },
      });

      if (!advisor || advisor.userAdvisors.length === 0) {
        return NextResponse.json({ error: "Advisor not found" }, { status: 404 });
      }

      // Neues Zugriffstoken generieren
      const newAccessToken = randomBytes(32).toString('hex');

      // Zugriffstoken aktualisieren
      const userAdvisor = await prisma.userAdvisor.update({
        where: {
          id: advisor.userAdvisors[0].id,
        },
        data: {
          accessToken: newAccessToken,
        },
      });

      return NextResponse.json({ accessToken: newAccessToken });
    } catch (dbError) {
      console.error("Database error when regenerating token:", dbError);
      return NextResponse.json({ error: "Failed to regenerate token" }, { status: 500 });
    }
  } catch (error) {
    console.error("Failed to regenerate token:", error);
    return NextResponse.json({ error: "Failed to regenerate token" }, { status: 500 });
  }
}
