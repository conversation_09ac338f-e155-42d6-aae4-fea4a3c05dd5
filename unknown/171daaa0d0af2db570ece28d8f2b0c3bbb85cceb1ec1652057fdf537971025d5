"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useLanguage } from "@/app/contexts/LanguageContext"
import { useTheme } from "next-themes"
import { useEffect, useState, useTransition } from "react"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { saveUserSettings } from "@/app/actions/settings"
import { useToast } from "@/app/context/ToastContext"

export default function SettingsPage() {
  const { t, language, setLanguage } = useLanguage()
  const { theme, resolvedTheme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [isPending, startTransition] = useTransition()
  const { toast } = useToast()

  // Only show the theme switcher after mounting to avoid hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Save settings to database when they change
  const handleSaveSettings = (newTheme: string, newLanguage: string) => {
    startTransition(async () => {
      try {
        // Update UI immediately
        if (newTheme !== theme) setTheme(newTheme)
        if (newLanguage !== language) setLanguage(newLanguage as "en" | "de")

        // Save to database
        await saveUserSettings(newTheme, newLanguage)
        toast.success(t('settings'), { description: t('settingsSaved') })
      } catch (error) {
        console.error('Failed to save settings:', error)
        toast.error(t('error'), { description: t('settingsError') })
      }
    })
  }

  if (!mounted) {
    // Return a loading skeleton to avoid hydration issues
    return (
      <div className="space-y-6" suppressHydrationWarning>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            User settings
          </p>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm h-40"></div>
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm h-40"></div>
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm h-40"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6" suppressHydrationWarning>
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{t('userSettings')}</h1>
        <p className="text-muted-foreground">
          {t('settings')}
        </p>
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{t('currentLanguage')}</CardTitle>
            <CardDescription>
              {t('switchLanguage')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={language}
              onValueChange={(value) => handleSaveSettings(theme || "system", value)}
              disabled={isPending}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t('selectLanguage')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">{t('english')}</SelectItem>
                <SelectItem value="de">{t('german')}</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('appearance')}</CardTitle>
            <CardDescription>
              {t('theme')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <Button
                variant={(theme === 'light' || (!theme && resolvedTheme === 'light')) ? "default" : "outline"}
                className="w-full"
                onClick={() => handleSaveSettings('light', language)}
                disabled={isPending}
              >
                {t('light')}
              </Button>
              <Button
                variant={(theme === 'dark' || (!theme && resolvedTheme === 'dark')) ? "default" : "outline"}
                className="w-full"
                onClick={() => handleSaveSettings('dark', language)}
                disabled={isPending}
              >
                {t('dark')}
              </Button>
              <Button
                variant={theme === 'system' ? "default" : "outline"}
                className="w-full"
                onClick={() => handleSaveSettings('system', language)}
                disabled={isPending}
              >
                {t('system')}
              </Button>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
