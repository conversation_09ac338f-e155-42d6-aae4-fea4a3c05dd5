import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import prisma from "@/app/utils/db";

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, targetAmount, yearlyContribution } = body;

    const { id } = await params;

    // Check if reserve belongs to user
    const existingReserve = await prisma.reserve.findFirst({
      where: {
        id: id,
        userId: session.user.id,
      },
    });

    if (!existingReserve) {
      return NextResponse.json({ error: "Reserve not found" }, { status: 404 });
    }

    const reserve = await prisma.reserve.update({
      where: {
        id: id,
      },
      data: {
        name: name || existingReserve.name,
        description: description || existingReserve.description,
        targetAmount: targetAmount !== undefined ? targetAmount : existingReserve.targetAmount,
        yearlyContribution: yearlyContribution !== undefined ? yearlyContribution : existingReserve.yearlyContribution,
      },
      include: {
        transactions: {
          orderBy: {
            date: "desc",
          },
        },
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json(reserve);
  } catch (error) {
    console.error("Failed to update reserve:", error);
    return NextResponse.json({ error: "Failed to update reserve" }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    // Check if reserve belongs to user
    const existingReserve = await prisma.reserve.findFirst({
      where: {
        id: id,
        userId: session.user.id,
      },
    });

    if (!existingReserve) {
      return NextResponse.json({ error: "Reserve not found" }, { status: 404 });
    }

    await prisma.reserve.delete({
      where: {
        id: id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete reserve:", error);
    return NextResponse.json({ error: "Failed to delete reserve" }, { status: 500 });
  }
}
