import prisma from "@/app/utils/db";
import { redirect } from "next/navigation";
import { requireUser } from "@/app/utils/hooks";
import { MarkAsPaidClient } from "@/app/components/MarkAsPaidClient";

async function Authorize(invoiceId: string, userId: string) {
  const data = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
      userId: userId,
    },
  });

  if (!data) {
    return redirect("/dashboard/invoices");
  }
}

type Params = { invoiceId: string };

export default async function MarkAsPaid({ params }: { params: Promise<Params> }) {
  const { invoiceId } = await params;
  const session = await requireUser();
  await Authorize(invoiceId, session.user?.id as string);
  
  return <MarkAsPaidClient invoiceId={invoiceId} />;
}
