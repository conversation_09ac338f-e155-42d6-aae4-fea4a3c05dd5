"use server";

import prisma from "@/app/utils/db";
import { revalidatePath } from "next/cache";
import { requireUser } from "@/app/utils/hooks";

export async function DeleteInvoice(invoiceId: string) {
  const session = await requireUser();
  
  if (!session?.user) {
    throw new Error("Unauthorized");
  }
  
  try {
    await prisma.invoice.delete({
      where: {
        id: invoiceId,
        userId: session.user.id,
      },
    });
    
    revalidatePath("/dashboard/invoices");
  } catch (error) {
    console.error("Failed to delete invoice:", error);
    throw new Error("Failed to delete invoice");
  }
}
