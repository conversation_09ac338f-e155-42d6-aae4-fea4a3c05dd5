// Shared storage for owners to ensure consistency across API routes
// In a production environment, this would be replaced with a database

import type { Owner } from "./wegTypes";

// In-memory storage for owners - starts empty, no example data
let owners: Owner[] = [];

export const getOwners = (): Owner[] => {
  return owners;
};

export const setOwners = (newOwners: Owner[]): void => {
  owners = newOwners;
};

export const addOwner = (owner: Owner): Owner => {
  owners.push(owner);
  return owner;
};

export const updateOwner = (id: string, updatedData: Partial<Owner>): Owner | null => {
  const ownerIndex = owners.findIndex(owner => owner.id === id);
  
  if (ownerIndex === -1) {
    return null;
  }
  
  owners[ownerIndex] = {
    ...owners[ownerIndex],
    ...updatedData
  };
  
  return owners[ownerIndex];
};

export const deleteOwner = (id: string): boolean => {
  const ownerIndex = owners.findIndex(owner => owner.id === id);
  
  if (ownerIndex === -1) {
    return false;
  }
  
  owners.splice(ownerIndex, 1);
  return true;
};

export const getOwnerById = (id: string): Owner | null => {
  return owners.find(owner => owner.id === id) || null;
};
