"use client"

import { useLanguage } from '@/app/contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useEffect, useState } from 'react';

interface StatsCardProps {
  title: "totalRevenue" | "totalInvoicesIssued" | "paidInvoices" | "pendingInvoices";
  value: string;
  description: "basedOnTotalVolume" | "totalInvoicesIssuedDesc" | "paidInvoicesDesc" | "pendingInvoicesDesc";
  fallbackTitle: string;
  fallbackDescription: string;
}

export function StatsCard({ title, value, description, fallbackTitle, fallbackDescription }: StatsCardProps) {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {mounted ? t(title) : fallbackTitle}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">
          {mounted ? t(description) : fallbackDescription}
        </p>
      </CardContent>
    </Card>
  );
}

export function DashboardStats({
  totalRevenue,
  totalInvoices,
  paidInvoices,
  pendingInvoices,
}: {
  totalRevenue: string;
  totalInvoices: number;
  paidInvoices: number;
  pendingInvoices: number;
}) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatsCard 
        title="totalRevenue" 
        value={totalRevenue} 
        description="basedOnTotalVolume"
        fallbackTitle="Total Revenue"
        fallbackDescription="Based on total volume"
      />
      <StatsCard 
        title="totalInvoicesIssued" 
        value={`+${totalInvoices}`} 
        description="totalInvoicesIssuedDesc"
        fallbackTitle="Total Invoices Issued"
        fallbackDescription="Total Invoices Issued!"
      />
      <StatsCard 
        title="paidInvoices" 
        value={`+${paidInvoices}`} 
        description="paidInvoicesDesc"
        fallbackTitle="Paid Invoices"
        fallbackDescription="Total Invoices which have been paid!"
      />
      <StatsCard 
        title="pendingInvoices" 
        value={`+${pendingInvoices}`} 
        description="pendingInvoicesDesc"
        fallbackTitle="Pending Invoices"
        fallbackDescription="Invoices which are currently pending!"
      />
    </div>
  );
}
