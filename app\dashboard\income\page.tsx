"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useLanguage } from "@/app/contexts/LanguageContext";

export default function IncomePage() {
  const { t } = useLanguage();

  // Mock data for income with net amounts - updated VAT rate to 20%
  const incomeData = [
    { id: 1, description: "Kundenauftrag #1234", netAmount: 1500, vatRate: 20, date: "2023-05-15" },
    { id: 2, description: "Beratungsleistung", netAmount: 850, vatRate: 20, date: "2023-05-20" },
    { id: 3, description: "Produktverkauf", netAmount: 1200, vatRate: 20, date: "2023-05-25" },
  ];

  // Calculate totals
  const totalNet = incomeData.reduce((sum, item) => sum + item.netAmount, 0);
  const totalVat = incomeData.reduce((sum, item) => sum + (item.netAmount * item.vatRate / 100), 0);
  const totalGross = totalNet + totalVat;

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Einnahmen</h1>

      <Card>
        <CardHeader>
          <CardTitle>Einnahmenübersicht</CardTitle>
          <CardDescription>Detaillierte Übersicht aller Einnahmen</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Beschreibung</th>
                  <th className="text-left py-2">Datum</th>
                  <th className="text-right py-2">Netto (Euro)</th>
                  <th className="text-right py-2">USt (Euro)</th>
                  <th className="text-right py-2">Brutto (Euro)</th>
                </tr>
              </thead>
              <tbody>
                {incomeData.map((item) => {
                  const vatAmount = item.netAmount * item.vatRate / 100;
                  const grossAmount = item.netAmount + vatAmount;

                  return (
                    <tr key={item.id} className="border-b hover:bg-slate-50 transition-colors">
                      <td className="py-3">
                        <p className="font-medium">{item.description}</p>
                      </td>
                      <td className="py-3 text-sm text-muted-foreground">{item.date}</td>
                      <td className="py-3 text-right">{item.netAmount.toFixed(2)} €</td>
                      <td className="py-3 text-right">{vatAmount.toFixed(2)} €</td>
                      <td className="py-3 text-right font-semibold text-green-600">{grossAmount.toFixed(2)} €</td>
                    </tr>
                  );
                })}
              </tbody>
              <tfoot>
                <tr className="font-bold">
                  <td colSpan={2} className="pt-4">Gesamt</td>
                  <td className="pt-4 text-right">{totalNet.toFixed(2)} €</td>
                  <td className="pt-4 text-right">{totalVat.toFixed(2)} €</td>
                  <td className="pt-4 text-right text-green-600">{totalGross.toFixed(2)} €</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
