"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { TranslationKey } from "@/i18n";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { addEmployee } from "@/app/actions/employees";
import { Employee } from "@/app/lib/definitions";
import { useToast } from "@/app/context/ToastContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/seperator";
import { motion } from "framer-motion";
import { Textarea } from "@/components/ui/textarea";
import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

type EmployeeFormProps = {
  onEmployeeAdded: (employee: Employee) => void;
  onCancel: () => void;
};

export function EmployeeForm({ onEmployeeAdded, onCancel }: EmployeeFormProps) {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    taxId: "", // Added tax ID/UID
    address: "", // Added address
    position: "",
    department: "",
    startDate: "",
    salary: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [focused, setFocused] = useState<string | null>(null);

  const { toast } = useToast();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const newEmployee = await addEmployee(formData);
      toast.success(
        t('employeeAdded' as TranslationKey),
        {
          description: t('employeeAddedSuccess' as TranslationKey)
        }
      );
      onEmployeeAdded(newEmployee);
    } catch (error) {
      toast.error(
        t('error' as TranslationKey),
        {
          description: t('employeeAddFailed' as TranslationKey)
        }
      );
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const FormLabel = ({
    htmlFor,
    required = true,
    children,
    tooltip
  }: {
    htmlFor: string,
    required?: boolean,
    children: React.ReactNode,
    tooltip?: string
  }) => (
    <div className="flex items-center justify-between mb-1.5">
      <Label htmlFor={htmlFor} className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
        {children}
        {required && <span className="text-red-500 ml-1 text-sm">*</span>}
      </Label>
      {tooltip && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="cursor-help">
                <InfoCircledIcon className="h-4 w-4 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" />
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs text-xs">
              {tooltip}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );

  const SectionTitle = ({ children }: { children: React.ReactNode }) => (
    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
      {children}
    </h3>
  );

  const SectionDescription = ({ children }: { children: React.ReactNode }) => (
    <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
      {children}
    </p>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-gray-50 dark:bg-gray-800/30 p-5 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-bold mb-4">
            {t('personalInfo' as TranslationKey)}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            {/* Name Field */}
            <div>
              <div className="bg-white dark:bg-gray-950 rounded-t-md border border-b-0 border-gray-300 dark:border-gray-600 px-3 py-2">
                <Label htmlFor="name" className="block text-sm font-bold">
                  {t('employeeName' as TranslationKey)}
                  <span className="text-red-500 ml-1">*</span>
                </Label>
              </div>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                onFocus={() => setFocused("name")}
                onBlur={() => setFocused(null)}
                className="rounded-t-none border-gray-300 dark:border-gray-600 h-12 text-base"
                placeholder="John Doe"
              />
            </div>

            {/* Email Field */}
            <div>
              <div className="bg-white dark:bg-gray-950 rounded-t-md border border-b-0 border-gray-300 dark:border-gray-600 px-3 py-2">
                <Label htmlFor="email" className="block text-sm font-bold">
                  {t('email' as TranslationKey)}
                  <span className="text-red-500 ml-1">*</span>
                </Label>
              </div>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
                onFocus={() => setFocused("email")}
                onBlur={() => setFocused(null)}
                className="rounded-t-none border-gray-300 dark:border-gray-600 h-12 text-base"
                placeholder="<EMAIL>"
              />
            </div>

            {/* Tax ID Field */}
            <div>
              <div className="bg-white dark:bg-gray-950 rounded-t-md border border-b-0 border-gray-300 dark:border-gray-600 px-3 py-2">
                <Label htmlFor="taxId" className="block text-sm font-bold">
                  {t('taxId' as TranslationKey)}
                  <span className="text-gray-500 ml-2 font-normal">(optional)</span>
                </Label>
              </div>
              <Input
                id="taxId"
                name="taxId"
                value={formData.taxId}
                onChange={handleChange}
                onFocus={() => setFocused("taxId")}
                onBlur={() => setFocused(null)}
                className="rounded-t-none border-gray-300 dark:border-gray-600 h-12 text-base"
                placeholder="e.g., ATU12345678"
              />
            </div>

            {/* Address Field */}
            <div className="md:col-span-2">
              <div className="bg-white dark:bg-gray-950 rounded-t-md border border-b-0 border-gray-300 dark:border-gray-600 px-3 py-2">
                <Label htmlFor="address" className="block text-sm font-bold">
                  {t('address' as TranslationKey)}
                  <span className="text-red-500 ml-1">*</span>
                </Label>
              </div>
              <Textarea
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                required
                onFocus={() => setFocused("address")}
                onBlur={() => setFocused(null)}
                className="rounded-t-none border-gray-300 dark:border-gray-600 min-h-[80px] text-base"
                placeholder="Hauptstrasse 1, 1010 Wien, Austria"
              />
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800/30 p-5 rounded-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-bold mb-4">
            {t('jobDetails' as TranslationKey)}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            {/* Position Field */}
            <div>
              <div className="bg-white dark:bg-gray-950 rounded-t-md border border-b-0 border-gray-300 dark:border-gray-600 px-3 py-2">
                <Label htmlFor="position" className="block text-sm font-bold">
                  {t('position' as TranslationKey)}
                  <span className="text-red-500 ml-1">*</span>
                </Label>
              </div>
              <Input
                id="position"
                name="position"
                value={formData.position}
                onChange={handleChange}
                required
                onFocus={() => setFocused("position")}
                onBlur={() => setFocused(null)}
                className="rounded-t-none border-gray-300 dark:border-gray-600 h-12 text-base"
                placeholder="Software Engineer"
              />
            </div>

            {/* Department Field */}
            <div>
              <div className="bg-white dark:bg-gray-950 rounded-t-md border border-b-0 border-gray-300 dark:border-gray-600 px-3 py-2">
                <Label htmlFor="department" className="block text-sm font-bold">
                  {t('department' as TranslationKey)}
                  <span className="text-red-500 ml-1">*</span>
                </Label>
              </div>
              <Select
                value={formData.department}
                onValueChange={(value) => handleSelectChange("department", value)}
                required
              >
                <SelectTrigger
                  id="department"
                  onFocus={() => setFocused("department")}
                  onBlur={() => setFocused(null)}
                  className="rounded-t-none border-gray-300 dark:border-gray-600 h-12 text-base"
                >
                  <SelectValue placeholder={t('selectDepartment' as TranslationKey)} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hr">HR - Human Resources</SelectItem>
                  <SelectItem value="finance">Finance - Accounting & Finance</SelectItem>
                  <SelectItem value="sales">Sales - Sales & Business Development</SelectItem>
                  <SelectItem value="development">Development - Software & IT</SelectItem>
                  <SelectItem value="marketing">Marketing - Marketing & Communications</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Start Date Field */}
            <div>
              <div className="bg-white dark:bg-gray-950 rounded-t-md border border-b-0 border-gray-300 dark:border-gray-600 px-3 py-2">
                <Label htmlFor="startDate" className="block text-sm font-bold">
                  {t('startDate' as TranslationKey)}
                  <span className="text-red-500 ml-1">*</span>
                </Label>
              </div>
              <Input
                id="startDate"
                name="startDate"
                type="date"
                value={formData.startDate}
                onChange={handleChange}
                required
                onFocus={() => setFocused("startDate")}
                onBlur={() => setFocused(null)}
                className="rounded-t-none border-gray-300 dark:border-gray-600 h-12 text-base"
              />
            </div>

            {/* Salary Field */}
            <div>
              <div className="bg-white dark:bg-gray-950 rounded-t-md border border-b-0 border-gray-300 dark:border-gray-600 px-3 py-2">
                <Label htmlFor="salary" className="block text-sm font-bold">
                  {t('salary' as TranslationKey)}
                  <span className="text-red-500 ml-1">*</span>
                </Label>
              </div>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-base font-medium">€</span>
                <Input
                  id="salary"
                  name="salary"
                  type="number"
                  value={formData.salary}
                  onChange={handleChange}
                  required
                  onFocus={() => setFocused("salary")}
                  onBlur={() => setFocused(null)}
                  className="pl-7 rounded-t-none border-gray-300 dark:border-gray-600 h-12 text-base"
                  placeholder="60000"
                  min="0"
                  step="1000"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="pt-4 border-t border-gray-200 dark:border-gray-700 flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="font-medium text-sm flex items-center text-gray-700 dark:text-gray-300">
            <span className="text-red-500 mr-2 text-lg">*</span>
            {t('requiredFields' as TranslationKey)}
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              type="button"
              onClick={onCancel}
              className="border-2 h-11 px-5 text-base font-medium"
            >
              {t('cancel' as TranslationKey)}
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700 h-11 px-5 text-base font-medium"
            >              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin mr-2 h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                  {t('saving' as TranslationKey)}
                </div>
              ) : (
                t('addEmployee' as TranslationKey)
              )}
            </Button>
          </div>
        </div>
      </form>
    </motion.div>
  );
}
