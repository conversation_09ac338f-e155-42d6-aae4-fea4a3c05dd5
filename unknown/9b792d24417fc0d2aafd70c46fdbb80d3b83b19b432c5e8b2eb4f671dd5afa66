"use client";

import { useState, useEffect, useCallback } from "react";
import { useLanguage } from "../../contexts/LanguageContext";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Building,
  Users,
  Calculator,
  Receipt,
  PiggyBank,
  FileText,
  UserCog,
  ChevronDown
} from "lucide-react";
import { cn } from "@/lib/utils";
import { PropertiesTab } from "./properties/PropertiesTab";
import { OwnersTab } from "./owners/OwnersTab";
import { AccountingTab } from "./accounting/AccountingTab";
import { InvoiceTab } from "./invoices/InvoiceTab";
import { ReserveTab } from "./reserves/ReserveTab";
import { ReportsTab } from "./reports/ReportsTab";
import { AdvisorsTab } from "./advisors/AdvisorsTab";
import { LoadingState } from "@/app/components/LoadingState";
import { Property } from "./properties/PropertyList";
import { Owner } from "./owners/OwnerList";
import { Advisor as DbAdvisor } from "@/app/lib/definitions";
import { Advisor as WegAdvisor } from "@/app/lib/wegTypes";
import { ExpenseCategory } from "./accounting/AccountingList";
import { getAdvisors } from "@/app/actions/advisors";
import { getProperties, getOwners } from "@/app/actions/wegAccounting";
import { getExpenseCategories } from "@/app/actions/wegAccountingMore";

// Helper function to convert database advisors to WEG advisors
const convertToWegAdvisor = (dbAdvisor: DbAdvisor): WegAdvisor => {
  return {
    id: dbAdvisor.id,
    name: dbAdvisor.name,
    email: dbAdvisor.email,
    phone: dbAdvisor.phone || undefined,
    address: dbAdvisor.company || undefined,
    notes: dbAdvisor.notes || undefined,
    permissions: {
      canViewProperties: true, // Default permissions
      canViewOwners: true,
      canViewAccounting: true,
      canViewInvoices: true,
      canViewReports: true,
      canDownloadDocuments: true,
      canEdit: false
    }
  };
};

export function WEGAccountingDashboard() {
  const { language } = useLanguage();
  const [activeTab, setActiveTab] = useState("dashboard");
  const [advisors, setAdvisors] = useState<WegAdvisor[]>([]);

  // Loading-States für die Tabs
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({
    dashboard: false,
    properties: false,
    owners: false,
    accounting: false,
    invoices: false,
    reserves: false,
    reports: false,
    advisors: false
  });

  // Funktion zum Setzen des Loading-States für einen Tab
  const setTabLoading = useCallback((tab: string, loading: boolean) => {
    setIsLoading(prev => ({ ...prev, [tab]: loading }));
  }, []);

  // Laden der Berater und Properties beim Mounten der Komponente
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load advisors
        setTabLoading('advisors', true);
        const advisorData = await getAdvisors();

        // Convert database advisors to WEG advisors
        const wegAdvisors = advisorData.map(convertToWegAdvisor);
        setAdvisors(wegAdvisors);

        // Load properties
        setTabLoading('properties', true);
        const propertyData = await getProperties();
        setProperties(propertyData);

        // Load owners
        setTabLoading('owners', true);
        const ownerData = await getOwners();
        setOwners(ownerData);

        // Load expense categories
        setTabLoading('accounting', true);
        const categoryData = await getExpenseCategories();
        setExpenseCategories(categoryData);
      } catch (error) {
        console.error("Failed to load data:", error);
      } finally {
        setTabLoading('advisors', false);
        setTabLoading('properties', false);
        setTabLoading('owners', false);
        setTabLoading('accounting', false);
      }
    };

    loadData();
  }, [setTabLoading]);

  // Setze den Loading-State für den aktiven Tab beim ersten Laden
  useEffect(() => {
    console.log("Initial loading for tab:", activeTab);
    setTabLoading(activeTab, true);

    // Simuliere eine Ladezeit für den initialen Tab
    const loadingTime = 3000;
    setTimeout(() => {
      console.log("Initial loading finished for tab:", activeTab);
      setTabLoading(activeTab, false);
    }, loadingTime);
  }, [activeTab, setTabLoading]);

  // Setzen des Loading-States beim Tab-Wechsel
  const handleTabChange = (tab: string) => {
    console.log("Tab changed to:", tab);
    setActiveTab(tab);

    // Simuliere Ladezeit für den Tab (nur für Demo-Zwecke)
    if (!['dashboard', 'properties', 'owners', 'accounting', 'invoices', 'reserves', 'reports', 'advisors'].includes(tab)) {
      return;
    }

    // Setze den Loading-State für den Tab
    console.log("Setting loading state for tab:", tab);
    setTabLoading(tab, true);

    // Simuliere eine Ladezeit (kürzer für Dashboard, länger für andere Tabs)
    const loadingTime = tab === 'dashboard' ? 3000 : 5000; // Längere Zeiten zum Testen
    setTimeout(() => {
      console.log("Loading finished for tab:", tab);
      setTabLoading(tab, false);
    }, loadingTime);
  };

  // State für die Objekte
  const [properties, setProperties] = useState<Property[]>([]);

  // State für die Eigentümer
  const [owners, setOwners] = useState<Owner[]>([]);

  // State für die Ausgabenkategorien
  const [expenseCategories, setExpenseCategories] = useState<ExpenseCategory[]>([]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          {language === "de" ? "WEG-Abrechnung" : "Property Management Accounting"}
        </h1>
        <p className="text-muted-foreground">
          {language === "de"
            ? "Verwalten Sie Ihre Wohnungseigentümergemeinschaften und erstellen Sie Abrechnungen"
            : "Manage your property owner associations and create statements"}
        </p>
      </div>

      <Tabs
        defaultValue="dashboard"
        className="space-y-4"
        value={activeTab}
        onValueChange={handleTabChange}
      >
        <div className="flex flex-col space-y-2">
          <div className="flex overflow-x-auto pb-2 scrollbar-hide">
            <TabsList className="flex h-auto">
              <TabsTrigger value="dashboard" className="flex items-center gap-2 py-2 px-4">
                <Building className="h-4 w-4" />
                <span>{language === "de" ? "Übersicht" : "Dashboard"}</span>
              </TabsTrigger>

              <div className="flex items-center px-2">
                <div className="h-8 w-px bg-muted-foreground/20"></div>
              </div>

              {/* Verwaltung Gruppe */}
              <div className="flex items-center">
                <TabsTrigger value="properties" className="flex items-center gap-2 py-2 px-4 rounded-r-none border-r-0">
                  <Building className="h-4 w-4" />
                  <span>{language === "de" ? "Objekte" : "Properties"}</span>
                </TabsTrigger>
                <TabsTrigger value="owners" className="flex items-center gap-2 py-2 px-4 rounded-none border-x-0">
                  <Users className="h-4 w-4" />
                  <span>{language === "de" ? "Eigentümer" : "Owners"}</span>
                </TabsTrigger>
              </div>

              <div className="flex items-center px-2">
                <div className="h-8 w-px bg-muted-foreground/20"></div>
              </div>

              {/* Finanzen Gruppe */}
              <div className="flex items-center">
                <TabsTrigger value="accounting" className="flex items-center gap-2 py-2 px-4 rounded-r-none border-r-0">
                  <Calculator className="h-4 w-4" />
                  <span>{language === "de" ? "Abrechnung" : "Accounting"}</span>
                </TabsTrigger>
                <TabsTrigger value="invoices" className="flex items-center gap-2 py-2 px-4 rounded-none border-x-0">
                  <Receipt className="h-4 w-4" />
                  <span>{language === "de" ? "Rechnungen" : "Invoices"}</span>
                </TabsTrigger>
                <TabsTrigger value="reserves" className="flex items-center gap-2 py-2 px-4 rounded-none border-x-0">
                  <PiggyBank className="h-4 w-4" />
                  <span>{language === "de" ? "Rücklagen" : "Reserves"}</span>
                </TabsTrigger>
              </div>

              <div className="flex items-center px-2">
                <div className="h-8 w-px bg-muted-foreground/20"></div>
              </div>

              {/* Berichte */}
              <TabsTrigger value="reports" className="flex items-center gap-2 py-2 px-4 rounded-r-none border-r-0">
                <FileText className="h-4 w-4" />
                <span>{language === "de" ? "Berichte" : "Reports"}</span>
              </TabsTrigger>

              <div className="flex items-center px-2">
                <div className="h-8 w-px bg-muted-foreground/20"></div>
              </div>

              {/* Berater */}
              <TabsTrigger value="advisors" className="flex items-center gap-2 py-2 px-4">
                <UserCog className="h-4 w-4" />
                <span>{language === "de" ? "Berater" : "Advisors"}</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Mobile Navigation (Dropdown für kleine Bildschirme) */}
          <div className="md:hidden">
            <select
              className="w-full p-2 border rounded-md bg-background"
              value={activeTab}
              onChange={(e) => setActiveTab(e.target.value)}
            >
              <option value="dashboard">{language === "de" ? "Übersicht" : "Dashboard"}</option>
              <optgroup label={language === "de" ? "Verwaltung" : "Management"}>
                <option value="properties">{language === "de" ? "Objekte" : "Properties"}</option>
                <option value="owners">{language === "de" ? "Eigentümer" : "Owners"}</option>
              </optgroup>
              <optgroup label={language === "de" ? "Finanzen" : "Finances"}>
                <option value="accounting">{language === "de" ? "Abrechnung" : "Accounting"}</option>
                <option value="invoices">{language === "de" ? "Rechnungen" : "Invoices"}</option>
                <option value="reserves">{language === "de" ? "Rücklagen" : "Reserves"}</option>
              </optgroup>
              <option value="reports">{language === "de" ? "Berichte" : "Reports"}</option>
              <option value="advisors">{language === "de" ? "Berater" : "Advisors"}</option>
            </select>
          </div>
        </div>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-4">
          {isLoading.dashboard ? (
            <LoadingState
              title={language === "de" ? "Dashboard wird geladen..." : "Loading dashboard..."}
              description={language === "de" ? "Bitte warten Sie, während die Übersichtsdaten geladen werden." : "Please wait while the overview data is being loaded."}
            />
          ) : (
            <>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {language === "de" ? "Objekte" : "Properties"}
                    </CardTitle>
                    <Building className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{properties.length}</div>
                    <p className="text-xs text-muted-foreground">
                      {language === "de" ? "Verwaltete Objekte" : "Managed properties"}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {language === "de" ? "Eigentümer" : "Owners"}
                    </CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{owners.length}</div>
                    <p className="text-xs text-muted-foreground">
                      {language === "de" ? "Registrierte Eigentümer" : "Registered owners"}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {language === "de" ? "Offene Abrechnungen" : "Pending Statements"}
                    </CardTitle>
                    <Calculator className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">0</div>
                    <p className="text-xs text-muted-foreground">
                      {language === "de" ? "Zu erstellende Abrechnungen" : "Statements to create"}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {language === "de" ? "Gesamtrücklage" : "Total Reserves"}
                    </CardTitle>
                    <PiggyBank className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">€0,00</div>
                    <p className="text-xs text-muted-foreground">
                      {language === "de" ? "Summe aller Instandhaltungsrücklagen" : "Sum of all maintenance reserves"}
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle>{language === "de" ? "Nächste Termine" : "Upcoming Events"}</CardTitle>
                    <CardDescription>
                      {language === "de" ? "Anstehende Termine und Fristen" : "Upcoming events and deadlines"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground text-center py-6">
                      {language === "de" ? "Keine anstehenden Termine" : "No upcoming events"}
                    </p>
                  </CardContent>
                </Card>

                <Card className="col-span-1">
                  <CardHeader>
                    <CardTitle>{language === "de" ? "Aktuelle Projekte" : "Current Projects"}</CardTitle>
                    <CardDescription>
                      {language === "de" ? "Laufende Sonderprojekte" : "Ongoing special projects"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground text-center py-6">
                      {language === "de" ? "Keine laufenden Projekte" : "No ongoing projects"}
                    </p>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>

        {/* Placeholder für andere Tabs */}
        <TabsContent value="properties">
          {isLoading.properties ? (
            <LoadingState
              title={language === "de" ? "Objekte werden geladen..." : "Loading properties..."}
              description={language === "de" ? "Bitte warten Sie, während die Daten geladen werden." : "Please wait while the data is being loaded."}
            />
          ) : (
            <PropertiesTab
              properties={properties}
              owners={owners}
              onUpdateProperties={setProperties}
            />
          )}
        </TabsContent>

        <TabsContent value="owners">
          {isLoading.owners ? (
            <LoadingState
              title={language === "de" ? "Eigentümer werden geladen..." : "Loading owners..."}
              description={language === "de" ? "Bitte warten Sie, während die Daten geladen werden." : "Please wait while the data is being loaded."}
            />
          ) : (
            <OwnersTab
              properties={properties}
              owners={owners}
              onUpdateOwners={setOwners}
            />
          )}
        </TabsContent>

        <TabsContent value="accounting">
          {isLoading.accounting ? (
            <LoadingState
              title={language === "de" ? "Abrechnungen werden geladen..." : "Loading accounting data..."}
              description={language === "de" ? "Bitte warten Sie, während die Daten geladen werden." : "Please wait while the data is being loaded."}
            />
          ) : (
            <AccountingTab
              properties={properties}
              owners={owners}
              expenseCategories={expenseCategories}
            />
          )}
        </TabsContent>

        <TabsContent value="invoices">
          {isLoading.invoices ? (
            <LoadingState
              title={language === "de" ? "Rechnungen werden geladen..." : "Loading invoices..."}
              description={language === "de" ? "Bitte warten Sie, während die Daten geladen werden." : "Please wait while the data is being loaded."}
            />
          ) : (
            <InvoiceTab
              properties={properties}
              expenseCategories={expenseCategories}
            />
          )}
        </TabsContent>

        <TabsContent value="reserves">
          {isLoading.reserves ? (
            <LoadingState
              title={language === "de" ? "Rücklagen werden geladen..." : "Loading reserves..."}
              description={language === "de" ? "Bitte warten Sie, während die Daten geladen werden." : "Please wait while the data is being loaded."}
            />
          ) : (
            <ReserveTab properties={properties} />
          )}
        </TabsContent>

        <TabsContent value="reports">
          {isLoading.reports ? (
            <LoadingState
              title={language === "de" ? "Berichte werden geladen..." : "Loading reports..."}
              description={language === "de" ? "Bitte warten Sie, während die Daten geladen werden." : "Please wait while the data is being loaded."}
            />
          ) : (
            <ReportsTab
              properties={properties}
              owners={owners}
              expenseCategories={expenseCategories}
            />
          )}
        </TabsContent>

        <TabsContent value="advisors">
          {isLoading.advisors ? (
            <LoadingState
              title={language === "de" ? "Berater werden geladen..." : "Loading advisors..."}
              description={language === "de" ? "Bitte warten Sie, während die Daten geladen werden." : "Please wait while the data is being loaded."}
            />
          ) : (
            <AdvisorsTab
              advisors={advisors}
              onUpdateAdvisors={setAdvisors}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
