import { InvoiceList } from "@/app/components/InvoiceList";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";
import { InvoicesPageClient } from "@/app/components/InvoicesPageClient";

export default function InvoicesRoute() {
  return (
    <InvoicesPageClient>
      <Suspense fallback={<Skeleton className="w-full h-[500px]" />}>
        <InvoiceList />
      </Suspense>
    </InvoicesPageClient>
  );
}
