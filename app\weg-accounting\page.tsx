"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Providers } from "../providers";
import Link from "next/link";
import Image from "next/image";
import Logo from "@/public/logo.png";
import { Building, Calculator, FileText, PiggyBank, Users, Play, TrendingUp, Shield, Clock, CheckCircle, ArrowRight, BarChart3, Zap } from "lucide-react";
import { motion } from "framer-motion";
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Bar<PERSON>hart, Bar, Pie<PERSON>hart, Pie, Cell } from "recharts";

export default function WEGAccountingLandingPage() {
  return (
    <Providers>
      <div className="min-h-screen flex flex-col">
        {/* Navigation */}
        <header className="sticky top-0 z-50 backdrop-blur-md bg-dark/80 dark:bg-background/80 border-b">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 hover:scale-105 transition-transform duration-300">
                <Image src={Logo} alt="Logo" className="size-8" />
                <h3 className="text-2xl font-semibold">
                  WEG<span className="text-blue-500">Abrechnung</span>
                </h3>
              </div>

              <div className="hidden md:flex items-center gap-6 text-sm">
                <div className="hover:-translate-y-1 transition-transform duration-300">
                  <Link href="#features" className="text-muted-foreground hover:text-foreground transition-colors">
                    Funktionen
                  </Link>
                </div>
                <div className="hover:-translate-y-1 transition-transform duration-300">
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    Preise
                  </Link>
                </div>
                <div className="hover:-translate-y-1 transition-transform duration-300">
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    Über uns
                  </Link>
                </div>
                <div className="hover:-translate-y-1 transition-transform duration-300">
                  <Link href="#" className="text-muted-foreground hover:text-foreground transition-colors">
                    Kontakt
                  </Link>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Link href="/weg-accounting/login">
                  <div className="hover:scale-105 active:scale-95 transition-transform duration-300">
                    <Button variant="outline" className="relative overflow-hidden group">
                      <span className="relative z-10">Login</span>
                      <span className="absolute inset-0 bg-blue-100 dark:bg-blue-900/30 scale-0 opacity-0 group-hover:scale-100 group-hover:opacity-100 transition-all duration-300" style={{ borderRadius: "inherit", transformOrigin: "center" }}></span>
                    </Button>
                  </div>
                </Link>
                <Link href="/weg-accounting/register">
                  <div className="hover:scale-105 active:scale-95 transition-transform duration-300">
                    <Button className="relative overflow-hidden group">
                      <span className="relative z-10">Registrieren</span>
                      <span className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 group-hover:opacity-90 transition-opacity duration-300"></span>
                      <span className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 -translate-x-full group-hover:translate-x-0 transition-transform duration-300"></span>
                    </Button>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20 md:py-32 relative overflow-hidden">
          {/* Animated Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950 dark:via-indigo-950 dark:to-purple-950">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 2 }}
              className="absolute inset-0"
              style={{
                background: `radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                             radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
                             radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%)`
              }}
            />
            <motion.div
              animate={{
                backgroundPosition: ["0% 0%", "100% 100%"],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                repeatType: "reverse",
              }}
              className="absolute inset-0 opacity-30"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: "60px 60px",
              }}
            />
          </div>

          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              {/* Left Content */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-8"
              >
                <div className="space-y-4">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-700 dark:text-blue-300 text-sm font-medium"
                  >
                    <Zap className="mr-2 h-4 w-4" />
                    Enterprise WEG-Management Platform
                  </motion.div>

                  <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight leading-tight"
                  >
                    WEG-Verwaltung{" "}
                    <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                      neu definiert
                    </span>
                  </motion.h1>

                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-xl md:text-2xl text-muted-foreground leading-relaxed max-w-2xl"
                  >
                    Automatisierte WEG-Abrechnungen, intelligente Kostenumlage und professionelle Berichte. 
                    <span className="font-semibold text-foreground"> Sparen Sie 80% Zeit</span> bei der Verwaltung 
                    Ihrer Wohnungseigentümergemeinschaften.
                  </motion.p>
                </div>

                {/* Stats */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="grid grid-cols-3 gap-6"
                >
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">500+</div>
                    <div className="text-sm text-muted-foreground">Verwaltete WEGs</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">€2.5M</div>
                    <div className="text-sm text-muted-foreground">Gesparte Kosten</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600">99.8%</div>
                    <div className="text-sm text-muted-foreground">Verfügbarkeit</div>
                  </div>
                </motion.div>

                {/* CTA Buttons */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="flex flex-col sm:flex-row gap-4"
                >
                  <Link href="/weg-accounting/dashboard">
                    <Button size="lg" className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 group">
                      <span className="mr-2">Kostenlos starten</span>
                      <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                  <Link href="#demo">
                    <Button size="lg" variant="outline" className="w-full sm:w-auto border-2 hover:bg-muted/50 group">
                      <Play className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform" />
                      Demo ansehen
                    </Button>
                  </Link>
                </motion.div>

                {/* Trust Indicators */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8 }}
                  className="flex items-center gap-6 pt-4"
                >
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Shield className="h-4 w-4 text-green-600" />
                    DSGVO-konform
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    ISO 27001 zertifiziert
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4 text-blue-600" />
                    24/7 Support
                  </div>
                </motion.div>
              </motion.div>

              {/* Right Visualization */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="relative"
              >
                <div className="bg-white dark:bg-background p-8 rounded-3xl shadow-2xl border border-blue-100 dark:border-blue-900/30">
                  <motion.div
                    initial={{ scale: 0.9 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                  >
                    <h3 className="text-lg font-semibold mb-4 text-center">WEG-Performance Dashboard</h3>
                    <ResponsiveContainer width="100%" height={250}>
                      <LineChart data={[
                        { month: 'Jan', kosten: 4500, ersparnis: 1200 },
                        { month: 'Feb', kosten: 4200, ersparnis: 1400 },
                        { month: 'Mar', kosten: 3800, ersparnis: 1600 },
                        { month: 'Apr', kosten: 3500, ersparnis: 1800 },
                        { month: 'Mai', kosten: 3200, ersparnis: 2000 },
                        { month: 'Jun', kosten: 2900, ersparnis: 2200 },
                      ]}>
                        <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                        <XAxis dataKey="month" fontSize={12} />
                        <YAxis fontSize={12} />
                        <Line 
                          type="monotone" 
                          dataKey="kosten" 
                          stroke="#ef4444" 
                          strokeWidth={3}
                          dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="ersparnis" 
                          stroke="#22c55e" 
                          strokeWidth={3}
                          dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                    <div className="flex justify-center gap-6 mt-4 text-sm">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span>Verwaltungskosten</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span>Kosteneinsparungen</span>
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Floating Elements */}
                <motion.div
                  animate={{ y: [0, -10, 0] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="absolute -top-4 -right-4 bg-gradient-to-r from-green-400 to-blue-500 text-white p-3 rounded-2xl shadow-lg"
                >
                  <TrendingUp className="h-6 w-6" />
                </motion.div>

                <motion.div
                  animate={{ y: [0, 10, 0] }}
                  transition={{ duration: 4, repeat: Infinity, delay: 1 }}
                  className="absolute -bottom-4 -left-4 bg-gradient-to-r from-purple-400 to-pink-500 text-white p-3 rounded-2xl shadow-lg"
                >
                  <BarChart3 className="h-6 w-6" />
                </motion.div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Enterprise Features Section */}
        <section id="features" className="py-24 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50 dark:from-slate-950 dark:via-blue-950/30 dark:to-indigo-950" />
          
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-full text-blue-700 dark:text-blue-300 text-sm font-medium mb-4">
                <BarChart3 className="mr-2 h-4 w-4" />
                Enterprise-Grade Features
              </div>
              <h2 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
                Revolutionäre WEG-Verwaltung mit 
                <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"> KI-Power</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Nutzen Sie modernste Technologie für automatisierte Abrechnungen, intelligente Kostenumlage 
                und datengetriebene Entscheidungen in der Immobilienverwaltung.
              </p>
            </motion.div>

            {/* Main Feature Showcase */}
            <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="space-y-8"
              >
                <div>
                  <h3 className="text-3xl font-bold mb-4">Intelligente Kostenumlage in Echtzeit</h3>
                  <p className="text-lg text-muted-foreground mb-6">
                    Unsere KI-gestützte Engine berechnet automatisch faire Kostenverteilungen basierend auf 
                    verschiedenen Parametern und historischen Daten.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span>Automatische Berechnung nach WEG-Gesetz</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span>Multi-Parameter Verteilungsschlüssel</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span>Echtzeit-Kostenüberwachung</span>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="bg-white dark:bg-background p-6 rounded-3xl shadow-2xl border border-blue-100 dark:border-blue-900/30">
                  <h4 className="text-lg font-semibold mb-4">Kostenverteilung nach Wohneinheiten</h4>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'Heizkosten', value: 35, fill: '#3b82f6' },
                          { name: 'Wasser', value: 20, fill: '#06b6d4' },
                          { name: 'Strom', value: 15, fill: '#8b5cf6' },
                          { name: 'Wartung', value: 18, fill: '#10b981' },
                          { name: 'Verwaltung', value: 12, fill: '#f59e0b' },
                        ]}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        animationBegin={0}
                        animationDuration={1500}
                      >
                        {[
                          { name: 'Heizkosten', value: 35, fill: '#3b82f6' },
                          { name: 'Wasser', value: 20, fill: '#06b6d4' },
                          { name: 'Strom', value: 15, fill: '#8b5cf6' },
                          { name: 'Wartung', value: 18, fill: '#10b981' },
                          { name: 'Verwaltung', value: 12, fill: '#f59e0b' },
                        ].map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                  <div className="grid grid-cols-2 gap-2 text-sm mt-4">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span>Heizkosten (35%)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-cyan-500 rounded-full"></div>
                      <span>Wasser (20%)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      <span>Strom (15%)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span>Wartung (18%)</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Advanced Features Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <AdvancedFeatureCard
                icon={<Building className="size-8 text-blue-500" />}
                title="Smart Property Management"
                description="KI-gestützte Verwaltung mehrerer Immobilien mit automatischer Datenerfassung und Predictive Analytics für Wartungskosten."
                metrics="500+ WEGs verwaltet"
                delay={0.1}
              />
              
              <AdvancedFeatureCard
                icon={<Users className="size-8 text-green-500" />}
                title="360° Eigentümer-Portal"
                description="Transparente Kommunikation mit personalisierten Dashboards, automatischen Benachrichtigungen und digitaler Dokumentenverwaltung."
                metrics="98% Zufriedenheitsrate"
                delay={0.2}
              />
              
              <AdvancedFeatureCard
                icon={<Calculator className="size-8 text-purple-500" />}
                title="Dynamische Kostenmodelle"
                description="Flexible Verteilungsschlüssel mit Machine Learning-Optimierung für faire und gesetzeskonforme Kostenumlage."
                metrics="50+ Verteilungsmodelle"
                delay={0.3}
              />
              
              <AdvancedFeatureCard
                icon={<PiggyBank className="size-8 text-orange-500" />}
                title="Intelligente Rücklagen"
                description="Automatische Rücklagenplanung mit Zinsprognosen, Investitionsempfehlungen und steueroptimierter Verwaltung."
                metrics="€2.5M+ verwaltet"
                delay={0.4}
              />
              
              <AdvancedFeatureCard
                icon={<FileText className="size-8 text-indigo-500" />}
                title="Enterprise Reporting"
                description="Professionelle Berichte mit White-Label Branding, automatischer Generierung und Export in alle gängigen Formate."
                metrics="10.000+ Berichte/Monat"
                delay={0.5}
              />
              
              <AdvancedFeatureCard
                icon={<Shield className="size-8 text-red-500" />}
                title="Enterprise Security"
                description="Bank-Level Sicherheit mit End-to-End Verschlüsselung, Multi-Faktor Authentifizierung und DSGVO-Compliance."
                metrics="ISO 27001 zertifiziert"
                delay={0.6}
              />
            </div>

            {/* Real-time Analytics Dashboard Preview */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="mt-20 bg-white dark:bg-background rounded-3xl shadow-2xl border border-blue-100 dark:border-blue-900/30 p-8"
            >
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-2">Real-time WEG Analytics Dashboard</h3>
                <p className="text-muted-foreground">Live-Einblicke in Ihre Immobilien-Performance</p>
              </div>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h4 className="text-lg font-semibold mb-4">Monatliche Ausgaben-Trends</h4>
                  <ResponsiveContainer width="100%" height={200}>
                    <BarChart data={[
                      { monat: 'Jan', ausgaben: 12000, budget: 15000 },
                      { monat: 'Feb', ausgaben: 11500, budget: 15000 },
                      { monat: 'Mär', ausgaben: 13200, budget: 15000 },
                      { monat: 'Apr', ausgaben: 10800, budget: 15000 },
                      { monat: 'Mai', ausgaben: 9500, budget: 15000 },
                      { monat: 'Jun', ausgaben: 11200, budget: 15000 },
                    ]}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="monat" fontSize={12} />
                      <YAxis fontSize={12} />
                      <Bar dataKey="ausgaben" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                      <Bar dataKey="budget" fill="#e5e7eb" radius={[4, 4, 0, 0]} opacity={0.5} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
                
                <div className="space-y-6">
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-xl border border-green-200 dark:border-green-800">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-2xl font-bold text-green-700 dark:text-green-400">€18.500</div>
                        <div className="text-sm text-green-600 dark:text-green-500">Eingesparte Kosten (YTD)</div>
                      </div>
                      <TrendingUp className="h-8 w-8 text-green-600" />
                    </div>
                  </div>
                  
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-2xl font-bold text-blue-700 dark:text-blue-400">87%</div>
                        <div className="text-sm text-blue-600 dark:text-blue-500">Automatisierungsgrad</div>
                      </div>
                      <Zap className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>
                  
                  <div className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-2xl font-bold text-purple-700 dark:text-purple-400">2.3 Min</div>
                        <div className="text-sm text-purple-600 dark:text-purple-500">Ø Abrechnungszeit</div>
                      </div>
                      <Clock className="h-8 w-8 text-purple-600" />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Interactive Demo Section */}
        <section id="demo" className="py-24 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-slate-50 via-white to-blue-50 dark:from-slate-950 dark:via-background dark:to-blue-950" />
          
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 rounded-full text-purple-700 dark:text-purple-300 text-sm font-medium mb-4">
                <Play className="mr-2 h-4 w-4" />
                Interaktive Plattform-Demo
              </div>
              <h2 className="text-4xl md:text-5xl font-bold tracking-tight mb-6">
                Erleben Sie die Zukunft der 
                <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent"> WEG-Verwaltung</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Sehen Sie selbst, wie unsere KI-gestützte Plattform komplexe WEG-Abrechnungen in Minuten erstellt
                und dabei Zeit, Geld und Nerven spart.
              </p>
            </motion.div>

            {/* Interactive Dashboard Preview */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative max-w-6xl mx-auto"
            >
              <div className="bg-white dark:bg-background rounded-3xl shadow-2xl border border-gray-200 dark:border-gray-800 overflow-hidden">
                {/* Dashboard Header */}
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold">WEG Dashboard - Musterstraße 123</h3>
                      <p className="text-blue-100">24 Wohneinheiten • 3 Gebäude • €45.000 Jahresbudget</p>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="bg-white/20 px-3 py-1 rounded-full text-sm">Live</div>
                      <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Dashboard Content */}
                <div className="p-8">
                  <div className="grid lg:grid-cols-3 gap-8">
                    {/* Left: Quick Stats */}
                    <div className="space-y-6">
                      <h4 className="text-lg font-semibold">Performance Übersicht</h4>
                      
                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                        viewport={{ once: true }}
                        className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-xl border border-green-200 dark:border-green-800"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-2xl font-bold text-green-700 dark:text-green-400">€3.200</div>
                            <div className="text-sm text-green-600">Monatliche Ersparnis</div>
                          </div>
                          <TrendingUp className="h-8 w-8 text-green-600" />
                        </div>
                        <div className="mt-2 text-xs text-green-600">↗ +12% vs. letzter Monat</div>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6 }}
                        viewport={{ once: true }}
                        className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-2xl font-bold text-blue-700 dark:text-blue-400">98.5%</div>
                            <div className="text-sm text-blue-600">Eigentümer-Zufriedenheit</div>
                          </div>
                          <Users className="h-8 w-8 text-blue-600" />
                        </div>
                        <div className="mt-2 text-xs text-blue-600">Basierend auf 124 Bewertungen</div>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.7 }}
                        viewport={{ once: true }}
                        className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-2xl font-bold text-purple-700 dark:text-purple-400">1.8 Min</div>
                            <div className="text-sm text-purple-600">Ø Abrechnungszeit</div>
                          </div>
                          <Clock className="h-8 w-8 text-purple-600" />
                        </div>
                        <div className="mt-2 text-xs text-purple-600">92% schneller als traditionell</div>
                      </motion.div>
                    </div>

                    {/* Center: Live Chart */}
                    <div className="lg:col-span-2">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold">Live Kostenverteilung</h4>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                          Aktualisiert vor 2 Min
                        </div>
                      </div>
                      
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.8 }}
                        viewport={{ once: true }}
                      >
                        <ResponsiveContainer width="100%" height={300}>
                          <LineChart data={[
                            { monat: 'Jan', istKosten: 38500, budgetKosten: 42000, einsparung: 3500 },
                            { monat: 'Feb', istKosten: 36200, budgetKosten: 42000, einsparung: 5800 },
                            { monat: 'Mär', istKosten: 35800, budgetKosten: 42000, einsparung: 6200 },
                            { monat: 'Apr', istKosten: 33900, budgetKosten: 42000, einsparung: 8100 },
                            { monat: 'Mai', istKosten: 32100, budgetKosten: 42000, einsparung: 9900 },
                            { monat: 'Jun', istKosten: 30800, budgetKosten: 42000, einsparung: 11200 },
                          ]}>
                            <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                            <XAxis dataKey="monat" fontSize={12} />
                            <YAxis fontSize={12} />
                            <Line 
                              type="monotone" 
                              dataKey="budgetKosten" 
                              stroke="#94a3b8" 
                              strokeWidth={2}
                              strokeDasharray="5 5"
                              dot={false}
                            />
                            <Line 
                              type="monotone" 
                              dataKey="istKosten" 
                              stroke="#3b82f6" 
                              strokeWidth={3}
                              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 5 }}
                            />
                            <Line 
                              type="monotone" 
                              dataKey="einsparung" 
                              stroke="#10b981" 
                              strokeWidth={3}
                              dot={{ fill: '#10b981', strokeWidth: 2, r: 5 }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                        
                        <div className="flex justify-center gap-6 mt-4 text-sm">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-slate-400 rounded-full"></div>
                            <span>Budget</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span>Ist-Kosten</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span>Einsparungen</span>
                          </div>
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Action Buttons */}
              <motion.div
                animate={{ y: [0, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute -top-6 -right-6 bg-gradient-to-r from-green-400 to-emerald-500 text-white p-4 rounded-2xl shadow-lg"
              >
                <CheckCircle className="h-6 w-6" />
              </motion.div>

              <motion.div
                animate={{ y: [0, 5, 0] }}
                transition={{ duration: 3, repeat: Infinity, delay: 1 }}
                className="absolute -bottom-6 -left-6 bg-gradient-to-r from-blue-400 to-indigo-500 text-white p-4 rounded-2xl shadow-lg"
              >
                <BarChart3 className="h-6 w-6" />
              </motion.div>
            </motion.div>

            {/* ROI Benefits */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="grid md:grid-cols-3 gap-8 mt-16"
            >
              <div className="text-center group">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-all duration-300"
                >
                  <TrendingUp className="h-8 w-8 text-green-600" />
                </motion.div>
                <h3 className="text-xl font-bold mb-2">80% Zeitersparnis</h3>
                <p className="text-muted-foreground">Automatisierte Abrechnungen reduzieren manuellen Aufwand von Stunden auf Minuten</p>
              </div>

              <div className="text-center group">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-all duration-300"
                >
                  <Shield className="h-8 w-8 text-blue-600" />
                </motion.div>
                <h3 className="text-xl font-bold mb-2">100% Rechtssicherheit</h3>
                <p className="text-muted-foreground">Automatische Compliance mit WEG-Gesetz und allen relevanten Vorschriften</p>
              </div>

              <div className="text-center group">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-gradient-to-br from-purple-100 to-violet-100 dark:from-purple-900/30 dark:to-violet-900/30 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-all duration-300"
                >
                  <Users className="h-8 w-8 text-purple-600" />
                </motion.div>
                <h3 className="text-xl font-bold mb-2">99.8% Verfügbarkeit</h3>
                <p className="text-muted-foreground">Enterprise-Grade Infrastructure für zuverlässigen 24/7 Zugriff von überall</p>
              </div>
            </motion.div>

            {/* CTA */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
              className="text-center mt-16"
            >
              <Link href="/weg-accounting/dashboard">
                <Button size="lg" className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 group">
                  <span className="mr-2">Kostenlose Demo starten</span>
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
              <p className="text-sm text-muted-foreground mt-3">Keine Kreditkarte erforderlich • Sofortiger Zugang</p>
            </motion.div>
          </div>
        </section>

        {/* Footer */}
        <footer className="mt-auto py-12 border-t relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-t from-blue-50/30 to-white dark:from-blue-950/20 dark:to-background" />

          <div
            className="absolute inset-0 opacity-5 animate-pulse"
            style={{
              backgroundImage: "url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23000000' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E\")",
            }}
          />

          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="grid md:grid-cols-3 gap-8 mb-8">
              <div className="animate-fade-in-up">
                <div className="flex items-center gap-2 mb-4">
                  <Image src={Logo} alt="Logo" className="size-8" />
                  <h3 className="text-xl font-semibold">
                    WEG<span className="text-blue-500">Abrechnung</span>
                  </h3>
                </div>
                <p className="text-muted-foreground mb-4">
                  Die moderne Lösung für Ihre WEG-Verwaltung. Einfach, effizient und transparent.
                </p>
                <p className="text-sm text-muted-foreground">
                  © {new Date().getFullYear()} WEG-Abrechnung. Alle Rechte vorbehalten.
                </p>
              </div>

              <div className="animate-fade-in-up animation-delay-200">
                <h4 className="font-semibold mb-4">Schnellzugriff</h4>
                <ul className="space-y-2">
                  <li>
                    <Link href="/weg-accounting/dashboard" className="text-muted-foreground hover:text-blue-500 transition-colors">
                      Dashboard
                    </Link>
                  </li>
                  <li>
                    <Link href="#features" className="text-muted-foreground hover:text-blue-500 transition-colors">
                      Funktionen
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-muted-foreground hover:text-blue-500 transition-colors">
                      Preise
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-muted-foreground hover:text-blue-500 transition-colors">
                      Kontakt
                    </Link>
                  </li>
                </ul>
              </div>

              <div className="animate-fade-in-up animation-delay-400">
                <h4 className="font-semibold mb-4">Rechtliches</h4>
                <ul className="space-y-2">
                  <li>
                    <Link href="#" className="text-muted-foreground hover:text-blue-500 transition-colors">
                      Impressum
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-muted-foreground hover:text-blue-500 transition-colors">
                      Datenschutz
                    </Link>
                  </li>
                  <li>
                    <Link href="#" className="text-muted-foreground hover:text-blue-500 transition-colors">
                      AGB
                    </Link>
                  </li>
                </ul>
              </div>
            </div>

            <div className="border-t pt-6 text-center text-sm text-muted-foreground animate-fade-in animation-delay-600">
              Mit 💙 entwickelt für Wohnungseigentümergemeinschaften in Österreich
            </div>
          </div>
        </footer>
      </div>
    </Providers>
  );
}

function AdvancedFeatureCard({
  icon,
  title,
  description,
  metrics,
  delay = 0
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  metrics: string;
  delay?: number;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay }}
      viewport={{ once: true }}
      className="group relative"
    >
      <div className="bg-white dark:bg-background rounded-2xl p-8 shadow-lg border border-gray-100 dark:border-gray-800 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/30 dark:from-blue-950/20 dark:via-transparent dark:to-indigo-950/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div className="p-3 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-2xl">
              {icon}
            </div>
            <motion.div
              initial={{ scale: 0 }}
              whileInView={{ scale: 1 }}
              transition={{ delay: delay + 0.3, type: "spring", stiffness: 200 }}
              viewport={{ once: true }}
              className="px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 rounded-full text-xs font-medium text-green-700 dark:text-green-400"
            >
              {metrics}
            </motion.div>
          </div>
          
          <h3 className="text-xl font-bold mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
            {title}
          </h3>
          
          <p className="text-muted-foreground leading-relaxed">
            {description}
          </p>
        </div>
        
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
      </div>
    </motion.div>
  );
}

function FeatureCard({
  icon,
  emoji,
  title,
  description,
  delay = ""
}: {
  icon: React.ReactNode;
  emoji: string;
  title: string;
  description: string;
  delay?: string;
}) {
  return (
    <div
      className={`bg-background rounded-lg p-6 shadow-md border border-blue-100 dark:border-blue-900/30 overflow-hidden relative group hover:-translate-y-1 transition-transform duration-300 animate-fade-in-up ${delay}`}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-transparent dark:from-blue-950/30 dark:to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div className="relative z-10">
        <div className="flex items-center gap-3 mb-4">
          <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
            {icon}
          </div>
          <span className="text-2xl" role="img" aria-label={title}>
            {emoji}
          </span>
        </div>
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </div>

      <div
        className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 w-0 group-hover:w-full transition-all duration-500"
      />
    </div>
  );
}
