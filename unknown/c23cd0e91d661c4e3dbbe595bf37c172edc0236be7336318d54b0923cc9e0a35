"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Building, Calculator, Download, FileText, PiggyBank } from "lucide-react";
import { Property } from "../properties/PropertyList";
import { Owner } from "../owners/OwnerList";
import { AccountingPeriod, ExpenseCategory } from "./AccountingList";
import { ExpenseList } from "./ExpenseList";
import { OwnerStatement } from "./OwnerStatement";

interface AccountingDetailProps {
  accounting: AccountingPeriod;
  property: Property;
  owners: Owner[];
  expenseCategories: ExpenseCategory[];
  onBack: () => void;
  onUpdate: (accounting: AccountingPeriod) => void;
}

export function AccountingDetail({
  accounting,
  property,
  owners,
  expenseCategories,
  onBack,
  onUpdate
}: AccountingDetailProps) {
  const { language } = useLanguage();

  // State für die Bearbeitung der Abrechnung
  const [isEditing, setIsEditing] = useState(false);
  const [editedAccounting, setEditedAccounting] = useState<AccountingPeriod>(accounting);

  // Handler für die Aktualisierung der Abrechnung
  const handleUpdateAccounting = () => {
    onUpdate(editedAccounting);
    setIsEditing(false);
  };

  // Funktion zum Formatieren eines Betrags als Währung
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "de" ? "de-AT" : "en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  // Funktion zum Formatieren eines Datums
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === "de" ? "de-AT" : "en-US");
  };

  // Funktion zum Übersetzen des Status
  const translateStatus = (status: string) => {
    switch (status) {
      case "draft":
        return language === "de" ? "Entwurf" : "Draft";
      case "completed":
        return language === "de" ? "Abgeschlossen" : "Completed";
      case "approved":
        return language === "de" ? "Genehmigt" : "Approved";
      default:
        return status;
    }
  };

  // Funktion zum Abrufen der Statusfarbe
  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "completed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
    }
  };

  // Berechne die Gesamtsumme der Ausgaben
  const totalExpenses = accounting.expenses.reduce((total, expense) => total + expense.amount, 0);

  // Berechne die Summe der umlagefähigen Ausgaben
  const allocatableExpenses = accounting.expenses
    .filter(expense => {
      const category = expenseCategories.find(c => c.id === expense.categoryId);
      return category ? category.isAllocatable : true;
    })
    .reduce((total, expense) => total + expense.amount, 0);

  // Berechne die Summe der nicht umlagefähigen Ausgaben
  const nonAllocatableExpenses = accounting.expenses
    .filter(expense => {
      const category = expenseCategories.find(c => c.id === expense.categoryId);
      return category ? !category.isAllocatable : false;
    })
    .reduce((total, expense) => total + expense.amount, 0);

  // Berechne die Summe der haushaltsnahen Dienstleistungen
  const householdRelatedExpenses = accounting.expenses
    .reduce((total, expense) => total + expense.householdRelatedAmount, 0);

  // Berechne die Summe der Handwerkerleistungen
  const craftsmanExpenses = accounting.expenses
    .reduce((total, expense) => total + expense.craftsmanAmount, 0);

  // Berechne den Schlusssaldo
  const closingBalance = accounting.openingBalance + accounting.maintenanceReserveContribution - totalExpenses;

  // Berechne den Schlussstand der Instandhaltungsrücklage
  const maintenanceReserveClosing = accounting.maintenanceReserveOpening + accounting.maintenanceReserveContribution;

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" onClick={onBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-2xl font-bold tracking-tight">
          {property.name} - {language === "de" ? "Abrechnung " : "Statement "} {accounting.year}
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Abrechnungsdetails" : "Statement Details"}
            </CardTitle>
            <CardDescription>
              {formatDate(accounting.startDate)} - {formatDate(accounting.endDate)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">
                  {language === "de" ? "Status" : "Status"}
                </span>
                <Badge className={`${getStatusColor(accounting.status)} border-none`}>
                  {translateStatus(accounting.status)}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Anfangsbestand" : "Opening Balance"}
                </span>
                <span className="font-medium">{formatCurrency(accounting.openingBalance)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Ausgaben" : "Expenses"}
                </span>
                <span className="font-medium">{formatCurrency(totalExpenses)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Rücklagenzuführung" : "Reserve Contribution"}
                </span>
                <span className="font-medium">{formatCurrency(accounting.maintenanceReserveContribution)}</span>
              </div>
              <div className="border-t pt-2 flex justify-between font-medium">
                <span>
                  {language === "de" ? "Schlusssaldo" : "Closing Balance"}
                </span>
                <span>{formatCurrency(closingBalance)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Instandhaltungsrücklage" : "Maintenance Reserve"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Anfangsbestand" : "Opening Balance"}
                </span>
                <span className="font-medium">{formatCurrency(accounting.maintenanceReserveOpening)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Zuführung" : "Contribution"}
                </span>
                <span className="font-medium">{formatCurrency(accounting.maintenanceReserveContribution)}</span>
              </div>
              <div className="border-t pt-2 flex justify-between font-medium">
                <span>
                  {language === "de" ? "Schlussbestand" : "Closing Balance"}
                </span>
                <span>{formatCurrency(maintenanceReserveClosing)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Steuerlich absetzbar" : "Tax Deductible"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Haushaltsnahe Dienstleistungen" : "Household-Related Services"}
                </span>
                <span className="font-medium">{formatCurrency(householdRelatedExpenses)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Handwerkerleistungen" : "Craftsman Services"}
                </span>
                <span className="font-medium">{formatCurrency(craftsmanExpenses)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          {language === "de" ? "Exportieren" : "Export"}
        </Button>

        {accounting.status === "draft" && (
          <Button
            variant="default"
            onClick={() => {
              const updatedAccounting: AccountingPeriod = {
                ...accounting,
                status: "completed",
                closingBalance: closingBalance,
                maintenanceReserveClosing: maintenanceReserveClosing,
              };
              onUpdate(updatedAccounting);
            }}
          >
            <FileText className="h-4 w-4 mr-2" />
            {language === "de" ? "Abschließen" : "Complete"}
          </Button>
        )}
      </div>

      <Tabs defaultValue="expenses" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="expenses">
            {language === "de" ? "Ausgaben" : "Expenses"}
          </TabsTrigger>
          <TabsTrigger value="statements">
            {language === "de" ? "Eigentümerabrechnungen" : "Owner Statements"}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="expenses" className="space-y-4 mt-4">
          <ExpenseList
            accounting={accounting}
            property={property}
            expenseCategories={expenseCategories}
            onUpdate={onUpdate}
          />
        </TabsContent>

        <TabsContent value="statements" className="space-y-4 mt-4">
          <OwnerStatement
            accounting={accounting}
            property={property}
            owners={owners}
            expenseCategories={expenseCategories}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
