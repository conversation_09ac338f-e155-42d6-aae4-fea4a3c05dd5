"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useLanguage } from "@/app/contexts/LanguageContext";

export default function IncomeExpensesPage() {
  const { t } = useLanguage();

  // This would typically come from your API or data source - updated VAT rate to 20%
  const mockData = {
    income: [
      { id: 1, description: "Kundenauftrag #1234", netAmount: 1500, vatRate: 20, date: "2023-05-15" },
      { id: 2, description: "Beratungsleistung", netAmount: 850, vatRate: 20, date: "2023-05-20" },
    ],
    expenses: [
      { id: 1, description: "Büromaterial", netAmount: 120, vatRate: 20, date: "2023-05-10" },
      { id: 2, description: "Software-<PERSON><PERSON><PERSON>", netAmount: 350, vatRate: 20, date: "2023-05-18" },
    ]
  };

  // Calculate totals
  const incomeTotalNet = mockData.income.reduce((sum, item) => sum + item.netAmount, 0);
  const incomeTotalVat = mockData.income.reduce((sum, item) => sum + (item.netAmount * item.vatRate / 100), 0);
  const incomeTotalGross = incomeTotalNet + incomeTotalVat;

  const expensesTotalNet = mockData.expenses.reduce((sum, item) => sum + item.netAmount, 0);
  const expensesTotalVat = mockData.expenses.reduce((sum, item) => sum + (item.netAmount * item.vatRate / 100), 0);
  const expensesTotalGross = expensesTotalNet + expensesTotalVat;

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Einnahmen & Ausgaben</h1>

      <Card>
        <CardHeader>
          <CardTitle>Finanzübersicht</CardTitle>
          <CardDescription>Übersicht über Ihre Einnahmen und Ausgaben</CardDescription>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="income">
              <AccordionTrigger>Einnahmen</AccordionTrigger>
              <AccordionContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Beschreibung</th>
                        <th className="text-left py-2">Datum</th>
                        <th className="text-right py-2">Netto (Euro)</th>
                        <th className="text-right py-2">USt (Euro)</th>
                        <th className="text-right py-2">Brutto (Euro)</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockData.income.map((item) => {
                        const vatAmount = item.netAmount * item.vatRate / 100;
                        const grossAmount = item.netAmount + vatAmount;

                        return (
                          <tr key={item.id} className="border-b">
                            <td className="py-2">{item.description}</td>
                            <td className="py-2 text-sm text-muted-foreground">{item.date}</td>
                            <td className="py-2 text-right">{item.netAmount.toFixed(2)} €</td>
                            <td className="py-2 text-right">{vatAmount.toFixed(2)} €</td>
                            <td className="py-2 text-right font-semibold text-green-600">{grossAmount.toFixed(2)} €</td>
                          </tr>
                        );
                      })}
                    </tbody>
                    <tfoot>
                      <tr className="font-bold">
                        <td colSpan={2} className="pt-4">Gesamt</td>
                        <td className="pt-4 text-right">{incomeTotalNet.toFixed(2)} €</td>
                        <td className="pt-4 text-right">{incomeTotalVat.toFixed(2)} €</td>
                        <td className="pt-4 text-right text-green-600">{incomeTotalGross.toFixed(2)} €</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="expenses">
              <AccordionTrigger>Ausgaben</AccordionTrigger>
              <AccordionContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Beschreibung</th>
                        <th className="text-left py-2">Datum</th>
                        <th className="text-right py-2">Netto (Euro)</th>
                        <th className="text-right py-2">USt (Euro)</th>
                        <th className="text-right py-2">Brutto (Euro)</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockData.expenses.map((item) => {
                        const vatAmount = item.netAmount * item.vatRate / 100;
                        const grossAmount = item.netAmount + vatAmount;

                        return (
                          <tr key={item.id} className="border-b">
                            <td className="py-2">{item.description}</td>
                            <td className="py-2 text-sm text-muted-foreground">{item.date}</td>
                            <td className="py-2 text-right">{item.netAmount.toFixed(2)} €</td>
                            <td className="py-2 text-right">{vatAmount.toFixed(2)} €</td>
                            <td className="py-2 text-right font-semibold text-red-600">{grossAmount.toFixed(2)} €</td>
                          </tr>
                        );
                      })}
                    </tbody>
                    <tfoot>
                      <tr className="font-bold">
                        <td colSpan={2} className="pt-4">Gesamt</td>
                        <td className="pt-4 text-right">{expensesTotalNet.toFixed(2)} €</td>
                        <td className="pt-4 text-right">{expensesTotalVat.toFixed(2)} €</td>
                        <td className="pt-4 text-right text-red-600">{expensesTotalGross.toFixed(2)} €</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>
    </div>
  );
}
