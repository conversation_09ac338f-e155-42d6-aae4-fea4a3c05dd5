import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: Request) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const accountingPeriodId = searchParams.get('accountingPeriodId');

    if (!accountingPeriodId) {
      return NextResponse.json({ error: "AccountingPeriodId is required" }, { status: 400 });
    }

    // Verify that the accounting period belongs to the user
    const accountingPeriod = await prisma.accountingPeriod.findFirst({
      where: {
        id: accountingPeriodId,
        userId: session.user.id,
      },
    });

    if (!accountingPeriod) {
      return NextResponse.json({ error: "Accounting period not found" }, { status: 404 });
    }

    const expenses = await prisma.expense.findMany({
      where: {
        accountingPeriodId: accountingPeriodId,
      },
      include: {
        category: true,
      },
      orderBy: {
        date: 'desc',
      },
    });

    return NextResponse.json(expenses);
  } catch (error) {
    console.error("Failed to fetch expenses:", error);
    return NextResponse.json({ error: "Failed to fetch expenses" }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await request.json();
    
    // Verify that the accounting period belongs to the user
    const accountingPeriod = await prisma.accountingPeriod.findFirst({
      where: {
        id: data.accountingPeriodId,
        userId: session.user.id,
      },
    });

    if (!accountingPeriod) {
      return NextResponse.json({ error: "Accounting period not found" }, { status: 404 });
    }

    // Verify that the category belongs to the user
    const category = await prisma.expenseCategory.findFirst({
      where: {
        id: data.categoryId,
        userId: session.user.id,
      },
    });

    if (!category) {
      return NextResponse.json({ error: "Expense category not found" }, { status: 404 });
    }

    const expense = await prisma.expense.create({
      data: {
        accountingPeriodId: data.accountingPeriodId,
        categoryId: data.categoryId,
        description: data.description,
        amount: data.amount,
        date: new Date(data.date),
        distributionKeyId: data.distributionKeyId || null,
        householdRelatedAmount: data.householdRelatedAmount || 0,
        craftsmanAmount: data.craftsmanAmount || 0,
      },
      include: {
        category: true,
      },
    });
    
    return NextResponse.json(expense);
  } catch (error) {
    console.error("Failed to create expense:", error);
    return NextResponse.json({ error: "Failed to create expense" }, { status: 500 });
  }
}
