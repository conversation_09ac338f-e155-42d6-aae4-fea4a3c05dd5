"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, ArrowDown, ArrowUp, Building, Calculator, Download, Edit, FileText, PiggyBank, Plus, Trash2, TrendingUp } from "lucide-react";
import { Property } from "../properties/PropertyList";
import { Reserve, ReserveTransaction, ReserveTransactionType } from "./ReserveList";

interface ReserveDetailProps {
  reserve: Reserve;
  properties: Property[];
  onBack: () => void;
  onUpdate: (reserve: Reserve) => void;
  onDelete: (id: string) => void;
}

export function ReserveDetail({ 
  reserve, 
  properties, 
  onBack, 
  onUpdate,
  onDelete
}: ReserveDetailProps) {
  const { language } = useLanguage();
  
  // State für die Bearbeitung der Rücklage
  const [isEditing, setIsEditing] = useState(false);
  const [editedReserve, setEditedReserve] = useState<Reserve>(reserve);
  
  // State für das Hinzufügen einer neuen Transaktion
  const [isAddTransactionDialogOpen, setIsAddTransactionDialogOpen] = useState(false);
  const [newTransaction, setNewTransaction] = useState<Omit<ReserveTransaction, "id">>({
    reserveId: reserve.id,
    date: new Date().toISOString().split("T")[0],
    type: "contribution",
    amount: 0,
    description: "",
  });
  
  // Handler für die Aktualisierung der Rücklage
  const handleUpdateReserve = () => {
    onUpdate(editedReserve);
    setIsEditing(false);
  };
  
  // Handler für das Hinzufügen einer neuen Transaktion
  const handleAddTransaction = () => {
    const newId = (reserve.transactions.length + 1).toString();
    const transactionToAdd: ReserveTransaction = {
      id: newId,
      ...newTransaction,
    };
    
    // Berechne den neuen Kontostand basierend auf der Transaktionsart
    let newBalance = reserve.balance;
    switch (newTransaction.type) {
      case "contribution":
      case "interest":
        newBalance += newTransaction.amount;
        break;
      case "withdrawal":
        newBalance -= newTransaction.amount;
        break;
      case "transfer":
        // Bei Überweisungen ändert sich der Kontostand nicht
        break;
    }
    
    const updatedReserve = {
      ...reserve,
      balance: newBalance,
      transactions: [...reserve.transactions, transactionToAdd],
    };
    
    onUpdate(updatedReserve);
    setNewTransaction({
      reserveId: reserve.id,
      date: new Date().toISOString().split("T")[0],
      type: "contribution",
      amount: 0,
      description: "",
    });
    setIsAddTransactionDialogOpen(false);
  };
  
  // Handler für das Löschen einer Transaktion
  const handleDeleteTransaction = (id: string) => {
    const transactionToDelete = reserve.transactions.find(t => t.id === id);
    if (!transactionToDelete) return;
    
    // Berechne den neuen Kontostand basierend auf der Transaktionsart
    let newBalance = reserve.balance;
    switch (transactionToDelete.type) {
      case "contribution":
      case "interest":
        newBalance -= transactionToDelete.amount;
        break;
      case "withdrawal":
        newBalance += transactionToDelete.amount;
        break;
      case "transfer":
        // Bei Überweisungen ändert sich der Kontostand nicht
        break;
    }
    
    const updatedReserve = {
      ...reserve,
      balance: newBalance,
      transactions: reserve.transactions.filter(t => t.id !== id),
    };
    
    onUpdate(updatedReserve);
  };
  
  // Funktion zum Formatieren eines Betrags als Währung
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "de" ? "de-AT" : "en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };
  
  // Funktion zum Formatieren eines Datums
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === "de" ? "de-AT" : "en-US");
  };
  
  // Funktion zum Abrufen des Objektnamens anhand der ID
  const getPropertyName = (propertyId: string) => {
    const property = properties.find(p => p.id === propertyId);
    return property ? property.name : "Unbekanntes Objekt";
  };
  
  // Funktion zum Übersetzen des Transaktionstyps
  const translateTransactionType = (type: ReserveTransactionType) => {
    switch (type) {
      case "contribution":
        return language === "de" ? "Einzahlung" : "Contribution";
      case "withdrawal":
        return language === "de" ? "Auszahlung" : "Withdrawal";
      case "interest":
        return language === "de" ? "Zinsen" : "Interest";
      case "transfer":
        return language === "de" ? "Überweisung" : "Transfer";
      default:
        return type;
    }
  };
  
  // Funktion zum Abrufen des Transaktionstyp-Icons
  const getTransactionTypeIcon = (type: ReserveTransactionType) => {
    switch (type) {
      case "contribution":
        return <ArrowUp className="h-4 w-4 text-green-500" />;
      case "withdrawal":
        return <ArrowDown className="h-4 w-4 text-red-500" />;
      case "interest":
        return <TrendingUp className="h-4 w-4 text-blue-500" />;
      case "transfer":
        return <ArrowLeft className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };
  
  // Funktion zum Berechnen des Fortschritts einer Rücklage
  const calculateProgress = () => {
    if (!reserve.targetAmount || reserve.targetAmount <= 0) return 100;
    const progress = (reserve.balance / reserve.targetAmount) * 100;
    return Math.min(progress, 100);
  };
  
  // Funktion zum Berechnen der verbleibenden Zeit bis zum Erreichen des Zielbetrags
  const calculateTimeToTarget = () => {
    if (!reserve.targetAmount || reserve.targetAmount <= 0 || reserve.yearlyContribution <= 0) return null;
    if (reserve.balance >= reserve.targetAmount) return 0;
    
    const remainingAmount = reserve.targetAmount - reserve.balance;
    const yearsToTarget = remainingAmount / reserve.yearlyContribution;
    
    return yearsToTarget;
  };
  
  // Sortiere Transaktionen nach Datum (neueste zuerst)
  const sortedTransactions = [...reserve.transactions].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" onClick={onBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-2xl font-bold tracking-tight">{reserve.name}</h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Rücklagedetails" : "Reserve Details"}
            </CardTitle>
            <CardDescription>
              {getPropertyName(reserve.propertyId)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Aktueller Stand" : "Current Balance"}
                </span>
                <span className="font-medium">{formatCurrency(reserve.balance)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Jährlicher Beitrag" : "Yearly Contribution"}
                </span>
                <span className="font-medium">{formatCurrency(reserve.yearlyContribution)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Monatlicher Beitrag" : "Monthly Contribution"}
                </span>
                <span className="font-medium">{formatCurrency(reserve.yearlyContribution / 12)}</span>
              </div>
              {reserve.targetAmount && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">
                    {language === "de" ? "Zielbetrag" : "Target Amount"}
                  </span>
                  <span className="font-medium">{formatCurrency(reserve.targetAmount)}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Anzahl Transaktionen" : "Number of Transactions"}
                </span>
                <span className="font-medium">{reserve.transactions.length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Beschreibung" : "Description"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              {reserve.description || (language === "de" ? "Keine Beschreibung vorhanden" : "No description available")}
            </p>
            
            {reserve.targetAmount && (
              <div className="mt-4 space-y-2">
                <h4 className="text-sm font-medium">
                  {language === "de" ? "Fortschritt" : "Progress"}
                </h4>
                <div className="w-full bg-muted rounded-full h-2.5">
                  <div 
                    className="bg-primary h-2.5 rounded-full" 
                    style={{ width: `${calculateProgress()}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>
                    {formatCurrency(reserve.balance)} / {formatCurrency(reserve.targetAmount)}
                  </span>
                  <span>
                    {Math.round(calculateProgress())}%
                  </span>
                </div>
                
                {calculateTimeToTarget() !== null && (
                  <div className="text-sm mt-2">
                    {language === "de" 
                      ? `Voraussichtliche Zeit bis zum Erreichen des Zielbetrags: ${calculateTimeToTarget()?.toFixed(1)} Jahre` 
                      : `Estimated time to reach target: ${calculateTimeToTarget()?.toFixed(1)} years`}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      <div className="flex justify-end gap-2">
        <Button 
          variant="outline"
          onClick={() => setIsAddTransactionDialogOpen(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          {language === "de" ? "Transaktion hinzufügen" : "Add Transaction"}
        </Button>
        
        <Button 
          variant="outline"
          onClick={() => setIsEditing(true)}
        >
          <Edit className="h-4 w-4 mr-2" />
          {language === "de" ? "Bearbeiten" : "Edit"}
        </Button>
        
        <Button 
          variant="destructive"
          onClick={() => onDelete(reserve.id)}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {language === "de" ? "Löschen" : "Delete"}
        </Button>
      </div>
      
      <Tabs defaultValue="transactions" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="transactions">
            {language === "de" ? "Transaktionen" : "Transactions"}
          </TabsTrigger>
          <TabsTrigger value="reports">
            {language === "de" ? "Berichte" : "Reports"}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="transactions" className="space-y-4 mt-4">
          {sortedTransactions.length > 0 ? (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{language === "de" ? "Datum" : "Date"}</TableHead>
                    <TableHead>{language === "de" ? "Typ" : "Type"}</TableHead>
                    <TableHead>{language === "de" ? "Beschreibung" : "Description"}</TableHead>
                    <TableHead className="text-right">{language === "de" ? "Betrag" : "Amount"}</TableHead>
                    <TableHead className="w-[80px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{formatDate(transaction.date)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getTransactionTypeIcon(transaction.type)}
                          <span>{translateTransactionType(transaction.type)}</span>
                        </div>
                      </TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell className="text-right">
                        <span className={transaction.type === "withdrawal" ? "text-red-500" : transaction.type === "contribution" || transaction.type === "interest" ? "text-green-500" : ""}>
                          {transaction.type === "withdrawal" ? "-" : transaction.type === "contribution" || transaction.type === "interest" ? "+" : ""}
                          {formatCurrency(transaction.amount)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => handleDeleteTransaction(transaction.id)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-6 border rounded-md">
              <p className="text-sm text-muted-foreground">
                {language === "de" 
                  ? "Keine Transaktionen vorhanden. Fügen Sie eine neue Transaktion hinzu." 
                  : "No transactions available. Add a new transaction."}
              </p>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="reports" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {language === "de" ? "Berichte" : "Reports"}
              </CardTitle>
              <CardDescription>
                {language === "de" 
                  ? "Generieren Sie Berichte für diese Rücklage." 
                  : "Generate reports for this reserve."}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">
                      {language === "de" ? "Jahresbericht" : "Annual Report"}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">
                      {language === "de" 
                        ? "Generieren Sie einen Jahresbericht für diese Rücklage." 
                        : "Generate an annual report for this reserve."}
                    </p>
                    <Button variant="outline" className="w-full">
                      <FileText className="h-4 w-4 mr-2" />
                      {language === "de" ? "Bericht generieren" : "Generate Report"}
                    </Button>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">
                      {language === "de" ? "Transaktionsbericht" : "Transaction Report"}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">
                      {language === "de" 
                        ? "Exportieren Sie alle Transaktionen dieser Rücklage." 
                        : "Export all transactions for this reserve."}
                    </p>
                    <Button variant="outline" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      {language === "de" ? "Exportieren" : "Export"}
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Bearbeitungsdialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {language === "de" ? "Rücklage bearbeiten" : "Edit Reserve"}
            </DialogTitle>
            <DialogDescription>
              {language === "de" 
                ? "Bearbeiten Sie die Details der Rücklage." 
                : "Edit the details of the reserve."}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-propertyId">
                {language === "de" ? "Objekt" : "Property"}
              </Label>
              <Select
                value={editedReserve.propertyId}
                onValueChange={(value) => setEditedReserve({...editedReserve, propertyId: value})}
              >
                <SelectTrigger id="edit-propertyId">
                  <SelectValue placeholder={language === "de" ? "Objekt auswählen" : "Select property"} />
                </SelectTrigger>
                <SelectContent>
                  {properties.map((property) => (
                    <SelectItem key={property.id} value={property.id}>
                      {property.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-name">
                {language === "de" ? "Name" : "Name"}
              </Label>
              <Input
                id="edit-name"
                value={editedReserve.name}
                onChange={(e) => setEditedReserve({...editedReserve, name: e.target.value})}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-description">
                {language === "de" ? "Beschreibung" : "Description"}
              </Label>
              <Textarea
                id="edit-description"
                value={editedReserve.description}
                onChange={(e) => setEditedReserve({...editedReserve, description: e.target.value})}
                rows={3}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-yearlyContribution">
                  {language === "de" ? "Jährlicher Beitrag (€)" : "Yearly Contribution (€)"}
                </Label>
                <Input
                  id="edit-yearlyContribution"
                  type="number"
                  step="0.01"
                  value={editedReserve.yearlyContribution}
                  onChange={(e) => setEditedReserve({...editedReserve, yearlyContribution: parseFloat(e.target.value) || 0})}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-targetAmount">
                  {language === "de" ? "Zielbetrag (€, optional)" : "Target Amount (€, optional)"}
                </Label>
                <Input
                  id="edit-targetAmount"
                  type="number"
                  step="0.01"
                  value={editedReserve.targetAmount || ""}
                  onChange={(e) => {
                    const value = e.target.value ? parseFloat(e.target.value) : undefined;
                    setEditedReserve({...editedReserve, targetAmount: value});
                  }}
                />
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button onClick={handleUpdateReserve}>
              {language === "de" ? "Speichern" : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Dialog zum Hinzufügen einer Transaktion */}
      <Dialog open={isAddTransactionDialogOpen} onOpenChange={setIsAddTransactionDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {language === "de" ? "Neue Transaktion hinzufügen" : "Add New Transaction"}
            </DialogTitle>
            <DialogDescription>
              {language === "de" 
                ? "Geben Sie die Details der neuen Transaktion ein." 
                : "Enter the details of the new transaction."}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="type">
                {language === "de" ? "Transaktionstyp" : "Transaction Type"}
              </Label>
              <Select
                value={newTransaction.type}
                onValueChange={(value: ReserveTransactionType) => setNewTransaction({...newTransaction, type: value})}
              >
                <SelectTrigger id="type">
                  <SelectValue placeholder={language === "de" ? "Typ auswählen" : "Select type"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="contribution">
                    {language === "de" ? "Einzahlung" : "Contribution"}
                  </SelectItem>
                  <SelectItem value="withdrawal">
                    {language === "de" ? "Auszahlung" : "Withdrawal"}
                  </SelectItem>
                  <SelectItem value="interest">
                    {language === "de" ? "Zinsen" : "Interest"}
                  </SelectItem>
                  <SelectItem value="transfer">
                    {language === "de" ? "Überweisung" : "Transfer"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="date">
                {language === "de" ? "Datum" : "Date"}
              </Label>
              <Input
                id="date"
                type="date"
                value={newTransaction.date}
                onChange={(e) => setNewTransaction({...newTransaction, date: e.target.value})}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="amount">
                {language === "de" ? "Betrag (€)" : "Amount (€)"}
              </Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                value={newTransaction.amount}
                onChange={(e) => setNewTransaction({...newTransaction, amount: parseFloat(e.target.value) || 0})}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">
                {language === "de" ? "Beschreibung" : "Description"}
              </Label>
              <Input
                id="description"
                value={newTransaction.description}
                onChange={(e) => setNewTransaction({...newTransaction, description: e.target.value})}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddTransactionDialogOpen(false)}>
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button onClick={handleAddTransaction}>
              {language === "de" ? "Hinzufügen" : "Add"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
