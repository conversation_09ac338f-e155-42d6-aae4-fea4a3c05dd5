"use client"

import { buttonVariants } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { ReactNode, useEffect, useState } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext';

export function InvoicesPageClient({ children }: { children: ReactNode }) {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold">
              {mounted ? t('invoices') : "Rechnungen"}
            </CardTitle>
            <CardDescription>
              {mounted ? t('manageInvoices') : "Verwalten Sie Ihre Rechnungen hier"}
            </CardDescription>
          </div>
          <Link href="/dashboard/invoices/create" className={buttonVariants()}>
            <PlusIcon /> {mounted ? t('createInvoice') : "Rechnung erstellen"}
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
}
