"use client"

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "../utils/formatCurrency";
import { useLanguage } from '@/app/contexts/LanguageContext';
import { useEffect, useState } from 'react';

interface RecentInvoicesClientProps {
  data: Array<{
    id: string;
    clientName: string;
    clientEmail: string;
    total: number;
    currency: string;
  }>;
}

export function RecentInvoicesClient({ data }: RecentInvoicesClientProps) {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {mounted ? t('recentInvoices') : "Recent Invoices"}
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-8">
        {data.map((item) => (
          <div className="flex items-center gap-4" key={item.id}>
            <Avatar className="hidden sm:flex size-9">
              <AvatarFallback>{item.clientName.slice(0, 2)}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col gap-1">
              <p className="text-sm font-medium leadin-none">
                {item.clientName}
              </p>
              <p className="text-sm text-muted-foreground">
                {item.clientEmail}
              </p>
            </div>
            <div className="ml-auto font-medium">
              +
              {formatCurrency({
                amount: item.total,
                currency: item.currency as any,
              })}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
