"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, Users, Calculator, Receipt, PiggyBank, FileText, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { LoadingState } from "@/app/components/LoadingState";
import { UserAdvisor } from "@/app/lib/definitions";

interface AdvisorViewDashboardProps {
  userAdvisor: any; // Using any temporarily since we're passing the Prisma result directly
  token: string;
}

export function AdvisorViewDashboard({ userAdvisor, token }: AdvisorViewDashboardProps) {
  const [activeTab, setActiveTab] = useState("dashboard");
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({
    dashboard: false,
    weg: false,
    invoices: false,
    employees: false
  });

  // Extract user and advisor information
  const { user, advisor, canViewWEG, canViewInvoices, canViewEmployees, canDownload } = userAdvisor;

  // Funktion zum Setzen des Loading-States für einen Tab
  const setTabLoading = useCallback((tab: string, loading: boolean) => {
    setIsLoading(prev => ({ ...prev, [tab]: loading }));
  }, []);

  // Setzen des Loading-States beim Tab-Wechsel
  const handleTabChange = (tab: string) => {
    console.log("Advisor view: Tab changed to:", tab);
    setActiveTab(tab);

    // Setze den Loading-State für alle Tabs, auch Dashboard
    console.log("Advisor view: Setting loading state for tab:", tab);
    setTabLoading(tab, true);

    // Simuliere eine Ladezeit (kürzer für Dashboard, länger für andere Tabs)
    const loadingTime = tab === 'dashboard' ? 3000 : 5000; // Längere Zeiten zum Testen
    setTimeout(() => {
      console.log("Advisor view: Loading finished for tab:", tab);
      setTabLoading(tab, false);
    }, loadingTime);
  };

  // Setze den Loading-State für den aktiven Tab beim ersten Laden
  useEffect(() => {
    console.log("Advisor view: Initial loading for tab:", activeTab);
    setTabLoading(activeTab, true);

    // Simuliere eine Ladezeit für den initialen Tab
    const loadingTime = 3000;
    setTimeout(() => {
      console.log("Advisor view: Initial loading finished for tab:", activeTab);
      setTabLoading(activeTab, false);
    }, loadingTime);
  }, [activeTab, setTabLoading]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">
            Berater-Ansicht
          </h1>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              Angemeldet als: <strong>{advisor.name}</strong>
            </span>
            <Button variant="outline" size="sm" asChild>
              <a href="/login">Abmelden</a>
            </Button>
          </div>
        </div>
        <p className="text-muted-foreground">
          Sie sehen die Daten von <strong>{user.firstName} {user.lastName}</strong> ({user.email})
        </p>
      </div>

      <Tabs
        defaultValue="dashboard"
        className="space-y-4"
        value={activeTab}
        onValueChange={handleTabChange}
      >
        <div className="flex flex-col space-y-2">
          <div className="flex overflow-x-auto pb-2 scrollbar-hide">
            <TabsList className="flex h-auto">
              <TabsTrigger value="dashboard" className="flex items-center gap-2 py-2 px-4">
                <Building className="h-4 w-4" />
                <span>Übersicht</span>
              </TabsTrigger>

              {canViewWEG && (
                <>
                  <div className="flex items-center px-2">
                    <div className="h-8 w-px bg-muted-foreground/20"></div>
                  </div>
                  <TabsTrigger value="weg" className="flex items-center gap-2 py-2 px-4">
                    <Building className="h-4 w-4" />
                    <span>WEG-Abrechnung</span>
                  </TabsTrigger>
                </>
              )}

              {canViewInvoices && (
                <>
                  <div className="flex items-center px-2">
                    <div className="h-8 w-px bg-muted-foreground/20"></div>
                  </div>
                  <TabsTrigger value="invoices" className="flex items-center gap-2 py-2 px-4">
                    <Receipt className="h-4 w-4" />
                    <span>Rechnungen</span>
                  </TabsTrigger>
                </>
              )}

              {canViewEmployees && (
                <>
                  <div className="flex items-center px-2">
                    <div className="h-8 w-px bg-muted-foreground/20"></div>
                  </div>
                  <TabsTrigger value="employees" className="flex items-center gap-2 py-2 px-4">
                    <Users className="h-4 w-4" />
                    <span>Mitarbeiter</span>
                  </TabsTrigger>
                </>
              )}
            </TabsList>
          </div>
        </div>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-4">
          {isLoading.dashboard ? (
            <LoadingState
              title="Dashboard wird geladen..."
              description="Bitte warten Sie, während die Übersichtsdaten geladen werden."
            />
          ) : (
            <>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {canViewWEG && (
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        WEG-Abrechnung
                      </CardTitle>
                      <Building className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">Verfügbar</div>
                      <p className="text-xs text-muted-foreground">
                        Klicken Sie auf den Tab &quot;WEG-Abrechnung&quot;, um die Daten einzusehen
                      </p>
                    </CardContent>
                  </Card>
                )}

                {canViewInvoices && (
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Rechnungen
                      </CardTitle>
                      <Receipt className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">Verfügbar</div>
                      <p className="text-xs text-muted-foreground">
                        Klicken Sie auf den Tab &quot;Rechnungen&quot;, um die Daten einzusehen
                      </p>
                    </CardContent>
                  </Card>
                )}

                {canViewEmployees && (
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Mitarbeiter
                      </CardTitle>
                      <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">Verfügbar</div>
                      <p className="text-xs text-muted-foreground">
                        Klicken Sie auf den Tab &quot;Mitarbeiter&quot;, um die Daten einzusehen
                      </p>
                    </CardContent>
                  </Card>
                )}

                {canDownload && (
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Download
                      </CardTitle>
                      <Download className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">Verfügbar</div>
                      <p className="text-xs text-muted-foreground">
                        Sie können Daten herunterladen
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Willkommen in der Berater-Ansicht</CardTitle>
                  <CardDescription>
                    Hier können Sie die freigegebenen Daten einsehen
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p>
                    Sie wurden von {user.firstName} {user.lastName} als Berater hinzugefügt und haben Zugriff auf bestimmte Daten.
                    Verwenden Sie die Tabs oben, um zwischen den verschiedenen Bereichen zu navigieren.
                  </p>
                  <p className="mt-4">
                    Bitte beachten Sie, dass alle Aktionen protokolliert werden und Sie keine Daten bearbeiten können.
                  </p>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* WEG Tab */}
        <TabsContent value="weg">
          {isLoading.weg ? (
            <LoadingState
              title="WEG-Abrechnung wird geladen..."
              description="Bitte warten Sie, während die Daten geladen werden."
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>WEG-Abrechnung</CardTitle>
                <CardDescription>
                  Hier sehen Sie die WEG-Abrechnungsdaten
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center py-8 text-muted-foreground">
                  Die vollständige Implementierung der WEG-Abrechnungsansicht für Berater wird in einem zukünftigen Update hinzugefügt.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Invoices Tab */}
        <TabsContent value="invoices">
          {isLoading.invoices ? (
            <LoadingState
              title="Rechnungen werden geladen..."
              description="Bitte warten Sie, während die Daten geladen werden."
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Rechnungen</CardTitle>
                <CardDescription>
                  Hier sehen Sie die Rechnungsdaten
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center py-8 text-muted-foreground">
                  Die vollständige Implementierung der Rechnungsansicht für Berater wird in einem zukünftigen Update hinzugefügt.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Employees Tab */}
        <TabsContent value="employees">
          {isLoading.employees ? (
            <LoadingState
              title="Mitarbeiter werden geladen..."
              description="Bitte warten Sie, während die Daten geladen werden."
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Mitarbeiter</CardTitle>
                <CardDescription>
                  Hier sehen Sie die Mitarbeiterdaten
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center py-8 text-muted-foreground">
                  Die vollständige Implementierung der Mitarbeiteransicht für Berater wird in einem zukünftigen Update hinzugefügt.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
