"use client";

import { useState, useEffect } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Edit,
  Trash2,
  Clock
} from "lucide-react";
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, isToday } from "date-fns";
import { de } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { TimeEntryForm } from "./TimeEntryForm";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/app/context/ToastContext";

// Mock data for time entries (same as in TimeEntriesList)
const mockTimeEntries = [
  {
    id: "1",
    description: "Website Development",
    date: new Date(),
    startTime: new Date(new Date().setHours(9, 0, 0, 0)),
    endTime: new Date(new Date().setHours(12, 30, 0, 0)),
    duration: 210, // 3.5 hours in minutes
    projectId: "1",
    projectName: "Client Website",
    projectColor: "#4f46e5",
    employeeId: "",
    employeeName: "",
    isBillable: true
  },
  {
    id: "2",
    description: "Meeting with Client",
    date: new Date(),
    startTime: new Date(new Date().setHours(14, 0, 0, 0)),
    endTime: new Date(new Date().setHours(15, 0, 0, 0)),
    duration: 60, // 1 hour in minutes
    projectId: "1",
    projectName: "Client Website",
    projectColor: "#4f46e5",
    employeeId: "",
    employeeName: "",
    isBillable: true
  },
  {
    id: "3",
    description: "Documentation",
    date: new Date(new Date().setDate(new Date().getDate() - 1)), // Yesterday
    startTime: new Date(new Date().setDate(new Date().getDate() - 1)).setHours(10, 0, 0, 0),
    endTime: new Date(new Date().setDate(new Date().getDate() - 1)).setHours(12, 0, 0, 0),
    duration: 120, // 2 hours in minutes
    projectId: "2",
    projectName: "Internal Project",
    projectColor: "#10b981",
    employeeId: "",
    employeeName: "",
    isBillable: false
  },
  {
    id: "4",
    description: "Design Review",
    date: new Date(new Date().setDate(new Date().getDate() - 2)),
    startTime: new Date(new Date().setDate(new Date().getDate() - 2)).setHours(13, 0, 0, 0),
    endTime: new Date(new Date().setDate(new Date().getDate() - 2)).setHours(15, 30, 0, 0),
    duration: 150, // 2.5 hours in minutes
    projectId: "3",
    projectName: "Marketing Campaign",
    projectColor: "#f59e0b",
    employeeId: "1",
    employeeName: "Max Mustermann",
    isBillable: true
  }
];

export function TimeCalendarView() {
  const { language, t } = useLanguage();
  const { toast } = useToast();
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [timeEntries, setTimeEntries] = useState<any[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedDateEntries, setSelectedDateEntries] = useState<any[]>([]);
  const [projectFilter, setProjectFilter] = useState("all");
  const [employeeFilter, setEmployeeFilter] = useState("all");
  const [projects, setProjects] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [entryToEdit, setEntryToEdit] = useState<any | null>(null);

  // Format date based on language
  const formatDate = (date: Date, formatStr: string = "PPP") => {
    return format(
      date,
      formatStr,
      { locale: language === "de" ? de : undefined }
    );
  };

  // Format time
  const formatTime = (date: Date) => {
    return format(date, "HH:mm");
  };

  // Format duration
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}:${mins.toString().padStart(2, '0')}`;
  };

  // Navigate to previous month
  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  // Navigate to next month
  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  // Navigate to current month
  const goToToday = () => {
    setCurrentMonth(new Date());
    setSelectedDate(new Date());
  };

  // Load data
  useEffect(() => {
    // Simulate loading data from API
    setTimeout(() => {
      setTimeEntries(mockTimeEntries);

      // Extract unique projects and employees
      const uniqueProjects = Array.from(
        new Set(mockTimeEntries.map(entry => entry.projectId))
      ).map(id => {
        const entry = mockTimeEntries.find(e => e.projectId === id);
        return {
          id,
          name: entry?.projectName || "",
          color: entry?.projectColor || "#000000"
        };
      });

      const uniqueEmployees = Array.from(
        new Set(mockTimeEntries.filter(e => e.employeeId).map(entry => entry.employeeId))
      ).map(id => {
        const entry = mockTimeEntries.find(e => e.employeeId === id);
        return {
          id,
          name: entry?.employeeName || ""
        };
      });

      setProjects(uniqueProjects);
      setEmployees(uniqueEmployees);

      // Set today as selected date
      setSelectedDate(new Date());
    }, 500);
  }, []);

  // Update selected date entries when date or filters change
  useEffect(() => {
    if (selectedDate) {
      let filtered = timeEntries.filter(entry =>
        isSameDay(new Date(entry.date), selectedDate)
      );

      // Apply project filter
      if (projectFilter && projectFilter !== "all") {
        filtered = filtered.filter(entry => entry.projectId === projectFilter);
      }

      // Apply employee filter
      if (employeeFilter) {
        if (employeeFilter === "self") {
          filtered = filtered.filter(entry => !entry.employeeId);
        } else if (employeeFilter !== "all") {
          filtered = filtered.filter(entry => entry.employeeId === employeeFilter);
        }
      }

      // Sort by start time
      filtered.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());

      setSelectedDateEntries(filtered);
    } else {
      setSelectedDateEntries([]);
    }
  }, [selectedDate, timeEntries, projectFilter, employeeFilter]);

  // Get entries for a specific date
  const getEntriesForDate = (date: Date) => {
    let entries = timeEntries.filter(entry =>
      isSameDay(new Date(entry.date), date)
    );

    // Apply project filter
    if (projectFilter && projectFilter !== "all") {
      entries = entries.filter(entry => entry.projectId === projectFilter);
    }

    // Apply employee filter
    if (employeeFilter) {
      if (employeeFilter === "self") {
        entries = entries.filter(entry => !entry.employeeId);
      } else if (employeeFilter !== "all") {
        entries = entries.filter(entry => entry.employeeId === employeeFilter);
      }
    }

    return entries;
  };

  // Get total duration for a specific date
  const getTotalDurationForDate = (date: Date) => {
    const entries = getEntriesForDate(date);
    return entries.reduce((total, entry) => total + (entry.duration || 0), 0);
  };

  // Edit entry
  const handleEditEntry = (entry: any) => {
    setEntryToEdit(entry);
    setIsEditDialogOpen(true);
  };

  // Delete entry
  const handleDeleteEntry = (entryId: string) => {
    // Confirm deletion
    if (confirm(language === "de" ? "Möchten Sie diesen Zeiteintrag wirklich löschen?" : "Are you sure you want to delete this time entry?")) {
      setTimeEntries(prev => prev.filter(entry => entry.id !== entryId));

      toast.success(
        language === "de" ? "Zeiteintrag gelöscht" : "Time entry deleted",
        { description: language === "de" ? "Der Zeiteintrag wurde erfolgreich gelöscht." : "The time entry was successfully deleted." }
      );
    }
  };

  // Add new entry for selected date
  const addEntryForSelectedDate = () => {
    if (selectedDate) {
      setEntryToEdit({
        description: "",
        date: selectedDate,
        startTime: new Date(selectedDate).setHours(9, 0, 0, 0),
        endTime: null,
        duration: null,
        projectId: "",
        projectName: "",
        projectColor: "",
        employeeId: "",
        employeeName: "",
        isBillable: true
      });
      setIsEditDialogOpen(true);
    }
  };

  // Generate calendar days
  const calendarDays = () => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const dateRange = eachDayOfInterval({ start: monthStart, end: monthEnd });

    // Get day names
    const dayNames = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(2021, 0, i + 1); // Use a Sunday as the first day
      return formatDate(date, language === "de" ? "EEEEEE" : "EEEEEE");
    });

    // Calculate the day of the week for the first day of the month (0 = Sunday, 1 = Monday, etc.)
    const firstDayOfMonth = monthStart.getDay();

    // Create an array for the days of the previous month to fill the first row
    const prevMonthDays = Array.from({ length: firstDayOfMonth }, (_, i) => {
      const day = new Date(monthStart);
      day.setDate(day.getDate() - (firstDayOfMonth - i));
      return day;
    });

    // Create an array for the days of the next month to fill the last row
    const totalCells = Math.ceil((dateRange.length + firstDayOfMonth) / 7) * 7;
    const nextMonthDays = Array.from({ length: totalCells - (dateRange.length + firstDayOfMonth) }, (_, i) => {
      const day = new Date(monthEnd);
      day.setDate(day.getDate() + i + 1);
      return day;
    });

    // Combine all days
    const allDays = [...prevMonthDays, ...dateRange, ...nextMonthDays];

    return { dayNames, allDays };
  };

  const { dayNames, allDays } = calendarDays();

  return (
    <div className="space-y-6">
      {/* Calendar Header */}
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={prevMonth}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-xl font-semibold min-w-[200px] text-center">
            {formatDate(currentMonth, "MMMM yyyy")}
          </h2>
          <Button variant="outline" size="icon" onClick={nextMonth}>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button variant="outline" onClick={goToToday}>
            {language === "de" ? "Heute" : "Today"}
          </Button>
        </div>

        <div className="flex gap-2">
          {/* Project Filter */}
          <Select value={projectFilter} onValueChange={setProjectFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={language === "de" ? "Projekt" : "Project"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {language === "de" ? "Alle Projekte" : "All Projects"}
              </SelectItem>
              {projects.map(project => (
                <SelectItem key={project.id} value={project.id}>
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: project.color }}
                    />
                    {project.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Employee Filter */}
          <Select value={employeeFilter} onValueChange={setEmployeeFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={language === "de" ? "Mitarbeiter" : "Employee"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {language === "de" ? "Alle Mitarbeiter" : "All Employees"}
              </SelectItem>
              <SelectItem value="self">
                {language === "de" ? "Ich selbst" : "Myself"}
              </SelectItem>
              {employees.map(employee => (
                <SelectItem key={employee.id} value={employee.id}>
                  {employee.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Calendar Grid */}
      <Card>
        <CardContent className="p-4">
          {/* Day Names */}
          <div className="grid grid-cols-7 gap-1 mb-1">
            {dayNames.map((day, index) => (
              <div
                key={index}
                className="text-center text-sm font-medium text-muted-foreground p-2"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Days */}
          <div className="grid grid-cols-7 gap-1">
            {allDays.map((day, index) => {
              const isCurrentMonth = isSameMonth(day, currentMonth);
              const isSelected = selectedDate && isSameDay(day, selectedDate);
              const isDayToday = isToday(day);
              const entries = getEntriesForDate(day);
              const totalDuration = getTotalDurationForDate(day);

              return (
                <div
                  key={index}
                  className={cn(
                    "min-h-[100px] p-2 border rounded-md",
                    isCurrentMonth ? "bg-background" : "bg-muted/30",
                    isSelected ? "border-primary" : "border-border",
                    isDayToday && "bg-muted/50",
                    "cursor-pointer hover:border-primary/50 transition-colors"
                  )}
                  onClick={() => setSelectedDate(day)}
                >
                  <div className="flex justify-between items-start">
                    <span
                      className={cn(
                        "text-sm font-medium",
                        !isCurrentMonth && "text-muted-foreground",
                        isDayToday && "bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center"
                      )}
                    >
                      {format(day, "d")}
                    </span>

                    {totalDuration > 0 && (
                      <span className="text-xs text-muted-foreground">
                        {formatDuration(totalDuration)}
                      </span>
                    )}
                  </div>

                  {/* Entry Indicators */}
                  <div className="mt-1 space-y-1">
                    {entries.slice(0, 3).map((entry, i) => (
                      <div
                        key={i}
                        className="text-xs truncate rounded px-1 py-0.5"
                        style={{
                          backgroundColor: entry.projectColor ? `${entry.projectColor}20` : '#f1f5f9',
                          borderLeft: `3px solid ${entry.projectColor || '#94a3b8'}`
                        }}
                      >
                        {formatTime(new Date(entry.startTime))} {entry.description}
                      </div>
                    ))}

                    {entries.length > 3 && (
                      <div className="text-xs text-muted-foreground">
                        +{entries.length - 3} {language === "de" ? "mehr" : "more"}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Selected Date Entries */}
      {selectedDate && (
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {formatDate(selectedDate)}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={addEntryForSelectedDate}
              >
                {language === "de" ? "Eintrag hinzufügen" : "Add Entry"}
              </Button>
            </div>

            {selectedDateEntries.length > 0 ? (
              <div className="space-y-2">
                {selectedDateEntries.map(entry => (
                  <div
                    key={entry.id}
                    className="flex items-center justify-between p-3 border rounded-md hover:bg-muted/30 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: entry.projectColor || '#94a3b8' }}
                      />
                      <div>
                        <div className="font-medium">{entry.description}</div>
                        <div className="text-sm text-muted-foreground">
                          {formatTime(new Date(entry.startTime))}
                          {entry.endTime && ` - ${formatTime(new Date(entry.endTime))}`}
                          {entry.duration && ` (${formatDuration(entry.duration)})`}
                          {entry.projectName && ` • ${entry.projectName}`}
                          {entry.employeeName && ` • ${entry.employeeName}`}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-1">
                      {entry.isBillable && (
                        <Badge variant="outline" className="text-xs">
                          {language === "de" ? "Abrechenbar" : "Billable"}
                        </Badge>
                      )}

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditEntry(entry)}>
                            <Edit className="h-4 w-4 mr-2" />
                            {language === "de" ? "Bearbeiten" : "Edit"}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDeleteEntry(entry.id)}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            {language === "de" ? "Löschen" : "Delete"}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Clock className="h-8 w-8 mx-auto text-muted-foreground" />
                <p className="mt-2 text-muted-foreground">
                  {language === "de"
                    ? "Keine Zeiteinträge für diesen Tag."
                    : "No time entries for this day."}
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={addEntryForSelectedDate}
                >
                  {language === "de" ? "Eintrag hinzufügen" : "Add Entry"}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Edit Dialog */}
      {isEditDialogOpen && (
        <TimeEntryForm
          isOpen={isEditDialogOpen}
          onClose={() => {
            setIsEditDialogOpen(false);
            setEntryToEdit(null);
          }}
          entryToEdit={entryToEdit}
        />
      )}
    </div>
  );
}
