"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/app/utils/clientHooks";
import { AccountingTab } from "@/app/components/weg-accounting/accounting/AccountingTab";
import { LoadingState } from "@/app/components/LoadingState";
import { Property, Owner, ExpenseCategory, AccountingPeriod } from "@/app/lib/wegTypes";

export default function AccountingPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [properties, setProperties] = useState<Property[]>([]);
  const [owners, setOwners] = useState<Owner[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<ExpenseCategory[]>([]);
  const [accountingPeriods, setAccountingPeriods] = useState<AccountingPeriod[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Function to refresh accounting periods data from server
  const refreshAccountingPeriods = async () => {
    try {
      console.log("Refreshing accounting periods from server...");
      const accountingPeriodsResponse = await fetch('/api/weg-accounting/accounting-periods');
      if (!accountingPeriodsResponse.ok) {
        throw new Error(`Failed to fetch accounting periods: ${accountingPeriodsResponse.statusText}`);
      }
      const accountingPeriodsData = await accountingPeriodsResponse.json();
      setAccountingPeriods(accountingPeriodsData);
      console.log("Accounting periods refreshed successfully:", accountingPeriodsData);
    } catch (error) {
      console.error("Failed to refresh accounting periods:", error);
    }
  };

  useEffect(() => {
    // Only load data if the user is authenticated
    if (isAuthenticated) {
      const loadData = async () => {
        try {
          setIsLoading(true);
          // Fetch properties from API
          const propertiesResponse = await fetch('/api/weg-accounting/properties');
          if (!propertiesResponse.ok) {
            throw new Error(`Failed to fetch properties: ${propertiesResponse.statusText}`);
          }
          const propertiesData = await propertiesResponse.json();
          setProperties(propertiesData);

          // Fetch owners from API
          const ownersResponse = await fetch('/api/weg-accounting/owners');
          if (!ownersResponse.ok) {
            throw new Error(`Failed to fetch owners: ${ownersResponse.statusText}`);
          }
          const ownersData = await ownersResponse.json();
          setOwners(ownersData);

          // Fetch expense categories from API
          const expenseCategoriesResponse = await fetch('/api/weg-accounting/expense-categories');
          if (!expenseCategoriesResponse.ok) {
            throw new Error(`Failed to fetch expense categories: ${expenseCategoriesResponse.statusText}`);
          }
          const expenseCategoriesData = await expenseCategoriesResponse.json();
          setExpenseCategories(expenseCategoriesData);

          // Fetch accounting periods from API
          const accountingPeriodsResponse = await fetch('/api/weg-accounting/accounting-periods');
          if (!accountingPeriodsResponse.ok) {
            throw new Error(`Failed to fetch accounting periods: ${accountingPeriodsResponse.statusText}`);
          }
          const accountingPeriodsData = await accountingPeriodsResponse.json();
          setAccountingPeriods(accountingPeriodsData);
        } catch (error) {
          console.error("Failed to load data:", error);
        } finally {
          setIsLoading(false);
        }
      };

      loadData();
    }
  }, [isAuthenticated]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Abrechnung</h1>
        <p className="text-muted-foreground">
          Erstellen und verwalten Sie Ihre Abrechnungen
        </p>
      </div>

      {authLoading || isLoading ? (
        <LoadingState
          title="Abrechnungsdaten werden geladen..."
          description="Bitte warten Sie, während die Daten geladen werden."
        />
      ) : (
        <AccountingTab
          properties={properties}
          owners={owners}
          expenseCategories={expenseCategories}
          accountingPeriods={accountingPeriods}
          onRefreshAccountingPeriods={refreshAccountingPeriods}
        />
      )}
    </div>
  );
}
