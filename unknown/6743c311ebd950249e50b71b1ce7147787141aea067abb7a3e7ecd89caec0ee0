"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

// Client-side hook for authentication
export function useAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Use fetch to call a server endpoint to check authentication
        const response = await fetch('/api/auth/session');
        const data = await response.json();

        if (data && data.user) {
          setIsAuthenticated(true);
          setUser(data.user);
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error("Authentication error:", error);
        router.push('/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  return { isAuthenticated, isLoading, user };
}
