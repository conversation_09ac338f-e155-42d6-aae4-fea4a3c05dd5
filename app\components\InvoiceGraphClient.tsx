"use client"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Graph } from "./Graph";
import { useLanguage } from '@/app/contexts/LanguageContext';
import { useEffect, useState } from 'react';

interface InvoiceGraphClientProps {
  data: Array<{ date: string; amount: number }>;
}

export function InvoiceGraphClient({ data }: InvoiceGraphClientProps) {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <Card className="lg:col-span-2">
      <CardHeader>
        <CardTitle>
          {mounted ? t('paidInvoices') : "Bezahlte Rechnungen"}
        </CardTitle>
        <CardDescription>
          {mounted ? t('paidInvoicesDesc') : "Alle Rechnungen, die bereits bezahlt wurden!"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Graph data={data} />
      </CardContent>
    </Card>
  );
}
