"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import type { LanguageKey } from "@/i18n";
import { translations } from "@/i18n";

type LanguageContextType = {
  language: LanguageKey;
  setLanguage: (lang: LanguageKey) => void;
  t: (key: string) => string;
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ 
  children, 
  defaultLanguage = "de" 
}: { 
  children: ReactNode;
  defaultLanguage?: LanguageKey;
}) {
  const [language, setLanguage] = useState<LanguageKey>(defaultLanguage);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Nur localStorage prüfen, wenn es verfügbar ist (client-side)
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('preferred-language') as LanguageKey;
      if (savedLanguage && (savedLanguage === 'de' || savedLanguage === 'en')) {
        setLanguage(savedLanguage);
        document.documentElement.lang = savedLanguage;
      } else {
        // Wenn keine gespeicherte Sprache vorhanden ist, Deutsch als Standard setzen
        setLanguage('de');
        localStorage.setItem('preferred-language', 'de');
        document.documentElement.lang = 'de';
      }
    }
    setIsInitialized(true);
  }, []);

  const handleSetLanguage = (lang: LanguageKey) => {
    setLanguage(lang);
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-language', lang);
      // Aktualisiere auch das HTML lang attribute
      document.documentElement.lang = lang;
    }
  };

  const value = {
    language,
    setLanguage: handleSetLanguage,
    t: (key: string) => (translations[language] as Record<string, string>)[key] ?? key,
  };

  // Während der Initialisierung Deutsch anzeigen
  if (!isInitialized) {
    const fallbackValue = {
      language: 'de' as LanguageKey,
      setLanguage: handleSetLanguage,
      t: (key: string) => (translations['de'] as Record<string, string>)[key] ?? key,
    };
    
    return (
      <LanguageContext.Provider value={fallbackValue}>
        {children}
      </LanguageContext.Provider>
    );
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
} 