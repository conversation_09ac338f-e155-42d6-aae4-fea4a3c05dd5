import { EditEmployee } from "@/app/components/employees/EditEmployee";
import prisma from "@/app/utils/db";
import { requireUser } from "@/app/utils/hooks";
import { notFound } from "next/navigation";

async function getData(employeeId: string, userId: string) {
  const data = await prisma.employee.findUnique({
    where: {
      id: employeeId,
      userId: userId,
    },
  });

  if (!data) {
    return notFound();
  }

  return data;
}

type Params = { employeeId: string };

export default async function EditEmployeeRoute({ params }: { params: Promise<Params> }) {
  const { employeeId } = await params;
  const session = await requireUser();
  const data = await getData(employeeId, session.user?.id as string);

  return <EditEmployee data={data} />;
}
