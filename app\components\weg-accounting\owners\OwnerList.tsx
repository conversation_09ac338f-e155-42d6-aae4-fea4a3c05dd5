"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Plus, Search, Edit, Trash2, Building, Mail, Phone, Users } from "lucide-react";
import { Property } from "../properties/PropertyList";

// Typen für die Datenstruktur
export type OwnerUnit = {
  id: string;
  propertyId: string;
  unitNumber: string;
  area: number;
  ownershipPercentage: number;
};

export type Owner = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  units: OwnerUnit[];
};

interface OwnerListProps {
  owners: Owner[];
  properties: Property[];
  onSelectOwner: (owner: Owner) => void;
  onUpdateOwners: (owners: Owner[]) => void;
}

export function OwnerList({ owners, properties, onSelectOwner, onUpdateOwners }: OwnerListProps) {
  const { language } = useLanguage();

  // State für das Hinzufügen/Bearbeiten von Eigentümern
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentOwner, setCurrentOwner] = useState<Owner | null>(null);
  const [ownerToDelete, setOwnerToDelete] = useState<Owner | null>(null);
  const [newOwner, setNewOwner] = useState<Omit<Owner, "id" | "units">>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
  });

  // State für die Suche
  const [searchQuery, setSearchQuery] = useState("");

  // Loading states
  const [isAdding, setIsAdding] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Validation states
  const [addErrors, setAddErrors] = useState<{[key: string]: string}>({});
  const [editErrors, setEditErrors] = useState<{[key: string]: string}>({});

  // Validation function
  const validateOwnerData = (data: Omit<Owner, "id" | "units">) => {
    const errors: {[key: string]: string} = {};
    
    if (!data.firstName.trim()) {
      errors.firstName = language === "de" ? "Vorname ist erforderlich" : "First name is required";
    }
    
    if (!data.lastName.trim()) {
      errors.lastName = language === "de" ? "Nachname ist erforderlich" : "Last name is required";
    }
    
    if (!data.email.trim()) {
      errors.email = language === "de" ? "E-Mail ist erforderlich" : "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.email = language === "de" ? "Ungültige E-Mail-Adresse" : "Invalid email address";
    }
    
    return errors;
  };

  // Gefilterte Eigentümer basierend auf der Suche
  const filteredOwners = owners.filter(owner =>
    owner.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    owner.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    owner.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    owner.address.toLowerCase().includes(searchQuery.toLowerCase())
  );  // Handler für das Hinzufügen eines neuen Eigentümers
  const handleAddOwner = async () => {
    const errors = validateOwnerData(newOwner);
    setAddErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      return;
    }

    setIsAdding(true);
    try {
      const response = await fetch('/api/weg-accounting/owners', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newOwner),
      });

      if (!response.ok) {
        throw new Error('Failed to add owner');
      }

      const addedOwner = await response.json();
      
      // Aktualisiere die lokale Liste
      onUpdateOwners([...owners, addedOwner]);

      // Reset form
      setNewOwner({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        address: "",
      });
      setAddErrors({});
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error("Fehler beim Hinzufügen des Eigentümers:", error);
      setAddErrors({ 
        general: language === "de" ? "Fehler beim Hinzufügen des Eigentümers" : "Error adding owner" 
      });
    } finally {
      setIsAdding(false);
    }
  };
  // Handler für das Bearbeiten eines Eigentümers
  const handleEditOwner = async () => {
    if (!currentOwner) return;

    const errors = validateOwnerData(currentOwner);
    setEditErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      return;
    }

    setIsEditing(true);
    try {
      const response = await fetch(`/api/weg-accounting/owners/${currentOwner.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: currentOwner.firstName,
          lastName: currentOwner.lastName,
          email: currentOwner.email,
          phone: currentOwner.phone,
          address: currentOwner.address
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update owner');
      }

      const updatedOwner = await response.json();

      // Aktualisiere die lokale Liste
      onUpdateOwners(owners.map((owner: Owner) =>
        owner.id === updatedOwner.id ? updatedOwner : owner
      ));

      setCurrentOwner(null);
      setEditErrors({});
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Fehler beim Aktualisieren des Eigentümers:", error);
      setEditErrors({ 
        general: language === "de" ? "Fehler beim Aktualisieren des Eigentümers" : "Error updating owner" 
      });
    } finally {
      setIsEditing(false);
    }
  };

  // Handler für das Löschen eines Eigentümers
  const handleDeleteOwner = async (id: string) => {
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/weg-accounting/owners/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete owner');
      }

      // Aktualisiere die lokale Liste
      onUpdateOwners(owners.filter((owner: Owner) => owner.id !== id));
    } catch (error) {
      console.error("Fehler beim Löschen des Eigentümers:", error);
      // Hier könnte eine Toast-Nachricht angezeigt werden
    } finally {
      setIsDeleting(false);
    }
  };

  // Funktion zum Öffnen des Bearbeitungsdialogs
  const openEditDialog = (owner: Owner) => {
    setCurrentOwner({...owner});
    setIsEditDialogOpen(true);
  };

  // Funktion zum Abrufen des Eigentumsanteils eines Eigentümers
  const getOwnershipTotal = (owner: Owner) => {
    return owner.units.reduce((total, unit) => total + unit.ownershipPercentage, 0);
  };

  // Funktion zum Abrufen der Anzahl der Objekte eines Eigentümers
  const getPropertyCount = (owner: Owner) => {
    const propertyIds = new Set(owner.units.map(unit => unit.propertyId));
    return propertyIds.size;
  };
  // Funktion zum Abrufen der Objektnamen eines Eigentümers
  const getPropertyNames = (owner: Owner) => {
    const propertyIds = owner.units.map(unit => unit.propertyId);
    const uniquePropertyIds = [...new Set(propertyIds)];

    return uniquePropertyIds.map(id => {
      const property = properties.find((p: Property) => p.id === id);
      return property ? property.name : "Unbekanntes Objekt";
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">
          {language === "de" ? "Eigentümer" : "Owners"}
        </h2>

        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder={language === "de" ? "Eigentümer suchen..." : "Search owners..."}
              className="pl-8 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>          <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
            if (open) {
              setIsAddDialogOpen(true);
            } else {
              setIsAddDialogOpen(false);
              setAddErrors({});
              setNewOwner({
                firstName: "",
                lastName: "",
                email: "",
                phone: "",
                address: "",
              });
            }
          }}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {language === "de" ? "Neuer Eigentümer" : "New Owner"}
              </Button>
            </DialogTrigger>            <DialogContent className="sm:max-w-[500px] bg-white dark:bg-gray-900">
              <DialogHeader>
                <DialogTitle>
                  {language === "de" ? "Neuen Eigentümer hinzufügen" : "Add New Owner"}
                </DialogTitle>
                <DialogDescription>
                  {language === "de"
                    ? "Geben Sie die Details des neuen Eigentümers ein."
                    : "Enter the details of the new owner."}
                </DialogDescription>
              </DialogHeader><div className="grid gap-4 py-4">
                {addErrors.general && (
                  <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                    {addErrors.general}
                  </div>
                )}
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">
                      {language === "de" ? "Vorname" : "First Name"}
                    </Label>
                    <Input
                      id="firstName"
                      value={newOwner.firstName}
                      onChange={(e) => setNewOwner({...newOwner, firstName: e.target.value})}
                      className={addErrors.firstName ? "border-red-500" : ""}
                    />
                    {addErrors.firstName && (
                      <p className="text-sm text-red-600">{addErrors.firstName}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName">
                      {language === "de" ? "Nachname" : "Last Name"}
                    </Label>
                    <Input
                      id="lastName"
                      value={newOwner.lastName}
                      onChange={(e) => setNewOwner({...newOwner, lastName: e.target.value})}
                      className={addErrors.lastName ? "border-red-500" : ""}
                    />
                    {addErrors.lastName && (
                      <p className="text-sm text-red-600">{addErrors.lastName}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">
                    {language === "de" ? "E-Mail" : "Email"}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={newOwner.email}
                    onChange={(e) => setNewOwner({...newOwner, email: e.target.value})}
                    className={addErrors.email ? "border-red-500" : ""}
                  />
                  {addErrors.email && (
                    <p className="text-sm text-red-600">{addErrors.email}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">
                    {language === "de" ? "Telefon" : "Phone"}
                  </Label>
                  <Input
                    id="phone"
                    value={newOwner.phone}
                    onChange={(e) => setNewOwner({...newOwner, phone: e.target.value})}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">
                    {language === "de" ? "Adresse" : "Address"}
                  </Label>
                  <Input
                    id="address"
                    value={newOwner.address}
                    onChange={(e) => setNewOwner({...newOwner, address: e.target.value})}
                  />
                </div>
              </div>              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsAddDialogOpen(false);
                  setAddErrors({});
                  setNewOwner({
                    firstName: "",
                    lastName: "",
                    email: "",
                    phone: "",
                    address: "",
                  });
                }}>
                  {language === "de" ? "Abbrechen" : "Cancel"}
                </Button>
                <Button onClick={handleAddOwner} disabled={isAdding}>
                  {isAdding ? (
                    <>
                      <div className="animate-spin mr-2 h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                      {language === "de" ? "Hinzufügen..." : "Adding..."}
                    </>
                  ) : (
                    language === "de" ? "Hinzufügen" : "Add"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>      {/* Bearbeitungsdialog */}      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        if (open) {
          setIsEditDialogOpen(true);
        } else {
          setIsEditDialogOpen(false);
          setEditErrors({});
          setCurrentOwner(null);
        }
      }}>
        <DialogContent className="sm:max-w-[500px] bg-white dark:bg-gray-900">
          <DialogHeader>
            <DialogTitle>
              {language === "de" ? "Eigentümer bearbeiten" : "Edit Owner"}
            </DialogTitle>
            <DialogDescription>
              {language === "de"
                ? "Bearbeiten Sie die Details des Eigentümers."
                : "Edit the details of the owner."}
            </DialogDescription>
          </DialogHeader>          {currentOwner && (
            <div className="grid gap-4 py-4">
              {editErrors.general && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {editErrors.general}
                </div>
              )}
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-firstName">
                    {language === "de" ? "Vorname" : "First Name"}
                  </Label>
                  <Input
                    id="edit-firstName"
                    value={currentOwner.firstName}
                    onChange={(e) => setCurrentOwner({...currentOwner, firstName: e.target.value})}
                    className={editErrors.firstName ? "border-red-500" : ""}
                  />
                  {editErrors.firstName && (
                    <p className="text-sm text-red-600">{editErrors.firstName}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-lastName">
                    {language === "de" ? "Nachname" : "Last Name"}
                  </Label>
                  <Input
                    id="edit-lastName"
                    value={currentOwner.lastName}
                    onChange={(e) => setCurrentOwner({...currentOwner, lastName: e.target.value})}
                    className={editErrors.lastName ? "border-red-500" : ""}
                  />
                  {editErrors.lastName && (
                    <p className="text-sm text-red-600">{editErrors.lastName}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-email">
                  {language === "de" ? "E-Mail" : "Email"}
                </Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={currentOwner.email}
                  onChange={(e) => setCurrentOwner({...currentOwner, email: e.target.value})}
                  className={editErrors.email ? "border-red-500" : ""}
                />
                {editErrors.email && (
                  <p className="text-sm text-red-600">{editErrors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-phone">
                  {language === "de" ? "Telefon" : "Phone"}
                </Label>
                <Input
                  id="edit-phone"
                  value={currentOwner.phone}
                  onChange={(e) => setCurrentOwner({...currentOwner, phone: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-address">
                  {language === "de" ? "Adresse" : "Address"}
                </Label>
                <Input
                  id="edit-address"
                  value={currentOwner.address}
                  onChange={(e) => setCurrentOwner({...currentOwner, address: e.target.value})}
                />
              </div>
            </div>
          )}          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsEditDialogOpen(false);
              setEditErrors({});
              setCurrentOwner(null);
            }}>
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button onClick={handleEditOwner} disabled={isEditing}>
              {isEditing ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                  {language === "de" ? "Speichern..." : "Saving..."}
                </>
              ) : (
                language === "de" ? "Speichern" : "Save"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>      {/* Löschbestätigungsdialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => {
        if (open) {
          setIsDeleteDialogOpen(true);
        } else {
          setIsDeleteDialogOpen(false);
          setOwnerToDelete(null);
        }
      }}>
        <DialogContent className="sm:max-w-[400px] bg-white dark:bg-gray-900">
          <DialogHeader>
            <DialogTitle>
              {language === "de" ? "Eigentümer löschen" : "Delete Owner"}
            </DialogTitle>
            <DialogDescription>
              {language === "de"
                ? `Sind Sie sicher, dass Sie ${ownerToDelete?.firstName} ${ownerToDelete?.lastName} löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.`
                : `Are you sure you want to delete ${ownerToDelete?.firstName} ${ownerToDelete?.lastName}? This action cannot be undone.`}
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsDeleteDialogOpen(false);
              setOwnerToDelete(null);
            }}>
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button 
              variant="destructive" 
              onClick={async () => {
                if (ownerToDelete) {
                  await handleDeleteOwner(ownerToDelete.id);
                  setIsDeleteDialogOpen(false);
                  setOwnerToDelete(null);
                }
              }}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                  {language === "de" ? "Löschen..." : "Deleting..."}
                </>
              ) : (
                language === "de" ? "Löschen" : "Delete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Eigentümerliste */}
      {filteredOwners.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredOwners.map((owner) => (
            <Card
              key={owner.id}
              className="overflow-hidden cursor-pointer hover:border-primary transition-colors"
              onClick={() => onSelectOwner(owner)}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{owner.firstName} {owner.lastName}</CardTitle>
                    <CardDescription className="mt-1 flex items-center">
                      <Mail className="h-3.5 w-3.5 mr-1" />
                      {owner.email}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0"
                        onClick={(e) => e.stopPropagation()} // Verhindert, dass das Klicken auf das Menü die Karte auswählt
                      >
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>
                        {language === "de" ? "Aktionen" : "Actions"}
                      </DropdownMenuLabel>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        openEditDialog(owner);
                      }}>
                        <Edit className="h-4 w-4 mr-2" />
                        {language === "de" ? "Bearbeiten" : "Edit"}
                      </DropdownMenuItem>                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        setOwnerToDelete(owner);
                        setIsDeleteDialogOpen(true);
                      }}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        {language === "de" ? "Löschen" : "Delete"}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        onSelectOwner(owner);
                      }}>
                        <Building className="h-4 w-4 mr-2" />
                        {language === "de" ? "Einheiten verwalten" : "Manage Units"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex items-start">
                    <Phone className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                    <span>{owner.phone || (language === "de" ? "Keine Telefonnummer" : "No phone number")}</span>
                  </div>
                  <div className="flex items-start">
                    <Building className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                    <span>{owner.address || (language === "de" ? "Keine Adresse" : "No address")}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-muted/50 p-3">
                <div className="w-full">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">
                      {language === "de" ? "Objekte" : "Properties"}
                    </span>
                    <Badge variant="outline">
                      {getPropertyCount(owner)}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    {getPropertyNames(owner).map((name, index) => (
                      <div key={index} className="text-xs text-muted-foreground truncate">
                        {name}
                      </div>
                    ))}
                    {owner.units.length === 0 && (
                      <div className="text-xs text-muted-foreground">
                        {language === "de" ? "Keine Objekte zugewiesen" : "No properties assigned"}
                      </div>
                    )}
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-10">
          <Users className="h-10 w-10 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">
            {language === "de" ? "Keine Eigentümer gefunden" : "No owners found"}
          </h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {language === "de"
              ? "Beginnen Sie damit, einen neuen Eigentümer hinzuzufügen."
              : "Start by adding a new owner."}
          </p>
        </div>
      )}
    </div>
  );
}
