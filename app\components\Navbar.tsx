"use client";

import Image from "next/image";
import Link from "next/link";
import Logo from "@/public/logo.png";
import { RainbowButton } from "@/components/ui/rainbow-button";
import { LanguageSwitcher } from './LanguageSwitcher';
import { useLanguage } from '@/app/contexts/LanguageContext';

export function Navbar() {
  const { t } = useLanguage();
  
  return (
    <div className="flex items-center justify-between py-5">
      <div className="flex items-center gap-6">
        <Link href="/" className="flex items-center gap-2">
          <Image src={Logo} alt="Logo" className="size-10" />
          <h3 className="text-3xl font-semibold">
            Tax<span className="text-blue-500">Mate</span>
          </h3>
        </Link>
        <div className="hidden md:flex items-center gap-4">
          <Link href="/weg-accounting" className="text-sm font-medium text-muted-foreground hover:text-foreground">
            {t('wegAccounting')}
          </Link>
        </div>
      </div>
      <div className="flex items-center gap-4">
        {/* <LanguageSwitcher /> */}
        <Link href="/login">
          <RainbowButton>{t('getStarted')}</RainbowButton>
        </Link>
      </div>
    </div>
  );
}
