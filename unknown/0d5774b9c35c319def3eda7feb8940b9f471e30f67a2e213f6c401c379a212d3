"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Info, Plus, Trash2, AlertTriangle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

type ExtraordinaryBurden = {
  id: string;
  category: string;
  description: string;
  amount: string;
  date: string;
  hasReceipt: boolean;
};

export function ExtraordinaryBurdensStep() {
  const { language } = useLanguage();

  // Beispieldaten für außergewöhnliche Belastungen
  const [extraordinaryBurdens, setExtraordinaryBurdens] = useState<ExtraordinaryBurden[]>([
    {
      id: "1",
      category: "medicalExpenses",
      description: "Zahnbehandlung",
      amount: "1200",
      date: "2023-08-15",
      hasReceipt: true,
    },
  ]);

  const [medicalInfo, setMedicalInfo] = useState({
    hasInsurance: true,
    insuranceReimbursement: "400",
  });

  const [disabilityInfo, setDisabilityInfo] = useState({
    hasDisability: false,
    disabilityPercentage: "",
    dietRequired: false,
    needsCare: false,
  });

  const [childcareInfo, setChildcareInfo] = useState({
    hasChildcare: true,
    childcareExpenses: "2400",
    numberOfChildren: "2",
  });

  const handleExtraordinaryBurdenChange = (id: string, field: keyof ExtraordinaryBurden, value: string | boolean) => {
    setExtraordinaryBurdens(
      extraordinaryBurdens.map((burden) =>
        burden.id === id ? { ...burden, [field]: value } : burden
      )
    );
  };

  const handleMedicalInfoChange = (field: string, value: string | boolean) => {
    setMedicalInfo({
      ...medicalInfo,
      [field]: value,
    });
  };

  const handleDisabilityInfoChange = (field: string, value: string | boolean) => {
    setDisabilityInfo({
      ...disabilityInfo,
      [field]: value,
    });
  };

  const handleChildcareInfoChange = (field: string, value: string | boolean) => {
    setChildcareInfo({
      ...childcareInfo,
      [field]: value,
    });
  };

  const addExtraordinaryBurden = () => {
    const newId = (extraordinaryBurdens.length + 1).toString();
    setExtraordinaryBurdens([
      ...extraordinaryBurdens,
      {
        id: newId,
        category: "",
        description: "",
        amount: "",
        date: "",
        hasReceipt: false,
      },
    ]);
  };

  const removeExtraordinaryBurden = (id: string) => {
    setExtraordinaryBurdens(extraordinaryBurdens.filter((burden) => burden.id !== id));
  };

  // Berechne den Selbstbehalt für außergewöhnliche Belastungen
  const calculateDeductible = (income: number) => {
    // Vereinfachte Berechnung des Selbstbehalts
    // In einer echten Anwendung wäre dies komplexer und würde Familienstatus berücksichtigen
    if (income <= 7300) {
      return income * 0.06; // 6%
    } else if (income <= 14600) {
      return income * 0.08; // 8%
    } else if (income <= 36400) {
      return income * 0.1; // 10%
    } else {
      return income * 0.12; // 12%
    }
  };

  // Berechne die Summe der außergewöhnlichen Belastungen
  const calculateTotalBurdens = () => {
    const totalExpenses = extraordinaryBurdens.reduce((total, burden) => {
      return total + (burden.amount ? parseFloat(burden.amount) : 0);
    }, 0);

    // Abzüglich Versicherungserstattung für medizinische Kosten
    const insuranceReimbursement = medicalInfo.hasInsurance && medicalInfo.insuranceReimbursement
      ? parseFloat(medicalInfo.insuranceReimbursement)
      : 0;

    // Kinderbetreuungskosten
    const childcareExpenses = childcareInfo.hasChildcare && childcareInfo.childcareExpenses
      ? parseFloat(childcareInfo.childcareExpenses)
      : 0;

    return totalExpenses - insuranceReimbursement + childcareExpenses;
  };

  // Angenommenes Einkommen für die Berechnung des Selbstbehalts
  const assumedIncome = 35000;
  const deductible = calculateDeductible(assumedIncome);
  const totalBurdens = calculateTotalBurdens();
  const taxBenefit = totalBurdens > deductible ? (totalBurdens - deductible) * 0.35 : 0;

  return (
    <div className="space-y-6">
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">
            {language === "de" ? "Allgemein" : "General"}
          </TabsTrigger>
          <TabsTrigger value="medical">
            {language === "de" ? "Krankheitskosten" : "Medical Expenses"}
          </TabsTrigger>
          <TabsTrigger value="childcare">
            {language === "de" ? "Kinderbetreuung" : "Childcare"}
          </TabsTrigger>
        </TabsList>

        {/* Allgemeine Tab */}
        <TabsContent value="general" className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Außergewöhnliche Belastungen" : "Extraordinary Burdens"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Gib deine außergewöhnlichen Belastungen an, die deine wirtschaftliche Leistungsfähigkeit wesentlich beeinträchtigen."
                : "Enter your extraordinary burdens that significantly impair your economic capacity."}
            </p>

            <div className="p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-amber-800 dark:text-amber-300">
                    {language === "de" ? "Wichtiger Hinweis zum Selbstbehalt" : "Important Note on Deductible"}
                  </h4>
                  <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                    {language === "de"
                      ? `Außergewöhnliche Belastungen werden nur berücksichtigt, wenn sie den Selbstbehalt (ca. ${Math.round(deductible)}€ bei deinem Einkommen) übersteigen.`
                      : `Extraordinary burdens are only considered if they exceed the deductible (approx. €${Math.round(deductible)} at your income).`}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {extraordinaryBurdens.map((burden) => (
            <Card key={burden.id} className="border-gray-200 dark:border-gray-800">
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`category-${burden.id}`}>
                      {language === "de" ? "Kategorie" : "Category"}
                    </Label>
                    <Select
                      value={burden.category}
                      onValueChange={(value) => handleExtraordinaryBurdenChange(burden.id, "category", value)}
                    >
                      <SelectTrigger id={`category-${burden.id}`}>
                        <SelectValue placeholder={language === "de" ? "Kategorie auswählen" : "Select category"} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="medicalExpenses">
                          {language === "de" ? "Krankheitskosten" : "Medical Expenses"}
                        </SelectItem>
                        <SelectItem value="disabilityExpenses">
                          {language === "de" ? "Behinderungskosten" : "Disability Expenses"}
                        </SelectItem>
                        <SelectItem value="childcareExpenses">
                          {language === "de" ? "Kinderbetreuungskosten" : "Childcare Expenses"}
                        </SelectItem>
                        <SelectItem value="funeralExpenses">
                          {language === "de" ? "Begräbniskosten" : "Funeral Expenses"}
                        </SelectItem>
                        <SelectItem value="disasterDamage">
                          {language === "de" ? "Katastrophenschäden" : "Disaster Damage"}
                        </SelectItem>
                        <SelectItem value="other">
                          {language === "de" ? "Sonstiges" : "Other"}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`description-${burden.id}`}>
                      {language === "de" ? "Beschreibung" : "Description"}
                    </Label>
                    <Input
                      id={`description-${burden.id}`}
                      value={burden.description}
                      onChange={(e) => handleExtraordinaryBurdenChange(burden.id, "description", e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`amount-${burden.id}`}>
                      {language === "de" ? "Betrag (€)" : "Amount (€)"}
                    </Label>
                    <Input
                      id={`amount-${burden.id}`}
                      value={burden.amount}
                      onChange={(e) => handleExtraordinaryBurdenChange(burden.id, "amount", e.target.value)}
                      type="number"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`date-${burden.id}`}>
                      {language === "de" ? "Datum" : "Date"}
                    </Label>
                    <Input
                      id={`date-${burden.id}`}
                      value={burden.date}
                      onChange={(e) => handleExtraordinaryBurdenChange(burden.id, "date", e.target.value)}
                      type="date"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`hasReceipt-${burden.id}`}
                      checked={burden.hasReceipt}
                      onCheckedChange={(checked) =>
                        handleExtraordinaryBurdenChange(burden.id, "hasReceipt", checked as boolean)
                      }
                    />
                    <Label htmlFor={`hasReceipt-${burden.id}`} className="cursor-pointer">
                      {language === "de" ? "Beleg vorhanden" : "Receipt Available"}
                    </Label>
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeExtraordinaryBurden(burden.id)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    {language === "de" ? "Entfernen" : "Remove"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}

          <Button
            variant="outline"
            onClick={addExtraordinaryBurden}
            className="mt-2"
          >
            <Plus className="h-4 w-4 mr-2" />
            {language === "de" ? "Weitere außergewöhnliche Belastung hinzufügen" : "Add More Extraordinary Burden"}
          </Button>
        </TabsContent>

        {/* Krankheitskosten Tab */}
        <TabsContent value="medical" className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Krankheitskosten" : "Medical Expenses"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Gib deine Krankheitskosten an, die nicht von der Versicherung übernommen wurden."
                : "Enter your medical expenses that were not covered by insurance."}
            </p>
          </div>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasInsurance"
                    checked={medicalInfo.hasInsurance}
                    onCheckedChange={(checked) =>
                      handleMedicalInfoChange("hasInsurance", checked as boolean)
                    }
                  />
                  <Label htmlFor="hasInsurance" className="cursor-pointer">
                    {language === "de" ? "Versicherungserstattung erhalten" : "Received Insurance Reimbursement"}
                  </Label>
                </div>

                {medicalInfo.hasInsurance && (
                  <div className="space-y-2">
                    <Label htmlFor="insuranceReimbursement">
                      {language === "de" ? "Höhe der Erstattung (€)" : "Reimbursement Amount (€)"}
                    </Label>
                    <Input
                      id="insuranceReimbursement"
                      value={medicalInfo.insuranceReimbursement}
                      onChange={(e) => handleMedicalInfoChange("insuranceReimbursement", e.target.value)}
                      type="number"
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <h4 className="font-medium">
                    {language === "de" ? "Behinderung" : "Disability"}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hasDisability"
                      checked={disabilityInfo.hasDisability}
                      onCheckedChange={(checked) =>
                        handleDisabilityInfoChange("hasDisability", checked as boolean)
                      }
                    />
                    <Label htmlFor="hasDisability" className="cursor-pointer">
                      {language === "de" ? "Ich habe eine Behinderung" : "I Have a Disability"}
                    </Label>
                  </div>
                </div>

                {disabilityInfo.hasDisability && (
                  <div className="space-y-4 pl-6">
                    <div className="space-y-2">
                      <Label htmlFor="disabilityPercentage">
                        {language === "de" ? "Grad der Behinderung (%)" : "Degree of Disability (%)"}
                      </Label>
                      <Input
                        id="disabilityPercentage"
                        value={disabilityInfo.disabilityPercentage}
                        onChange={(e) => handleDisabilityInfoChange("disabilityPercentage", e.target.value)}
                        type="number"
                        min="0"
                        max="100"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="dietRequired"
                        checked={disabilityInfo.dietRequired}
                        onCheckedChange={(checked) =>
                          handleDisabilityInfoChange("dietRequired", checked as boolean)
                        }
                      />
                      <Label htmlFor="dietRequired" className="cursor-pointer">
                        {language === "de" ? "Diätverpflegung erforderlich" : "Diet Required"}
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="needsCare"
                        checked={disabilityInfo.needsCare}
                        onCheckedChange={(checked) =>
                          handleDisabilityInfoChange("needsCare", checked as boolean)
                        }
                      />
                      <Label htmlFor="needsCare" className="cursor-pointer">
                        {language === "de" ? "Pflegebedürftig" : "Needs Care"}
                      </Label>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Kinderbetreuung Tab */}
        <TabsContent value="childcare" className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Kinderbetreuungskosten" : "Childcare Expenses"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Gib deine Kosten für die Betreuung von Kindern unter 10 Jahren an."
                : "Enter your expenses for the care of children under 10 years of age."}
            </p>
          </div>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasChildcare"
                    checked={childcareInfo.hasChildcare}
                    onCheckedChange={(checked) =>
                      handleChildcareInfoChange("hasChildcare", checked as boolean)
                    }
                  />
                  <Label htmlFor="hasChildcare" className="cursor-pointer">
                    {language === "de" ? "Kinderbetreuungskosten geltend machen" : "Claim Childcare Expenses"}
                  </Label>
                </div>

                {childcareInfo.hasChildcare && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="numberOfChildren">
                        {language === "de" ? "Anzahl der Kinder unter 10 Jahren" : "Number of Children Under 10"}
                      </Label>
                      <Select
                        value={childcareInfo.numberOfChildren}
                        onValueChange={(value) => handleChildcareInfoChange("numberOfChildren", value)}
                      >
                        <SelectTrigger id="numberOfChildren">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1</SelectItem>
                          <SelectItem value="2">2</SelectItem>
                          <SelectItem value="3">3</SelectItem>
                          <SelectItem value="4">4</SelectItem>
                          <SelectItem value="5+">5+</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="childcareExpenses">
                        {language === "de" ? "Gesamte Kinderbetreuungskosten (€)" : "Total Childcare Expenses (€)"}
                      </Label>
                      <Input
                        id="childcareExpenses"
                        value={childcareInfo.childcareExpenses}
                        onChange={(e) => handleChildcareInfoChange("childcareExpenses", e.target.value)}
                        type="number"
                      />
                      <p className="text-xs text-muted-foreground">
                        {language === "de"
                          ? "Maximal 2.300€ pro Kind und Jahr absetzbar."
                          : "Maximum €2,300 per child per year deductible."}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Zusammenfassung */}
      <Card className="border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/20">
        <CardContent className="pt-6">
          <h4 className="font-medium text-green-700 dark:text-green-300 mb-2">
            {language === "de" ? "Zusammenfassung der außergewöhnlichen Belastungen" : "Summary of Extraordinary Burdens"}
          </h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>{language === "de" ? "Gesamte außergewöhnliche Belastungen" : "Total Extraordinary Burdens"}</span>
              <span className="font-medium">€{totalBurdens.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>{language === "de" ? "Selbstbehalt" : "Deductible"}</span>
              <span className="font-medium">€{deductible.toFixed(2)}</span>
            </div>
            <div className="border-t pt-2 mt-2 flex justify-between font-bold">
              <span>{language === "de" ? "Steuerliche Auswirkung (geschätzt)" : "Tax Impact (Estimated)"}</span>
              <span className={taxBenefit > 0 ? "text-green-600 dark:text-green-400" : ""}>
                €{taxBenefit.toFixed(2)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* FinanzOnline Hinweis */}
      <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-amber-800 dark:text-amber-300">
              {language === "de" ? "Wo finde ich das in FinanzOnline?" : "Where do I find this in FinanzOnline?"}
            </h4>
            <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
              {language === "de"
                ? "Außergewöhnliche Belastungen werden in FinanzOnline unter 'Außergewöhnliche Belastungen' eingetragen. Krankheitskosten findest du unter 'Krankheitskosten', Kinderbetreuungskosten unter 'Kinderbetreuungskosten'. Behinderungsbedingte Ausgaben werden unter 'Behinderung' eingetragen."
                : "Extraordinary burdens are entered in FinanzOnline under 'Extraordinary Burdens'. Medical expenses can be found under 'Medical Expenses', childcare expenses under 'Childcare Expenses'. Disability-related expenses are entered under 'Disability'."}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
