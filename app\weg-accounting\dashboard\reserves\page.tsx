"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/app/utils/clientHooks";
import { ReserveTab } from "@/app/components/weg-accounting/reserves/ReserveTab";
import { LoadingState } from "@/app/components/LoadingState";
import { Property } from "@/app/lib/wegTypes";

export default function ReservesPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [properties, setProperties] = useState<Property[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Only load data if the user is authenticated
    if (isAuthenticated) {
      const loadData = async () => {
        try {
          setIsLoading(true);
          // Fetch properties from API
          const propertiesResponse = await fetch('/api/weg-accounting/properties');
          if (!propertiesResponse.ok) {
            throw new Error(`Failed to fetch properties: ${propertiesResponse.statusText}`);
          }
          const propertiesData = await propertiesResponse.json();
          setProperties(propertiesData);
        } catch (error) {
          console.error("Failed to load data:", error);
        } finally {
          setIsLoading(false);
        }
      };

      loadData();
    }
  }, [isAuthenticated]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Rücklagen</h1>
        <p className="text-muted-foreground">
          Verwalten Sie Instandhaltungsrücklagen und Sonderzahlungen
        </p>
      </div>

      {authLoading || isLoading ? (
        <LoadingState
          title="Rücklagendaten werden geladen..."
          description="Bitte warten Sie, während die Daten geladen werden."
        />
      ) : (
        <ReserveTab properties={properties} />
      )}
    </div>
  );
}
