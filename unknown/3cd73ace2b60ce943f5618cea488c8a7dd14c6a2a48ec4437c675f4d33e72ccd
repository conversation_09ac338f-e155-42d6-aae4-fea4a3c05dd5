"use client";

import { Card } from "@/components/ui/card";
import { LightbulbIcon, XIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Button } from "@/components/ui/button";

interface TaxTip {
  id: number;
  textDE: string;
  textEN: string;
}

const taxTips: TaxTip[] = [
  {
    id: 1,
    textDE: "Wusstest du schon? Du kannst bis zu 730€ jährlich für Fortbildungen absetzen, die mit deinem aktuellen Beruf zusammenhängen.",
    textEN: "Did you know? You can deduct up to €730 annually for further education related to your current profession.",
  },
  {
    id: 2,
    textDE: "Tipp: Homeoffice-Pauschale nicht vergessen! Für jeden Homeoffice-Tag kannst du 3€ absetzen (max. 300€ jährlich).",
    textEN: "Tip: Don't forget the home office allowance! You can deduct €3 for each home office day (max. €300 annually).",
  },
  {
    id: 3,
    textDE: "Wusstest du schon? Pendlerpauschale und Pendlereuro können auch bei Teilzeitarbeit geltend gemacht werden.",
    textEN: "Did you know? Commuter allowance and commuter euro can also be claimed for part-time work.",
  },
  {
    id: 4,
    textDE: "Tipp: Spenden an begünstigte Organisationen sind bis zu 10% deines Jahreseinkommens absetzbar.",
    textEN: "Tip: Donations to eligible organizations are deductible up to 10% of your annual income.",
  },
  {
    id: 5,
    textDE: "Wusstest du schon? Der Familienbonus Plus bringt bis zu 2.000€ pro Kind und Jahr als direkte Steuerersparnis.",
    textEN: "Did you know? The Family Bonus Plus provides up to €2,000 per child per year as a direct tax saving.",
  },
];

export function TaxTipCard() {
  const { language } = useLanguage();
  const [currentTip, setCurrentTip] = useState<TaxTip | null>(null);
  const [isVisible, setIsVisible] = useState(true);
  const [fadeClass, setFadeClass] = useState("opacity-100 translate-y-0");

  // Wähle einen zufälligen Tipp beim ersten Laden
  useEffect(() => {
    const randomTip = taxTips[Math.floor(Math.random() * taxTips.length)];
    setCurrentTip(randomTip);
  }, []);

  // Wechsle den Tipp alle 30 Sekunden
  useEffect(() => {
    const interval = setInterval(() => {
      if (isVisible) {
        // Ausblenden
        setFadeClass("opacity-0 translate-y-4");

        // Warte auf die Ausblend-Animation, bevor der Tipp gewechselt wird
        setTimeout(() => {
          const currentIndex = currentTip ? taxTips.findIndex(tip => tip.id === currentTip.id) : -1;
          const nextIndex = (currentIndex + 1) % taxTips.length;
          setCurrentTip(taxTips[nextIndex]);

          // Wieder einblenden
          setFadeClass("opacity-100 translate-y-0");
        }, 300);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [currentTip, isVisible]);

  if (!currentTip || !isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div
        className={`transition-all duration-300 ease-in-out ${fadeClass}`}
      >
        <Card className="w-80 p-4 shadow-lg border-l-4 border-l-amber-500 bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20">
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-2 text-amber-600 dark:text-amber-400 mb-2">
              <LightbulbIcon className="h-5 w-5" />
              <h3 className="font-medium">
                {language === "de" ? "Steuertipp" : "Tax Tip"}
              </h3>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 rounded-full -mt-1 -mr-1 text-amber-600 dark:text-amber-400 hover:bg-amber-200 dark:hover:bg-amber-800/30"
              onClick={() => setIsVisible(false)}
            >
              <XIcon className="h-3 w-3" />
              <span className="sr-only">Schließen</span>
            </Button>
          </div>
          <p className="text-sm text-amber-800 dark:text-amber-200">
            {language === "de" ? currentTip.textDE : currentTip.textEN}
          </p>
        </Card>
      </div>
    </div>
  );
}
