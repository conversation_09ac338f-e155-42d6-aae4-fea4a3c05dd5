"use client";

import { useState } from "react";
import { PropertyList, Property } from "./PropertyList";
import { PropertyDetail } from "./PropertyDetail";

interface PropertiesTabProps {
  properties?: Property[];
  owners?: any[];
  initialProperties?: Property[];
  onUpdateProperties?: (properties: Property[]) => void;
}

export function PropertiesTab({ properties: propProperties, owners = [], initialProperties, onUpdateProperties }: PropertiesTabProps = {}) {
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [localProperties, setLocalProperties] = useState<Property[]>(initialProperties || []);

  // Verwende entweder die übergebenen Properties oder die lokalen Properties
  const properties = propProperties || localProperties;

  // Funktion zum Aktualisieren der Properties
  const updateProperties = (newProperties: Property[]) => {
    if (onUpdateProperties) {
      onUpdateProperties(newProperties);
    } else {
      setLocalProperties(newProperties);
    }
  };

  // Handler für die Auswahl eines Objekts
  const handleSelectProperty = (property: Property) => {
    setSelectedProperty(property);
  };

  // Handler für die Aktualisierung eines Objekts
  const handleUpdateProperty = (updatedProperty: Property) => {
    const newProperties = properties.map(property =>
      property.id === updatedProperty.id ? updatedProperty : property
    );

    updateProperties(newProperties);

    // Wenn das aktuell ausgewählte Objekt aktualisiert wurde, aktualisiere auch dieses
    if (selectedProperty && selectedProperty.id === updatedProperty.id) {
      setSelectedProperty(updatedProperty);
    }
  };

  return (
    <div>
      {selectedProperty ? (
        <PropertyDetail
          property={selectedProperty}
          owners={owners}
          onBack={() => setSelectedProperty(null)}
          onUpdate={handleUpdateProperty}
        />
      ) : (
        <PropertyList
          properties={properties}
          onSelectProperty={handleSelectProperty}
          onUpdateProperties={updateProperties}
        />
      )}
    </div>
  );
}
