"use client";

import { useState, useEffect } from "react";
import { InvoiceList, Invoice } from "./InvoiceList";
import { InvoiceDetail } from "./InvoiceDetail";
import { Property } from "../properties/PropertyList";
import { ExpenseCategory } from "../accounting/AccountingList";
import { LoadingState } from "@/app/components/LoadingState";

interface InvoiceTabProps {
  properties: Property[];
  expenseCategories: ExpenseCategory[];
}

export function InvoiceTab({ properties, expenseCategories }: InvoiceTabProps) {
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Lade Rechnungen von der API
  useEffect(() => {
    const loadInvoices = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await fetch('/api/weg-accounting/invoices');
        if (!response.ok) {
          throw new Error(`Failed to fetch invoices: ${response.statusText}`);
        }
        
        const invoicesData = await response.json();
        setInvoices(invoicesData);
      } catch (error) {
        console.error("Failed to load invoices:", error);
        setError("Fehler beim Laden der Rechnungen. Bitte versuchen Sie es erneut.");
      } finally {
        setIsLoading(false);
      }
    };

    loadInvoices();
  }, []);
  
  // Handler für die Auswahl einer Rechnung
  const handleSelectInvoice = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
  };
  
  // Handler für die Aktualisierung einer Rechnung
  const handleUpdateInvoice = async (updatedInvoice: Invoice) => {
    try {
      const response = await fetch(`/api/weg-accounting/invoices/${updatedInvoice.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedInvoice),
      });

      if (!response.ok) {
        throw new Error('Failed to update invoice');
      }

      const updated = await response.json();
      
      setInvoices(invoices.map(invoice => 
        invoice.id === updated.id ? updated : invoice
      ));
      
      // Wenn die aktuell ausgewählte Rechnung aktualisiert wurde, aktualisiere auch diese
      if (selectedInvoice && selectedInvoice.id === updated.id) {
        setSelectedInvoice(updated);
      }
    } catch (error) {
      console.error("Failed to update invoice:", error);
      setError("Fehler beim Aktualisieren der Rechnung.");
    }
  };
  
  // Handler für das Löschen einer Rechnung
  const handleDeleteInvoice = async (id: string) => {
    try {
      const response = await fetch(`/api/weg-accounting/invoices/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete invoice');
      }

      setInvoices(invoices.filter(invoice => invoice.id !== id));
      
      // Wenn die aktuell ausgewählte Rechnung gelöscht wurde, gehe zurück zur Liste
      if (selectedInvoice && selectedInvoice.id === id) {
        setSelectedInvoice(null);
      }
    } catch (error) {
      console.error("Failed to delete invoice:", error);
      setError("Fehler beim Löschen der Rechnung.");
    }
  };

  // Handler für das Hinzufügen einer neuen Rechnung
  const handleAddInvoice = async (newInvoices: Invoice[]) => {
    // Reload invoices from API to get the latest data
    try {
      const response = await fetch('/api/weg-accounting/invoices');
      if (response.ok) {
        const invoicesData = await response.json();
        setInvoices(invoicesData);
      }
    } catch (error) {
      console.error("Failed to reload invoices:", error);
    }
  };

  if (isLoading) {
    return (
      <LoadingState
        title="Rechnungen werden geladen..."
        description="Bitte warten Sie, während die Rechnungsdaten geladen werden."
      />
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="text-red-500 mb-4">{error}</div>
        <button 
          onClick={() => window.location.reload()} 
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Seite neu laden
        </button>
      </div>
    );
  }
  
  return (
    <div>
      {selectedInvoice ? (
        <InvoiceDetail 
          invoice={selectedInvoice} 
          properties={properties}
          expenseCategories={expenseCategories}
          onBack={() => setSelectedInvoice(null)} 
          onUpdate={handleUpdateInvoice}
          onDelete={handleDeleteInvoice}
        />
      ) : (
        <InvoiceList 
          invoices={invoices}
          properties={properties}
          expenseCategories={expenseCategories}
          onSelectInvoice={handleSelectInvoice}
          onUpdateInvoices={handleAddInvoice}
        />
      )}
    </div>
  );
}
