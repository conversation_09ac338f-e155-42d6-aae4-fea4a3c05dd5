"use client";

import { useState, useEffect, useRef } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Mi<PERSON>, MicOff, X, Volume2, Loader2 } from "lucide-react";

export function VoiceAssistant() {
  const { language } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [response, setResponse] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [showResponse, setShowResponse] = useState(false);
  const micButtonRef = useRef<HTMLButtonElement>(null);

  // Simuliere eine Spracherkennung
  const toggleListening = () => {
    if (isListening) {
      setIsListening(false);
      if (transcript) {
        processVoiceCommand(transcript);
      }
    } else {
      setIsListening(true);
      setTranscript("");
      setResponse("");
      setShowResponse(false);

      // Simuliere Spracherkennung mit zufälligen Wörtern
      const simulateVoiceRecognition = () => {
        const germanPhrases = [
          "Zeige mir die Rechnung vom 31. Mai",
          "Exportiere die Rechnung mit dem Titel Webdesign",
          "Wie viele unbezahlte Rechnungen habe ich?",
          "Erstelle eine neue Rechnung für Kunde Schmidt",
          "Wie hoch war mein Umsatz im letzten Monat?"
        ];

        const englishPhrases = [
          "Show me the invoice from May 31st",
          "Export the invoice with the title web design",
          "How many unpaid invoices do I have?",
          "Create a new invoice for client Smith",
          "What was my revenue last month?"
        ];

        const phrases = language === "de" ? germanPhrases : englishPhrases;
        const randomPhrase = phrases[Math.floor(Math.random() * phrases.length)];

        let currentText = "";
        const words = randomPhrase.split(" ");

        const wordInterval = setInterval(() => {
          if (words.length > 0 && isListening) {
            currentText += words.shift() + " ";
            setTranscript(currentText.trim());
          } else {
            clearInterval(wordInterval);
            if (isListening) {
              setTimeout(() => {
                setIsListening(false);
                processVoiceCommand(currentText.trim());
              }, 500);
            }
          }
        }, 300);
      };

      simulateVoiceRecognition();
    }
  };

  // Simuliere die Verarbeitung eines Sprachbefehls
  const processVoiceCommand = (command: string) => {
    setIsProcessing(true);

    // Simuliere eine Verarbeitungszeit
    setTimeout(() => {
      let responseText = "";

      if (language === "de") {
        if (command.includes("Rechnung") && command.includes("zeige")) {
          responseText = "Ich zeige dir die angeforderte Rechnung. Einen Moment bitte...";
        } else if (command.includes("Rechnung") && command.includes("exportiere")) {
          responseText = "Ich exportiere die Rechnung für dich. Die Datei wird vorbereitet...";
        } else if (command.includes("unbezahlte")) {
          responseText = "Du hast aktuell 3 unbezahlte Rechnungen mit einem Gesamtwert von 2.450€.";
        } else if (command.includes("erstelle") && command.includes("Rechnung")) {
          responseText = "Ich erstelle eine neue Rechnung. Bitte gib weitere Details an...";
        } else if (command.includes("Umsatz")) {
          responseText = "Dein Umsatz im letzten Monat betrug 8.750€, das sind 12% mehr als im Vormonat.";
        } else {
          responseText = "Entschuldigung, ich habe das nicht verstanden. Kannst du es anders formulieren?";
        }
      } else {
        if (command.includes("invoice") && command.includes("show")) {
          responseText = "I'll show you the requested invoice. One moment please...";
        } else if (command.includes("invoice") && command.includes("export")) {
          responseText = "I'm exporting the invoice for you. The file is being prepared...";
        } else if (command.includes("unpaid")) {
          responseText = "You currently have 3 unpaid invoices with a total value of €2,450.";
        } else if (command.includes("create") && command.includes("invoice")) {
          responseText = "I'm creating a new invoice. Please provide more details...";
        } else if (command.includes("revenue")) {
          responseText = "Your revenue last month was €8,750, which is 12% more than the previous month.";
        } else {
          responseText = "Sorry, I didn't understand that. Can you phrase it differently?";
        }
      }

      setResponse(responseText);
      setIsProcessing(false);
      setShowResponse(true);

      // Simuliere Text-to-Speech
      // In einer echten Anwendung würde hier die Web Speech API verwendet werden
    }, 1500);
  };

  // Schließe das Assistenten-Fenster nach einer Weile
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (showResponse) {
      timeout = setTimeout(() => {
        setIsOpen(false);
        setTranscript("");
        setResponse("");
        setShowResponse(false);
      }, 8000);
    }
    return () => clearTimeout(timeout);
  }, [showResponse]);

  return (
    <>
      {/* Schwebender Mic-Button */}
      <Button
        ref={micButtonRef}
        className={`fixed bottom-6 left-6 z-50 rounded-full w-12 h-12 shadow-lg ${
          isListening ? "bg-red-500 hover:bg-red-600" : "bg-blue-500 hover:bg-blue-600"
        }`}
        onClick={() => {
          if (!isOpen) {
            setIsOpen(true);
          }
          toggleListening();
        }}
      >
        {isListening ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
      </Button>

      {/* Assistenten-Fenster */}
      {isOpen && (
        <div className="fixed bottom-20 left-6 z-50 w-80">
          <Card className="p-4 shadow-lg border-t-4 border-t-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
            <div className="flex justify-between items-start mb-3">
              <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
                {isListening ? (
                  <div className="relative">
                    <Mic className="h-5 w-5" />
                    <span className="absolute top-0 right-0 flex h-2 w-2">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
                    </span>
                  </div>
                ) : isProcessing ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Volume2 className="h-5 w-5" />
                )}
                <h3 className="font-medium">
                  {language === "de" ? "Sprachassistent" : "Voice Assistant"}
                </h3>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-full -mt-1 -mr-1 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-800/30"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-3 w-3" />
                <span className="sr-only">
                  {language === "de" ? "Schließen" : "Close"}
                </span>
              </Button>
            </div>

            {/* Transkript */}
            {transcript && (
              <div className="mb-3 p-2 bg-white/50 dark:bg-white/10 rounded text-sm">
                <p className="text-gray-700 dark:text-gray-300 italic">&quot;{transcript}&quot;</p>
              </div>
            )}

            {/* Antwort */}
            {showResponse && (
              <div className="p-2 bg-blue-100/50 dark:bg-blue-900/20 rounded text-sm">
                <p className="text-blue-800 dark:text-blue-200">{response}</p>
              </div>
            )}

            {/* Hinweis */}
            {!transcript && !showResponse && (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {language === "de"
                  ? "Klicke auf das Mikrofon und sprich deine Anfrage..."
                  : "Click the microphone and speak your request..."}
              </p>
            )}
          </Card>
        </div>
      )}
    </>
  );
}
