"use client";

import { useState, useEffect } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Building, Mail, Phone, Plus, Trash2, User } from "lucide-react";
import { Owner, OwnerUnit } from "./OwnerList";
import { Property } from "../properties/PropertyList";

interface OwnerDetailProps {
  owner: Owner;
  properties: Property[];
  onBack: () => void;
  onUpdate: (owner: Owner) => void;
}

export function OwnerDetail({ owner, properties, onBack, onUpdate }: OwnerDetailProps) {
  const { language } = useLanguage();
  
  // State für das Hinzufügen einer neuen Einheit
  const [isAddUnitDialogOpen, setIsAddUnitDialogOpen] = useState(false);
  const [newUnit, setNewUnit] = useState<Omit<OwnerUnit, "id">>({
    propertyId: properties.length > 0 ? properties[0].id : "",
    unitNumber: "",
    area: 0,
    ownershipPercentage: 100,
  });
  
  // Handler für das Hinzufügen einer neuen Einheit
  const handleAddUnit = () => {
    const newId = (owner.units.length + 1).toString();
    const unitToAdd: OwnerUnit = {
      id: newId,
      ...newUnit,
    };
    
    const updatedOwner = {
      ...owner,
      units: [...owner.units, unitToAdd],
    };
    
    onUpdate(updatedOwner);
    setNewUnit({
      propertyId: properties.length > 0 ? properties[0].id : "",
      unitNumber: "",
      area: 0,
      ownershipPercentage: 100,
    });
    setIsAddUnitDialogOpen(false);
  };
  
  // Handler für das Löschen einer Einheit
  const handleDeleteUnit = (id: string) => {
    const updatedOwner = {
      ...owner,
      units: owner.units.filter(unit => unit.id !== id),
    };
    
    onUpdate(updatedOwner);
  };
  
  // Funktion zum Abrufen des Eigentumsanteils eines Eigentümers
  const getOwnershipTotal = () => {
    return owner.units.reduce((total, unit) => total + unit.ownershipPercentage, 0);
  };
  
  // Funktion zum Abrufen der Gesamtfläche eines Eigentümers
  const getTotalArea = () => {
    return owner.units.reduce((total, unit) => total + unit.area, 0);
  };
  
  // Funktion zum Abrufen des Objektnamens anhand der ID
  const getPropertyName = (propertyId: string) => {
    const property = properties.find(p => p.id === propertyId);
    return property ? property.name : "Unbekanntes Objekt";
  };
  
  const [statements, setStatements] = useState<any[]>([]);
  const [loadingStatements, setLoadingStatements] = useState(false);
  const [statementsError, setStatementsError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStatements() {
      setLoadingStatements(true);
      setStatementsError(null);
      try {
        // TODO: Passe die URL an, wenn ein echter API-Endpunkt existiert
        const res = await fetch(`/api/weg-accounting/owners/${owner.id}/statements`);
        if (!res.ok) throw new Error("Fehler beim Laden der Abrechnungen");
        const data = await res.json();
        setStatements(data);
      } catch (e: any) {
        setStatementsError(e.message || "Fehler beim Laden der Abrechnungen");
        // Dummy-Daten als Fallback
        setStatements([
          { id: "1", year: 2023, total: 1234.56, createdAt: "2024-01-10" },
          { id: "2", year: 2022, total: 1100.00, createdAt: "2023-01-10" },
        ]);
      } finally {
        setLoadingStatements(false);
      }
    }
    fetchStatements();
  }, [owner.id]);
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" onClick={onBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-2xl font-bold tracking-tight">{owner.firstName} {owner.lastName}</h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Kontaktdaten" : "Contact Information"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{owner.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>{owner.phone || (language === "de" ? "Keine Telefonnummer" : "No phone number")}</span>
              </div>
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-muted-foreground" />
                <span>{owner.address || (language === "de" ? "Keine Adresse" : "No address")}</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Übersicht" : "Overview"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="flex flex-col items-center justify-center p-4 bg-muted rounded-lg">
                <Building className="h-8 w-8 mb-2 text-primary" />
                <span className="text-xl font-bold">{owner.units.length}</span>
                <span className="text-sm text-muted-foreground">
                  {language === "de" ? "Einheiten" : "Units"}
                </span>
              </div>
              
              <div className="flex flex-col items-center justify-center p-4 bg-muted rounded-lg">
                <User className="h-8 w-8 mb-2 text-primary" />
                <span className="text-xl font-bold">{getTotalArea()} m²</span>
                <span className="text-sm text-muted-foreground">
                  {language === "de" ? "Gesamtfläche" : "Total Area"}
                </span>
              </div>
              
              <div className="flex flex-col items-center justify-center p-4 bg-muted rounded-lg">
                <Plus className="h-8 w-8 mb-2 text-primary" />
                <span className="text-xl font-bold">{getOwnershipTotal()}%</span>
                <span className="text-sm text-muted-foreground">
                  {language === "de" ? "Eigentumsanteil" : "Ownership Share"}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="units" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="units">
            {language === "de" ? "Einheiten" : "Units"}
          </TabsTrigger>
          <TabsTrigger value="statements">
            {language === "de" ? "Abrechnungen" : "Statements"}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="units" className="space-y-4 mt-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Einheiten" : "Units"}
            </h3>
            
            <Dialog open={isAddUnitDialogOpen} onOpenChange={setIsAddUnitDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  {language === "de" ? "Neue Einheit" : "New Unit"}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>
                    {language === "de" ? "Neue Einheit hinzufügen" : "Add New Unit"}
                  </DialogTitle>
                  <DialogDescription>
                    {language === "de" 
                      ? "Weisen Sie dem Eigentümer eine neue Einheit zu." 
                      : "Assign a new unit to the owner."}
                  </DialogDescription>
                </DialogHeader>
                
                <div className="grid gap-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="propertyId">
                      {language === "de" ? "Objekt" : "Property"}
                    </Label>
                    <Select
                      value={newUnit.propertyId}
                      onValueChange={(value) => setNewUnit({...newUnit, propertyId: value})}
                    >
                      <SelectTrigger id="propertyId">
                        <SelectValue placeholder={language === "de" ? "Objekt auswählen" : "Select property"} />
                      </SelectTrigger>
                      <SelectContent>
                        {properties.map((property) => (
                          <SelectItem key={property.id} value={property.id}>
                            {property.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="unitNumber">
                      {language === "de" ? "Einheitsnummer" : "Unit Number"}
                    </Label>
                    <Input
                      id="unitNumber"
                      value={newUnit.unitNumber}
                      onChange={(e) => setNewUnit({...newUnit, unitNumber: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="area">
                      {language === "de" ? "Fläche (m²)" : "Area (m²)"}
                    </Label>
                    <Input
                      id="area"
                      type="number"
                      value={newUnit.area}
                      onChange={(e) => setNewUnit({...newUnit, area: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="ownershipPercentage">
                      {language === "de" ? "Eigentumsanteil (%)" : "Ownership Percentage (%)"}
                    </Label>
                    <Input
                      id="ownershipPercentage"
                      type="number"
                      value={newUnit.ownershipPercentage}
                      onChange={(e) => setNewUnit({...newUnit, ownershipPercentage: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                </div>
                
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddUnitDialogOpen(false)}>
                    {language === "de" ? "Abbrechen" : "Cancel"}
                  </Button>
                  <Button onClick={handleAddUnit}>
                    {language === "de" ? "Hinzufügen" : "Add"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          
          {owner.units.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === "de" ? "Objekt" : "Property"}</TableHead>
                  <TableHead>{language === "de" ? "Einheit" : "Unit"}</TableHead>
                  <TableHead className="text-right">{language === "de" ? "Fläche" : "Area"}</TableHead>
                  <TableHead className="text-right">{language === "de" ? "Eigentumsanteil" : "Ownership"}</TableHead>
                  <TableHead className="w-[80px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {owner.units.map((unit) => (
                  <TableRow key={unit.id}>
                    <TableCell className="font-medium">{getPropertyName(unit.propertyId)}</TableCell>
                    <TableCell>{unit.unitNumber}</TableCell>
                    <TableCell className="text-right">{unit.area} m²</TableCell>
                    <TableCell className="text-right">{unit.ownershipPercentage}%</TableCell>
                    <TableCell>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleDeleteUnit(unit.id)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-6 border rounded-md">
              <p className="text-sm text-muted-foreground">
                {language === "de" 
                  ? "Keine Einheiten zugewiesen. Fügen Sie eine neue Einheit hinzu." 
                  : "No units assigned. Add a new unit."}
              </p>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="statements" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {language === "de" ? "Abrechnungen" : "Statements"}
              </CardTitle>
              <CardDescription>
                {language === "de" 
                  ? "Abrechnungen für diesen Eigentümer." 
                  : "Statements for this owner."}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loadingStatements ? (
                <div className="text-center py-6 text-muted-foreground animate-pulse">
                  {language === "de" ? "Lade Abrechnungen..." : "Loading statements..."}
                </div>
              ) : statementsError ? (
                <div className="text-center py-6 text-red-500">
                  {statementsError}
                </div>
              ) : statements.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  {language === "de" 
                    ? "Keine Abrechnungen gefunden." 
                    : "No statements found."}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border rounded-lg">
                    <thead>
                      <tr className="bg-muted">
                        <th className="px-4 py-2 text-left">{language === "de" ? "Jahr" : "Year"}</th>
                        <th className="px-4 py-2 text-left">{language === "de" ? "Erstellt am" : "Created At"}</th>
                        <th className="px-4 py-2 text-right">{language === "de" ? "Gesamtsumme" : "Total"}</th>
                        <th className="px-4 py-2"></th>
                      </tr>
                    </thead>
                    <tbody>
                      {statements.map((s) => (
                        <tr key={s.id} className="border-b hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                          <td className="px-4 py-2">{s.year}</td>
                          <td className="px-4 py-2">{s.createdAt}</td>
                          <td className="px-4 py-2 text-right font-semibold">{s.total.toLocaleString("de-AT", { style: "currency", currency: "EUR" })}</td>
                          <td className="px-4 py-2 text-right">
                            <Button size="sm" variant="outline">
                              {language === "de" ? "Anzeigen" : "View"}
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
