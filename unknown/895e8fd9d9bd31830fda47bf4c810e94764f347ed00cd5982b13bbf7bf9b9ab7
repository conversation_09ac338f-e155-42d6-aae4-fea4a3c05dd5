// WEG-Accounting Typen

export type DistributionKey = {
  id: string;
  name: string;
  type: "squareMeters" | "units" | "consumption" | "personDays" | "custom";
  description: string;
};

export type Property = {
  id: string;
  name: string;
  address: string;
  units: number;
  totalArea: number;
  yearOfConstruction: number | null;
  distributionKeys: DistributionKey[];
};

export type OwnerUnit = {
  id: string;
  propertyId: string;
  unitNumber: string;
  area: number;
  ownershipPercentage: number;
};

export type Owner = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  units: OwnerUnit[];
};

export type ExpenseCategory = {
  id: string;
  name: string;
  isAllocatable: boolean;
  isHouseholdRelated: boolean;
  isCraftsman: boolean;
};

export type Expense = {
  id: string;
  accountingId: string;
  categoryId: string;
  description: string;
  amount: number;
  date: string;
  distributionKeyId: string;
  householdRelatedAmount: number;
  craftsmanAmount: number;
};

export type AccountingPeriod = {
  id: string;
  propertyId: string;
  year: number;
  startDate: string;
  endDate: string;
  status: "draft" | "completed" | "approved";
  expenses: Expense[];
  openingBalance: number;
  closingBalance: number;
  maintenanceReserveOpening: number;
  maintenanceReserveClosing: number;
  maintenanceReserveContribution: number;
};

export type WEGInvoice = {
  id: string;
  invoiceNumber: string;
  vendorName: string;
  vendorAddress?: string;
  invoiceDate: string;
  dueDate?: string;
  totalAmount: number;
  isPaid: boolean;
  paidDate?: string;
  notes?: string;
  filePath?: string;
};

export type AdvisorPermissions = {
  canViewProperties: boolean;
  canViewOwners: boolean;
  canViewAccounting: boolean;
  canViewInvoices: boolean;
  canViewReports: boolean;
  canDownloadDocuments: boolean;
  canEdit: boolean;
};

export type Advisor = {
  id: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  notes?: string;
  permissions: AdvisorPermissions;
};
