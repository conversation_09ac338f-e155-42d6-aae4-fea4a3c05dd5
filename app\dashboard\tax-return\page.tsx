"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { useState, useEffect } from "react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { InfoIcon, FileText, Calculator, Calendar, CheckCircle } from "lucide-react";
import { TaxTipCard } from "@/app/components/TaxTipCard";
import { VoiceAssistant } from "@/app/components/VoiceAssistant";
import { TaxReturnAssistant } from "@/app/components/tax-return/TaxReturnAssistant";

export default function TaxReturnPage() {
  const { language } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const pageTitle = language === "de" ? "Steuerausgleich" : "Tax Return";
  const pageDescription = language === "de"
    ? "Österreichischer Steuerausgleich Leitfaden"
    : "Austrian Tax Return Guide";

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{pageTitle}</h1>
        <p className="text-muted-foreground">{pageDescription}</p>
      </div>

      <Alert>
        <InfoIcon className="h-4 w-4" />
        <AlertTitle>
          {language === "de"
            ? "Wichtige Information"
            : "Important Information"}
        </AlertTitle>
        <AlertDescription>
          {language === "de"
            ? "Dieser Leitfaden dient nur zu Informationszwecken. Für eine individuelle Steuerberatung wenden Sie sich bitte an einen Steuerberater."
            : "This guide is for informational purposes only. For individual tax advice, please consult a tax professional."}
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">
            {language === "de" ? "Überblick" : "Overview"}
          </TabsTrigger>
          <TabsTrigger value="documents">
            {language === "de" ? "Dokumente" : "Documents"}
          </TabsTrigger>
          <TabsTrigger value="deductions">
            {language === "de" ? "Absetzbeträge" : "Deductions"}
          </TabsTrigger>
          <TabsTrigger value="timeline">
            {language === "de" ? "Zeitplan" : "Timeline"}
          </TabsTrigger>
          <TabsTrigger value="assistant" className="bg-blue-50 text-blue-600 data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 dark:data-[state=active]:bg-blue-900/40 dark:data-[state=active]:text-blue-300">
            {language === "de" ? "Steuerausgleich starten" : "Start Tax Return"}
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {language === "de" ? "Was ist der Steuerausgleich?" : "What is the Tax Return?"}
              </CardTitle>
              <CardDescription>
                {language === "de"
                  ? "Grundlegende Informationen zum österreichischen Steuerausgleich"
                  : "Basic information about the Austrian tax return process"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                {language === "de"
                  ? "Der Steuerausgleich (auch Arbeitnehmerveranlagung genannt) ist ein Prozess, bei dem Arbeitnehmer in Österreich zu viel bezahlte Lohnsteuer vom Finanzamt zurückfordern können."
                  : "The tax return (also called 'Arbeitnehmerveranlagung' in Austria) is a process where employees in Austria can reclaim overpaid income tax from the tax office."}
              </p>

              <h3 className="text-lg font-medium mt-4">
                {language === "de" ? "Wer sollte einen Steuerausgleich machen?" : "Who should file a tax return?"}
              </h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>
                  {language === "de"
                    ? "Arbeitnehmer, die Werbungskosten, Sonderausgaben oder außergewöhnliche Belastungen hatten"
                    : "Employees who had work-related expenses, special expenses, or extraordinary burdens"}
                </li>
                <li>
                  {language === "de"
                    ? "Personen mit mehreren Einkünften oder wechselnden Gehältern"
                    : "People with multiple sources of income or varying salaries"}
                </li>
                <li>
                  {language === "de"
                    ? "Personen, die nicht das ganze Jahr beschäftigt waren"
                    : "People who were not employed for the entire year"}
                </li>
                <li>
                  {language === "de"
                    ? "Personen mit Anspruch auf Absetzbeträge (z.B. Alleinverdienerabsetzbetrag)"
                    : "People eligible for tax credits (e.g., single earner tax credit)"}
                </li>
              </ul>

              <h3 className="text-lg font-medium mt-4">
                {language === "de" ? "Vorteile des Steuerausgleichs" : "Benefits of Filing a Tax Return"}
              </h3>
              <ul className="list-disc pl-5 space-y-2">
                <li>
                  {language === "de"
                    ? "Rückerstattung von zu viel bezahlter Lohnsteuer"
                    : "Refund of overpaid income tax"}
                </li>
                <li>
                  {language === "de"
                    ? "Geltendmachung von berufsbedingten Ausgaben"
                    : "Claiming work-related expenses"}
                </li>
                <li>
                  {language === "de"
                    ? "Nutzung von Steuervergünstigungen und Absetzbeträgen"
                    : "Utilizing tax benefits and credits"}
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {language === "de" ? "Benötigte Dokumente" : "Required Documents"}
              </CardTitle>
              <CardDescription>
                {language === "de"
                  ? "Dokumente, die Sie für Ihren Steuerausgleich benötigen"
                  : "Documents you need for your tax return"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <AccordionTrigger className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-500" />
                    {language === "de" ? "Grundlegende Dokumente" : "Basic Documents"}
                  </AccordionTrigger>
                  <AccordionContent>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>
                        {language === "de"
                          ? "Jahreslohnzettel (L16) von allen Arbeitgebern"
                          : "Annual pay slip (L16) from all employers"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Lohnzettel für Krankengeld oder Arbeitslosengeld"
                          : "Pay slips for sick pay or unemployment benefits"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Sozialversicherungsnummer"
                          : "Social security number"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Bankverbindung für die Rückerstattung"
                          : "Bank details for the refund"}
                      </li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="item-2">
                  <AccordionTrigger className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-green-500" />
                    {language === "de" ? "Belege für Werbungskosten" : "Receipts for Work Expenses"}
                  </AccordionTrigger>
                  <AccordionContent>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>
                        {language === "de"
                          ? "Belege für Fortbildungskosten"
                          : "Receipts for further education costs"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Fahrtkosten (z.B. Pendlerpauschale)"
                          : "Travel expenses (e.g., commuter allowance)"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Belege für Arbeitsmittel"
                          : "Receipts for work equipment"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Belege für Fachliteratur"
                          : "Receipts for professional literature"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Belege für Homeoffice-Kosten"
                          : "Receipts for home office expenses"}
                      </li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="item-3">
                  <AccordionTrigger className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-purple-500" />
                    {language === "de" ? "Belege für Sonderausgaben" : "Receipts for Special Expenses"}
                  </AccordionTrigger>
                  <AccordionContent>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>
                        {language === "de"
                          ? "Spenden an begünstigte Organisationen"
                          : "Donations to eligible organizations"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Kirchenbeiträge"
                          : "Church contributions"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Belege für freiwillige Versicherungen"
                          : "Receipts for voluntary insurance"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Belege für Steuerberatungskosten"
                          : "Receipts for tax consulting costs"}
                      </li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="item-4">
                  <AccordionTrigger className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-orange-500" />
                    {language === "de" ? "Belege für außergewöhnliche Belastungen" : "Receipts for Extraordinary Burdens"}
                  </AccordionTrigger>
                  <AccordionContent>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>
                        {language === "de"
                          ? "Krankheitskosten (Arzt, Medikamente, Therapien)"
                          : "Medical expenses (doctor, medication, therapies)"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Kosten für Behinderung"
                          : "Disability-related expenses"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Begräbniskosten"
                          : "Funeral expenses"}
                      </li>
                      <li>
                        {language === "de"
                          ? "Kosten für Kinderbetreuung"
                          : "Childcare expenses"}
                      </li>
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Deductions Tab */}
        <TabsContent value="deductions" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {language === "de" ? "Wichtige Absetzbeträge" : "Important Deductions"}
              </CardTitle>
              <CardDescription>
                {language === "de"
                  ? "Absetzbeträge, die Ihre Steuerlast reduzieren können"
                  : "Deductions that can reduce your tax burden"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Calculator className="h-5 w-5 text-blue-500" />
                    <h3 className="text-lg font-medium">
                      {language === "de" ? "Werbungskosten" : "Work-Related Expenses"}
                    </h3>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {language === "de"
                      ? "Ausgaben, die im Zusammenhang mit Ihrer beruflichen Tätigkeit stehen."
                      : "Expenses related to your professional activity."}
                  </p>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>
                      {language === "de"
                        ? "Pendlerpauschale für Fahrten zwischen Wohnung und Arbeit"
                        : "Commuter allowance for travel between home and work"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Arbeitsmittel (Computer, Fachliteratur, Werkzeuge)"
                        : "Work equipment (computers, professional literature, tools)"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Aus- und Fortbildungskosten"
                        : "Education and training costs"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Homeoffice-Pauschale"
                        : "Home office allowance"}
                    </li>
                  </ul>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Calculator className="h-5 w-5 text-green-500" />
                    <h3 className="text-lg font-medium">
                      {language === "de" ? "Sonderausgaben" : "Special Expenses"}
                    </h3>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {language === "de"
                      ? "Private Ausgaben, die steuerlich absetzbar sind."
                      : "Private expenses that are tax deductible."}
                  </p>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>
                      {language === "de"
                        ? "Spenden an begünstigte Organisationen (bis zu 10% des Jahreseinkommens)"
                        : "Donations to eligible organizations (up to 10% of annual income)"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Kirchenbeiträge (bis zu 400€ jährlich)"
                        : "Church contributions (up to €400 annually)"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Steuerberatungskosten"
                        : "Tax consulting costs"}
                    </li>
                  </ul>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Calculator className="h-5 w-5 text-purple-500" />
                    <h3 className="text-lg font-medium">
                      {language === "de" ? "Außergewöhnliche Belastungen" : "Extraordinary Burdens"}
                    </h3>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {language === "de"
                      ? "Unvermeidbare Ausgaben, die die wirtschaftliche Leistungsfähigkeit wesentlich beeinträchtigen."
                      : "Unavoidable expenses that significantly impair economic capacity."}
                  </p>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>
                      {language === "de"
                        ? "Krankheitskosten (abzüglich Selbstbehalt)"
                        : "Medical expenses (minus deductible)"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Kosten für Behinderung (ohne Selbstbehalt)"
                        : "Disability-related expenses (without deductible)"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Katastrophenschäden"
                        : "Disaster damages"}
                    </li>
                  </ul>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Calculator className="h-5 w-5 text-orange-500" />
                    <h3 className="text-lg font-medium">
                      {language === "de" ? "Absetzbeträge" : "Tax Credits"}
                    </h3>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {language === "de"
                      ? "Direkte Reduktion der Steuerlast (nicht des steuerpflichtigen Einkommens)."
                      : "Direct reduction of tax burden (not of taxable income)."}
                  </p>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li>
                      {language === "de"
                        ? "Verkehrsabsetzbetrag (automatisch berücksichtigt)"
                        : "Traffic tax credit (automatically considered)"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Alleinverdiener-/Alleinerzieherabsetzbetrag"
                        : "Single earner/single parent tax credit"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Familienbonus Plus"
                        : "Family bonus plus"}
                    </li>
                    <li>
                      {language === "de"
                        ? "Pensionistenabsetzbetrag"
                        : "Pensioner tax credit"}
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Timeline Tab */}
        <TabsContent value="timeline" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {language === "de" ? "Zeitplan für den Steuerausgleich" : "Tax Return Timeline"}
              </CardTitle>
              <CardDescription>
                {language === "de"
                  ? "Wichtige Termine und Schritte für Ihren Steuerausgleich"
                  : "Important dates and steps for your tax return"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative border-l-2 border-gray-200 dark:border-gray-700 ml-3 pl-8 space-y-10">
                <div className="relative">
                  <div className="absolute -left-11 flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300">
                    <Calendar className="h-4 w-4" />
                  </div>
                  <h3 className="text-lg font-medium">
                    {language === "de" ? "Januar - Februar" : "January - February"}
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {language === "de"
                      ? "Sammeln Sie alle notwendigen Unterlagen und Belege für das vergangene Jahr."
                      : "Collect all necessary documents and receipts for the past year."}
                  </p>
                </div>

                <div className="relative">
                  <div className="absolute -left-11 flex items-center justify-center w-6 h-6 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300">
                    <Calendar className="h-4 w-4" />
                  </div>
                  <h3 className="text-lg font-medium">
                    {language === "de" ? "März - Juni" : "March - June"}
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {language === "de"
                      ? "Idealer Zeitraum für die Einreichung der Arbeitnehmerveranlagung, da alle Lohnzettel und Meldungen von Arbeitgebern und Organisationen bereits beim Finanzamt vorliegen sollten."
                      : "Ideal period for filing your tax return, as all pay slips and reports from employers and organizations should already be available to the tax office."}
                  </p>
                </div>

                <div className="relative">
                  <div className="absolute -left-11 flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300">
                    <Calendar className="h-4 w-4" />
                  </div>
                  <h3 className="text-lg font-medium">
                    {language === "de" ? "30. Juni" : "June 30"}
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {language === "de"
                      ? "Frist für die verpflichtende Steuererklärung (wenn Sie neben Ihrem Gehalt weitere Einkünfte über 730€ hatten)."
                      : "Deadline for mandatory tax returns (if you had additional income over €730 besides your salary)."}
                  </p>
                </div>

                <div className="relative">
                  <div className="absolute -left-11 flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300">
                    <Calendar className="h-4 w-4" />
                  </div>
                  <h3 className="text-lg font-medium">
                    {language === "de" ? "31. Dezember" : "December 31"}
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {language === "de"
                      ? "Letzter Tag für die freiwillige Einreichung der Arbeitnehmerveranlagung für das vorletzte Jahr (z.B. 2023 für das Steuerjahr 2021)."
                      : "Last day for voluntary submission of the tax return for the year before last (e.g., 2023 for the 2021 tax year)."}
                  </p>
                </div>

                <div className="relative">
                  <div className="absolute -left-11 flex items-center justify-center w-6 h-6 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300">
                    <CheckCircle className="h-4 w-4" />
                  </div>
                  <h3 className="text-lg font-medium">
                    {language === "de" ? "Nach der Einreichung" : "After Submission"}
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {language === "de"
                      ? "Die Bearbeitung durch das Finanzamt dauert in der Regel 4-8 Wochen. Bei automatischer Arbeitnehmerveranlagung erfolgt die Bearbeitung meist schneller."
                      : "Processing by the tax office usually takes 4-8 weeks. With automatic employee assessment, processing is usually faster."}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Assistant Tab */}
        <TabsContent value="assistant" className="mt-4">
          <TaxReturnAssistant />
        </TabsContent>
      </Tabs>

      {/* Tax Tip Card */}
      <TaxTipCard />

      {/* Voice Assistant */}
      <VoiceAssistant />
    </div>
  );
}
