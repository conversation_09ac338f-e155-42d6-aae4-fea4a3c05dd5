import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: accountingPeriodId } = await params;

    // Check if accounting period exists and belongs to user
    const accountingPeriod = await prisma.accountingPeriod.findFirst({
      where: {
        id: accountingPeriodId,
        userId: session.user.id,
      },
      include: {
        expenses: true,
      },
    });

    if (!accountingPeriod) {
      return NextResponse.json({ error: "Accounting period not found" }, { status: 404 });
    }

    // Delete all related expenses first (cascade)
    await prisma.expense.deleteMany({
      where: {
        accountingPeriodId: accountingPeriodId,
      },
    });

    // Delete the accounting period
    await prisma.accountingPeriod.delete({
      where: {
        id: accountingPeriodId,
      },
    });

    return NextResponse.json({ message: "Accounting period deleted successfully" });
  } catch (error) {
    console.error("Failed to delete accounting period:", error);
    return NextResponse.json({ error: "Failed to delete accounting period" }, { status: 500 });
  }
}
