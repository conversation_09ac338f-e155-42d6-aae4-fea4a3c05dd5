"use server"

import { requireUser } from "../utils/hooks"
import prisma from "../utils/db"

export async function saveUserSettings(theme: string, language: string, visibleTabs?: string) {
  const session = await requireUser()
  const userId = session.user?.id as string

  if (!userId) {
    throw new Error("User not authenticated")
  }

  // Check if user settings already exist
  const existingSettings = await prisma.userSettings.findUnique({
    where: {
      userId,
    },
  })

  // Prepare data object
  const data: any = {
    theme,
    language,
  }

  // Only add visibleTabs if it's provided
  if (visibleTabs !== undefined) {
    data.visibleTabs = visibleTabs
  }

  if (existingSettings) {
    // Update existing settings
    await prisma.userSettings.update({
      where: {
        userId,
      },
      data,
    })
  } else {
    // Create new settings
    await prisma.userSettings.create({
      data: {
        userId,
        ...data,
      },
    })
  }

  return { success: true }
}

export async function getUserSettings() {
  const session = await requireUser()
  const userId = session.user?.id as string

  if (!userId) {
    return null
  }

  const settings = await prisma.userSettings.findUnique({
    where: {
      userId,
    },
  })

  return settings
}

export async function saveVisibleTabs(visibleTabs: string) {
  const session = await requireUser()
  const userId = session.user?.id as string

  if (!userId) {
    throw new Error("User not authenticated")
  }

  // Da das visibleTabs-Feld noch nicht in der Datenbank existiert,
  // speichern wir die Einstellung vorübergehend im localStorage
  // Dies ist eine temporäre Lösung, bis die Datenbankänderungen angewendet werden können

  return { success: true }
}
