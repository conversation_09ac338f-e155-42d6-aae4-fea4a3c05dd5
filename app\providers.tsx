"use client"

import React from 'react'
import { LanguageProvider } from '@/app/contexts/LanguageContext'
import { ThemeProvider } from '@/components/ui/ThemeProvider'

interface ProvidersProps {
  children: React.ReactNode
  initialTheme?: string
  initialLanguage?: string
}

export function Providers({
  children,
  initialTheme = "system",
  initialLanguage = "de"
}: ProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme={initialTheme as "light" | "dark" | "system"}
      enableSystem
      disableTransitionOnChange
      suppressHydrationWarning
    >
      <LanguageProvider defaultLanguage={initialLanguage as "en" | "de"}>
        {children}
      </LanguageProvider>
    </ThemeProvider>
  )
}
