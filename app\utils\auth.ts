import { PrismaAdapter } from "@auth/prisma-adapter";
import <PERSON><PERSON><PERSON> from "next-auth";
import <PERSON><PERSON>mail<PERSON> from "next-auth/providers/nodemailer";
import CredentialsProvider from "next-auth/providers/credentials";
import { compare, hash } from "bcrypt";
import prisma from "./db";

// Funktion zum Hashen von Passwörtern für die Registrierung
export async function hashPassword(password: string) {
  return await hash(password, 10);
}

export const { handlers, signIn, signOut, auth } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    // E-Mail-Provider für die E-Mail-Bestätigung
    ...(process.env.EMAIL_SERVER_HOST ? [Nodemailer({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: Number(process.env.EMAIL_SERVER_PORT) || 587,
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM,
    })] : []),
    // Credentials-Provider für Benutzername/Passwort-Authentifizierung
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const email = credentials.email as string;
        const password = credentials.password as string;

        // Benutzer in der Datenbank suchen
        const user = await prisma.user.findUnique({
          where: { email }
        });

        // Wenn kein Benutzer gefunden wurde oder kein Passwort gesetzt ist
        if (!user || !user.password) {
          return null;
        }

        // Passwort überprüfen
        const isPasswordValid = await compare(password, user.password);

        if (!isPasswordValid) {
          return null;
        }

        // Wenn die E-Mail nicht bestätigt wurde
        if (!user.emailVerified) {
          throw new Error("email-not-verified");
        }

        return {
          id: user.id,
          email: user.email,
          name: user.firstName ? `${user.firstName} ${user.lastName}` : user.email,
        };
      }
    }),
  ],
  pages: {
    signIn: "/weg-accounting/login",
    verifyRequest: "/verify",
    error: "/weg-accounting/login",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },
  session: {
    strategy: "jwt",
  },
});
