"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Info, AlertCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function TaxCreditsStep() {
  const { language } = useLanguage();

  // Beispieldaten für Absetzbeträge
  const [generalCredits, setGeneralCredits] = useState({
    singleEarner: false,
    singleParent: false,
    numberOfChildren: "2",
  });

  const [familyBonus, setFamilyBonus] = useState({
    applyFamilyBonus: true,
    children: [
      { id: "1", name: "<PERSON>", birthDate: "2015-05-10", percentage: "100" },
      { id: "2", name: "Max", birthDate: "2018-11-22", percentage: "100" },
    ],
  });

  const [commuter, setCommuter] = useState({
    applyCommuterCredit: true,
    distance: "35",
    publicTransportPossible: false,
  });

  const handleGeneralCreditsChange = (field: string, value: string | boolean) => {
    setGeneralCredits({
      ...generalCredits,
      [field]: value,
    });
  };

  const handleFamilyBonusChange = (field: string, value: string | boolean) => {
    setFamilyBonus({
      ...familyBonus,
      [field]: value,
    });
  };

  const handleChildChange = (id: string, field: string, value: string) => {
    setFamilyBonus({
      ...familyBonus,
      children: familyBonus.children.map((child) =>
        child.id === id ? { ...child, [field]: value } : child
      ),
    });
  };

  const handleCommuterChange = (field: string, value: string | boolean) => {
    setCommuter({
      ...commuter,
      [field]: value,
    });
  };

  const addChild = () => {
    const newId = (familyBonus.children.length + 1).toString();
    setFamilyBonus({
      ...familyBonus,
      children: [
        ...familyBonus.children,
        { id: newId, name: "", birthDate: "", percentage: "100" },
      ],
    });
  };

  const removeChild = (id: string) => {
    setFamilyBonus({
      ...familyBonus,
      children: familyBonus.children.filter((child) => child.id !== id),
    });
  };

  // Berechne den Familienbonus Plus
  const calculateFamilyBonus = () => {
    if (!familyBonus.applyFamilyBonus) return 0;

    return familyBonus.children.reduce((total, child) => {
      // Berechne das Alter des Kindes
      const birthDate = new Date(child.birthDate);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();

      // Familienbonus Plus beträgt 2.000€ pro Kind unter 18 Jahren
      // und 650€ pro Kind ab 18 Jahren
      const baseAmount = age < 18 ? 2000 : 650;

      // Berücksichtige den Prozentsatz (z.B. bei geteiltem Familienbonus)
      const percentage = child.percentage ? parseFloat(child.percentage) / 100 : 1;

      return total + (baseAmount * percentage);
    }, 0);
  };

  // Berechne den Pendlereuro
  const calculateCommuterEuro = () => {
    if (!commuter.applyCommuterCredit) return 0;

    const distance = parseInt(commuter.distance);
    // Pendlereuro: 2€ pro Kilometer des einfachen Arbeitsweges pro Jahr
    return distance * 2;
  };

  // Berechne den Alleinverdiener-/Alleinerzieherabsetzbetrag
  const calculateSingleEarnerCredit = () => {
    if (!generalCredits.singleEarner && !generalCredits.singleParent) return 0;

    const numberOfChildren = parseInt(generalCredits.numberOfChildren);

    // Alleinverdiener-/Alleinerzieherabsetzbetrag
    if (numberOfChildren === 0) return 0;
    if (numberOfChildren === 1) return 494;
    if (numberOfChildren === 2) return 669;
    return 669 + (numberOfChildren - 2) * 220; // 220€ für jedes weitere Kind
  };

  // Berechne die Gesamtsumme der Absetzbeträge
  const calculateTotalCredits = () => {
    return calculateFamilyBonus() + calculateCommuterEuro() + calculateSingleEarnerCredit();
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">
            {language === "de" ? "Allgemein" : "General"}
          </TabsTrigger>
          <TabsTrigger value="familyBonus">
            {language === "de" ? "Familienbonus Plus" : "Family Bonus Plus"}
          </TabsTrigger>
          <TabsTrigger value="commuter">
            {language === "de" ? "Pendlereuro" : "Commuter Euro"}
          </TabsTrigger>
        </TabsList>

        {/* Allgemeine Tab */}
        <TabsContent value="general" className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Allgemeine Absetzbeträge" : "General Tax Credits"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Absetzbeträge reduzieren deine Steuerlast direkt und nicht nur dein zu versteuerndes Einkommen."
                : "Tax credits directly reduce your tax burden, not just your taxable income."}
            </p>
          </div>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                  <div className="flex items-start">
                    <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-2 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-blue-700 dark:text-blue-300">
                        {language === "de" ? "Automatisch berücksichtigte Absetzbeträge" : "Automatically Considered Tax Credits"}
                      </h4>
                      <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                        {language === "de"
                          ? "Folgende Absetzbeträge werden automatisch berücksichtigt: Verkehrsabsetzbetrag (400€), Pensionistenabsetzbetrag (falls zutreffend)."
                          : "The following tax credits are automatically considered: Traffic tax credit (€400), pensioner tax credit (if applicable)."}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">
                    {language === "de" ? "Alleinverdiener-/Alleinerzieherabsetzbetrag" : "Single Earner/Single Parent Tax Credit"}
                  </h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="singleEarner"
                        checked={generalCredits.singleEarner}
                        onCheckedChange={(checked) => {
                          handleGeneralCreditsChange("singleEarner", checked as boolean);
                          if (checked) {
                            handleGeneralCreditsChange("singleParent", false);
                          }
                        }}
                      />
                      <Label htmlFor="singleEarner" className="cursor-pointer">
                        {language === "de" ? "Alleinverdienerabsetzbetrag" : "Single Earner Tax Credit"}
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="singleParent"
                        checked={generalCredits.singleParent}
                        onCheckedChange={(checked) => {
                          handleGeneralCreditsChange("singleParent", checked as boolean);
                          if (checked) {
                            handleGeneralCreditsChange("singleEarner", false);
                          }
                        }}
                      />
                      <Label htmlFor="singleParent" className="cursor-pointer">
                        {language === "de" ? "Alleinerzieherabsetzbetrag" : "Single Parent Tax Credit"}
                      </Label>
                    </div>
                  </div>

                  {(generalCredits.singleEarner || generalCredits.singleParent) && (
                    <div className="space-y-2 mt-2">
                      <Label htmlFor="numberOfChildren">
                        {language === "de" ? "Anzahl der Kinder" : "Number of Children"}
                      </Label>
                      <Select
                        value={generalCredits.numberOfChildren}
                        onValueChange={(value) => handleGeneralCreditsChange("numberOfChildren", value)}
                      >
                        <SelectTrigger id="numberOfChildren">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">0</SelectItem>
                          <SelectItem value="1">1</SelectItem>
                          <SelectItem value="2">2</SelectItem>
                          <SelectItem value="3">3</SelectItem>
                          <SelectItem value="4">4</SelectItem>
                          <SelectItem value="5+">5+</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Familienbonus Plus Tab */}
        <TabsContent value="familyBonus" className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Familienbonus Plus" : "Family Bonus Plus"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Der Familienbonus Plus ist ein Steuerabsetzbetrag, der deine Steuerlast direkt reduziert."
                : "The Family Bonus Plus is a tax credit that directly reduces your tax burden."}
            </p>
          </div>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="applyFamilyBonus"
                    checked={familyBonus.applyFamilyBonus}
                    onCheckedChange={(checked) =>
                      handleFamilyBonusChange("applyFamilyBonus", checked as boolean)
                    }
                  />
                  <Label htmlFor="applyFamilyBonus" className="cursor-pointer">
                    {language === "de" ? "Familienbonus Plus beantragen" : "Apply for Family Bonus Plus"}
                  </Label>
                </div>

                {familyBonus.applyFamilyBonus && (
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                      <div className="flex items-start">
                        <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-2 flex-shrink-0" />
                        <div>
                          <p className="text-sm text-blue-600 dark:text-blue-400">
                            {language === "de"
                              ? "Der Familienbonus Plus beträgt bis zu 2.000€ pro Kind unter 18 Jahren und 650€ pro Kind ab 18 Jahren, für das Familienbeihilfe bezogen wird."
                              : "The Family Bonus Plus amounts to up to €2,000 per child under 18 years and €650 per child from 18 years for whom family allowance is received."}
                          </p>
                        </div>
                      </div>
                    </div>

                    {familyBonus.children.map((child) => (
                      <div key={child.id} className="p-4 border rounded-md">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor={`childName-${child.id}`}>
                              {language === "de" ? "Name des Kindes" : "Child's Name"}
                            </Label>
                            <Input
                              id={`childName-${child.id}`}
                              value={child.name}
                              onChange={(e) => handleChildChange(child.id, "name", e.target.value)}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor={`childBirthDate-${child.id}`}>
                              {language === "de" ? "Geburtsdatum" : "Birth Date"}
                            </Label>
                            <Input
                              id={`childBirthDate-${child.id}`}
                              value={child.birthDate}
                              onChange={(e) => handleChildChange(child.id, "birthDate", e.target.value)}
                              type="date"
                            />
                          </div>

                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor={`childPercentage-${child.id}`}>
                              {language === "de" ? "Prozentsatz (%)" : "Percentage (%)"}
                            </Label>
                            <Select
                              value={child.percentage}
                              onValueChange={(value) => handleChildChange(child.id, "percentage", value)}
                            >
                              <SelectTrigger id={`childPercentage-${child.id}`}>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="100">100%</SelectItem>
                                <SelectItem value="90">90%</SelectItem>
                                <SelectItem value="80">80%</SelectItem>
                                <SelectItem value="70">70%</SelectItem>
                                <SelectItem value="60">60%</SelectItem>
                                <SelectItem value="50">50%</SelectItem>
                                <SelectItem value="40">40%</SelectItem>
                                <SelectItem value="30">30%</SelectItem>
                                <SelectItem value="20">20%</SelectItem>
                                <SelectItem value="10">10%</SelectItem>
                                <SelectItem value="0">0%</SelectItem>
                              </SelectContent>
                            </Select>
                            <p className="text-xs text-muted-foreground">
                              {language === "de"
                                ? "Bei geteiltem Familienbonus: Gib an, zu welchem Prozentsatz du den Familienbonus beantragst."
                                : "For shared Family Bonus: Specify the percentage at which you are applying for the Family Bonus."}
                            </p>
                          </div>
                        </div>

                        {familyBonus.children.length > 1 && (
                          <div className="mt-4 flex justify-end">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeChild(child.id)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                            >
                              {language === "de" ? "Entfernen" : "Remove"}
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}

                    <Button
                      variant="outline"
                      onClick={addChild}
                      className="mt-2"
                    >
                      {language === "de" ? "Weiteres Kind hinzufügen" : "Add Another Child"}
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pendlereuro Tab */}
        <TabsContent value="commuter" className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Pendlereuro" : "Commuter Euro"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Der Pendlereuro ist ein zusätzlicher Steuerabsetzbetrag für Pendler."
                : "The Commuter Euro is an additional tax credit for commuters."}
            </p>
          </div>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="applyCommuterCredit"
                    checked={commuter.applyCommuterCredit}
                    onCheckedChange={(checked) =>
                      handleCommuterChange("applyCommuterCredit", checked as boolean)
                    }
                  />
                  <Label htmlFor="applyCommuterCredit" className="cursor-pointer">
                    {language === "de" ? "Pendlereuro beantragen" : "Apply for Commuter Euro"}
                  </Label>
                </div>

                {commuter.applyCommuterCredit && (
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                      <div className="flex items-start">
                        <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-2 flex-shrink-0" />
                        <div>
                          <p className="text-sm text-blue-600 dark:text-blue-400">
                            {language === "de"
                              ? "Der Pendlereuro beträgt 2€ pro Kilometer des einfachen Arbeitsweges pro Jahr und wird zusätzlich zur Pendlerpauschale gewährt."
                              : "The Commuter Euro amounts to €2 per kilometer of the one-way commute per year and is granted in addition to the commuter allowance."}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="distance">
                        {language === "de" ? "Entfernung Wohnung - Arbeit (km)" : "Distance Home - Work (km)"}
                      </Label>
                      <Input
                        id="distance"
                        value={commuter.distance}
                        onChange={(e) => handleCommuterChange("distance", e.target.value)}
                        type="number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>
                        {language === "de" ? "Öffentliche Verkehrsmittel zumutbar?" : "Public Transport Reasonable?"}
                      </Label>
                      <div className="flex space-x-4 mt-1">
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="publicTransportYes"
                            name="publicTransport"
                            checked={commuter.publicTransportPossible}
                            onChange={() => handleCommuterChange("publicTransportPossible", true)}
                            className="h-4 w-4"
                          />
                          <Label htmlFor="publicTransportYes" className="cursor-pointer">
                            {language === "de" ? "Ja" : "Yes"}
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="publicTransportNo"
                            name="publicTransport"
                            checked={!commuter.publicTransportPossible}
                            onChange={() => handleCommuterChange("publicTransportPossible", false)}
                            className="h-4 w-4"
                          />
                          <Label htmlFor="publicTransportNo" className="cursor-pointer">
                            {language === "de" ? "Nein" : "No"}
                          </Label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Zusammenfassung */}
      <Card className="border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/20">
        <CardContent className="pt-6">
          <h4 className="font-medium text-green-700 dark:text-green-300 mb-2">
            {language === "de" ? "Zusammenfassung der Absetzbeträge" : "Summary of Tax Credits"}
          </h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>{language === "de" ? "Verkehrsabsetzbetrag (automatisch)" : "Traffic Tax Credit (automatic)"}</span>
              <span className="font-medium">€400,00</span>
            </div>

            {(generalCredits.singleEarner || generalCredits.singleParent) && (
              <div className="flex justify-between">
                <span>
                  {generalCredits.singleEarner
                    ? (language === "de" ? "Alleinverdienerabsetzbetrag" : "Single Earner Tax Credit")
                    : (language === "de" ? "Alleinerzieherabsetzbetrag" : "Single Parent Tax Credit")}
                </span>
                <span className="font-medium">€{calculateSingleEarnerCredit().toFixed(2)}</span>
              </div>
            )}

            {familyBonus.applyFamilyBonus && (
              <div className="flex justify-between">
                <span>{language === "de" ? "Familienbonus Plus" : "Family Bonus Plus"}</span>
                <span className="font-medium">€{calculateFamilyBonus().toFixed(2)}</span>
              </div>
            )}

            {commuter.applyCommuterCredit && (
              <div className="flex justify-between">
                <span>{language === "de" ? "Pendlereuro" : "Commuter Euro"}</span>
                <span className="font-medium">€{calculateCommuterEuro().toFixed(2)}</span>
              </div>
            )}

            <div className="border-t pt-2 mt-2 flex justify-between font-bold">
              <span>{language === "de" ? "Gesamtsumme der Absetzbeträge" : "Total Tax Credits"}</span>
              <span className="text-green-600 dark:text-green-400">
                €{(400 + calculateTotalCredits()).toFixed(2)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* FinanzOnline Hinweis */}
      <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-amber-800 dark:text-amber-300">
              {language === "de" ? "Wo finde ich das in FinanzOnline?" : "Where do I find this in FinanzOnline?"}
            </h4>
            <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
              {language === "de"
                ? "Absetzbeträge werden in FinanzOnline unter 'Absetzbeträge' eingetragen. Der Familienbonus Plus wird unter 'Familienbonus Plus' beantragt, der Alleinverdiener-/Alleinerzieherabsetzbetrag unter 'Alleinverdiener-/Alleinerzieherabsetzbetrag' und der Pendlereuro unter 'Pendlerpauschale/Pendlereuro'."
                : "Tax credits are entered in FinanzOnline under 'Tax Credits'. The Family Bonus Plus is applied for under 'Family Bonus Plus', the Single Earner/Single Parent Tax Credit under 'Single Earner/Single Parent Tax Credit', and the Commuter Euro under 'Commuter Allowance/Commuter Euro'."}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
