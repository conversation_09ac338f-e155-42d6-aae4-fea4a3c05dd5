"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Activity, CreditCard, DollarSign, Users } from "lucide-react";
import { useLanguage } from '@/app/contexts/LanguageContext';
import { useEffect, useState } from 'react';

interface DashboardBlocksClientProps {
  totalRevenue: string;
  totalInvoices: number;
  paidInvoices: number;
  pendingInvoices: number;
}

export function DashboardBlocksClient({
  totalRevenue,
  totalInvoices,
  paidInvoices,
  pendingInvoices
}: DashboardBlocksClientProps) {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 md:gap-8">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {mounted ? t('totalRevenue') : "Total Revenue"}
          </CardTitle>
          <DollarSign className="size-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <h2 className="text-2xl font-bold">{totalRevenue}</h2>
          <p className="text-xs text-muted-foreground">
            {mounted ? t('basedOnTotalVolume') : "Based on total volume"}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {mounted ? t('totalInvoicesIssued') : "Total Invoices Issued"}
          </CardTitle>
          <Users className="size-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <h2 className="text-2xl font-bold">+{totalInvoices}</h2>
          <p className="text-xs text-muted-foreground">
            {mounted ? t('totalInvoicesIssuedDesc') : "Total Invoices Issued!"}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {mounted ? t('paidInvoices') : "Paid Invoices"}
          </CardTitle>
          <CreditCard className="size-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <h2 className="text-2xl font-bold">+{paidInvoices}</h2>
          <p className="text-xs text-muted-foreground">
            {mounted ? t('paidInvoicesDesc') : "Total Invoices which have been paid!"}
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {mounted ? t('pendingInvoices') : "Pending Invoices"}
          </CardTitle>
          <Activity className="size-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <h2 className="text-2xl font-bold">+{pendingInvoices}</h2>
          <p className="text-xs text-muted-foreground">
            {mounted ? t('pendingInvoicesDesc') : "Invoices which are currently pending!"}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
