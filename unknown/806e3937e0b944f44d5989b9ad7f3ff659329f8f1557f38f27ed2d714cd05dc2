"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON>F<PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, ArrowRight, HelpCircle, CheckCircle } from "lucide-react";
import { PersonalInfoStep } from "./steps/PersonalInfoStep";
import { IncomeStep } from "./steps/IncomeStep";
import { ExpensesStep } from "./steps/ExpensesStep";
import { SpecialExpensesStep } from "./steps/SpecialExpensesStep";
import { ExtraordinaryBurdensStep } from "./steps/ExtraordinaryBurdensStep";
import { TaxCreditsStep } from "./steps/TaxCreditsStep";
import { SummaryStep } from "./steps/SummaryStep";

// Definiere die Schritte des Steuerausgleich-Assistenten
type Step = {
  id: string;
  titleDE: string;
  titleEN: string;
  descriptionDE: string;
  descriptionEN: string;
};

const steps: Step[] = [
  {
    id: "intro",
    titleDE: "Willkommen beim Steuerausgleich-Assistenten",
    titleEN: "Welcome to the Tax Return Assistant",
    descriptionDE: "Dieser Assistent führt dich Schritt für Schritt durch deinen Steuerausgleich. Du kannst jederzeit Fragen stellen oder Hilfe anfordern.",
    descriptionEN: "This assistant will guide you step by step through your tax return. You can ask questions or request help at any time.",
  },
  {
    id: "personal-info",
    titleDE: "Persönliche Informationen",
    titleEN: "Personal Information",
    descriptionDE: "Überprüfe deine persönlichen Daten und gib fehlende Informationen an.",
    descriptionEN: "Review your personal data and provide any missing information.",
  },
  {
    id: "income",
    titleDE: "Einkünfte",
    titleEN: "Income",
    descriptionDE: "Gib deine Einkünfte aus nichtselbständiger Arbeit und andere Einkommensquellen an.",
    descriptionEN: "Enter your income from employment and other sources.",
  },
  {
    id: "expenses",
    titleDE: "Werbungskosten",
    titleEN: "Work-Related Expenses",
    descriptionDE: "Gib deine berufsbedingten Ausgaben an, um deine Steuerlast zu reduzieren.",
    descriptionEN: "Enter your work-related expenses to reduce your tax burden.",
  },
  {
    id: "special-expenses",
    titleDE: "Sonderausgaben",
    titleEN: "Special Expenses",
    descriptionDE: "Gib deine Sonderausgaben wie Spenden oder Kirchenbeiträge an.",
    descriptionEN: "Enter your special expenses such as donations or church contributions.",
  },
  {
    id: "extraordinary-burdens",
    titleDE: "Außergewöhnliche Belastungen",
    titleEN: "Extraordinary Burdens",
    descriptionDE: "Gib außergewöhnliche Belastungen wie Krankheitskosten an.",
    descriptionEN: "Enter extraordinary burdens such as medical expenses.",
  },
  {
    id: "tax-credits",
    titleDE: "Absetzbeträge",
    titleEN: "Tax Credits",
    descriptionDE: "Überprüfe und beantrage zusätzliche Absetzbeträge.",
    descriptionEN: "Review and apply for additional tax credits.",
  },
  {
    id: "summary",
    titleDE: "Zusammenfassung",
    titleEN: "Summary",
    descriptionDE: "Überprüfe deine Angaben und reiche deinen Steuerausgleich ein.",
    descriptionEN: "Review your information and submit your tax return.",
  },
];

export function TaxReturnAssistant() {
  const { language } = useLanguage();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [showAIHelp, setShowAIHelp] = useState(false);

  const currentStep = steps[currentStepIndex];
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  const nextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
      setShowAIHelp(false);
    }
  };

  const prevStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
      setShowAIHelp(false);
    }
  };

  const toggleAIHelp = () => {
    setShowAIHelp(!showAIHelp);
  };

  return (
    <div className="space-y-6">
      {/* Fortschrittsanzeige */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>
            {language === "de" ? "Schritt" : "Step"} {currentStepIndex + 1} / {steps.length}
          </span>
          <span>
            {Math.round(progress)}%
          </span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Hauptinhalt */}
      <Card>
        <CardHeader>
          <CardTitle>
            {language === "de" ? currentStep.titleDE : currentStep.titleEN}
          </CardTitle>
          <CardDescription>
            {language === "de" ? currentStep.descriptionDE : currentStep.descriptionEN}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Hier wird der spezifische Inhalt für jeden Schritt angezeigt */}
          {currentStep.id === "intro" && (
            <div className="space-y-4">
              <p className="text-muted-foreground">
                {language === "de"
                  ? "Der Steuerausgleich-Assistent hilft dir dabei, deinen Steuerausgleich schnell und einfach zu erledigen. Im Gegensatz zu FinanzOnline bieten wir klare Erklärungen und Hilfestellungen bei jedem Schritt."
                  : "The Tax Return Assistant helps you complete your tax return quickly and easily. Unlike FinanzOnline, we provide clear explanations and assistance at each step."}
              </p>
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md">
                <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">
                  {language === "de" ? "Vorteile des Assistenten:" : "Benefits of the Assistant:"}
                </h4>
                <ul className="list-disc pl-5 space-y-1 text-blue-600 dark:text-blue-300">
                  <li>
                    {language === "de"
                      ? "Schritt-für-Schritt Anleitung mit verständlichen Erklärungen"
                      : "Step-by-step guidance with understandable explanations"}
                  </li>
                  <li>
                    {language === "de"
                      ? "KI-Unterstützung bei Fragen und Unklarheiten"
                      : "AI support for questions and clarifications"}
                  </li>
                  <li>
                    {language === "de"
                      ? "Tipps zur Steueroptimierung während des gesamten Prozesses"
                      : "Tax optimization tips throughout the process"}
                  </li>
                  <li>
                    {language === "de"
                      ? "Genaue Anweisungen, wo Informationen in FinanzOnline einzutragen sind"
                      : "Precise instructions on where to enter information in FinanzOnline"}
                  </li>
                </ul>
              </div>
            </div>
          )}

          {/* Persönliche Informationen */}
          {currentStep.id === "personal-info" && <PersonalInfoStep />}

          {/* Einkünfte */}
          {currentStep.id === "income" && <IncomeStep />}

          {/* Werbungskosten */}
          {currentStep.id === "expenses" && <ExpensesStep />}

          {/* Sonderausgaben */}
          {currentStep.id === "special-expenses" && <SpecialExpensesStep />}

          {/* Außergewöhnliche Belastungen */}
          {currentStep.id === "extraordinary-burdens" && <ExtraordinaryBurdensStep />}

          {/* Absetzbeträge */}
          {currentStep.id === "tax-credits" && <TaxCreditsStep />}

          {/* Zusammenfassung */}
          {currentStep.id === "summary" && <SummaryStep />}
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStepIndex === 0}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {language === "de" ? "Zurück" : "Back"}
          </Button>

          <Button
            variant="outline"
            onClick={toggleAIHelp}
            className="text-blue-600 border-blue-200 hover:bg-blue-50 hover:text-blue-700 dark:text-blue-400 dark:border-blue-900 dark:hover:bg-blue-900/20"
          >
            <HelpCircle className="mr-2 h-4 w-4" />
            {language === "de" ? "KI-Hilfe" : "AI Help"}
          </Button>

          <Button
            onClick={nextStep}
            disabled={currentStepIndex === steps.length - 1}
          >
            {currentStepIndex === steps.length - 1
              ? (language === "de" ? "Fertigstellen" : "Complete")
              : (language === "de" ? "Weiter" : "Next")}
            {currentStepIndex === steps.length - 1
              ? <CheckCircle className="ml-2 h-4 w-4" />
              : <ArrowRight className="ml-2 h-4 w-4" />}
          </Button>
        </CardFooter>
      </Card>

      {/* KI-Hilfe Panel */}
      {showAIHelp && (
        <Card className="border-blue-200 dark:border-blue-900">
          <CardHeader className="bg-blue-50 dark:bg-blue-900/20 border-b border-blue-100 dark:border-blue-900">
            <CardTitle className="text-blue-700 dark:text-blue-300 text-lg">
              {language === "de" ? "KI-Assistent" : "AI Assistant"}
            </CardTitle>
            <CardDescription className="text-blue-600 dark:text-blue-400">
              {language === "de"
                ? "Hier sind einige Hilfestellungen zum aktuellen Schritt"
                : "Here are some tips for the current step"}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            {currentStep.id === "intro" && (
              <div className="space-y-3">
                <p>
                  {language === "de"
                    ? "Der Steuerausgleich (Arbeitnehmerveranlagung) kann dir helfen, zu viel bezahlte Steuern zurückzubekommen."
                    : "The tax return (Arbeitnehmerveranlagung) can help you get back taxes you've overpaid."}
                </p>
                <p>
                  {language === "de"
                    ? "Für den Steuerausgleich benötigst du deinen Jahreslohnzettel (L16) und Belege für absetzbare Ausgaben."
                    : "For the tax return, you'll need your annual pay slip (L16) and receipts for deductible expenses."}
                </p>
                <p>
                  {language === "de"
                    ? "Tipp: Sammle alle relevanten Unterlagen, bevor du beginnst, um den Prozess zu beschleunigen."
                    : "Tip: Gather all relevant documents before you start to speed up the process."}
                </p>
              </div>
            )}

            {/* KI-Hilfe für Persönliche Informationen */}
            {currentStep.id === "personal-info" && (
              <div className="space-y-3">
                <p>
                  {language === "de"
                    ? "Deine persönlichen Daten sind wichtig für die korrekte Berechnung deiner Steuern."
                    : "Your personal data is important for the correct calculation of your taxes."}
                </p>
                <p>
                  {language === "de"
                    ? "Tipp: Wenn du Kinder hast, kannst du den Familienbonus Plus beantragen. Dieser beträgt bis zu 2.000€ pro Kind und Jahr."
                    : "Tip: If you have children, you can apply for the Family Bonus Plus. This amounts to up to €2,000 per child per year."}
                </p>
                <p>
                  {language === "de"
                    ? "Bei einer Behinderung ab 25% kannst du zusätzliche Freibeträge geltend machen."
                    : "With a disability of 25% or more, you can claim additional tax allowances."}
                </p>
              </div>
            )}

            {/* KI-Hilfe für Einkünfte */}
            {currentStep.id === "income" && (
              <div className="space-y-3">
                <p>
                  {language === "de"
                    ? "Die Angaben zu deinen Einkünften findest du auf deinem Jahreslohnzettel (L16), den du von deinem Arbeitgeber erhältst."
                    : "You can find information about your income on your annual pay slip (L16), which you receive from your employer."}
                </p>
                <p>
                  {language === "de"
                    ? "Tipp: Wenn du mehrere Arbeitgeber hattest, ist es wichtig, alle Lohnzettel anzugeben, da sonst Nachzahlungen drohen können."
                    : "Tip: If you had multiple employers, it's important to include all pay slips, as otherwise you may face additional payments."}
                </p>
                <p>
                  {language === "de"
                    ? "Die einbehaltene Lohnsteuer wird dir bei der Arbeitnehmerveranlagung gutgeschrieben und kann zu einer Rückerstattung führen."
                    : "The withheld income tax will be credited to you in the tax return and can lead to a refund."}
                </p>
              </div>
            )}

            {/* KI-Hilfe für Werbungskosten */}
            {currentStep.id === "expenses" && (
              <div className="space-y-3">
                <p>
                  {language === "de"
                    ? "Werbungskosten sind Ausgaben, die im Zusammenhang mit deiner beruflichen Tätigkeit stehen und deine Steuerlast reduzieren können."
                    : "Work-related expenses are expenses related to your professional activity and can reduce your tax burden."}
                </p>
                <p>
                  {language === "de"
                    ? "Tipp: Das Pendlerpauschale kannst du auch dann geltend machen, wenn du ein Firmenauto nutzt oder Fahrgemeinschaften bildest."
                    : "Tip: You can claim the commuter allowance even if you use a company car or form carpools."}
                </p>
                <p>
                  {language === "de"
                    ? "Für Homeoffice-Tage kannst du seit 2020 eine Pauschale von 3€ pro Tag (max. 300€ pro Jahr) geltend machen."
                    : "For home office days, you can claim a flat rate of €3 per day (max. €300 per year) since 2020."}
                </p>
              </div>
            )}

            {/* KI-Hilfe für Sonderausgaben */}
            {currentStep.id === "special-expenses" && (
              <div className="space-y-3">
                <p>
                  {language === "de"
                    ? "Viele Sonderausgaben wie Spenden und Kirchenbeiträge werden automatisch an das Finanzamt übermittelt."
                    : "Many special expenses such as donations and church contributions are automatically transmitted to the tax office."}
                </p>
                <p>
                  {language === "de"
                    ? "Tipp: Überprüfe die automatisch übermittelten Daten auf Vollständigkeit. Fehlende Spenden kannst du manuell nachtragen."
                    : "Tip: Check the automatically transmitted data for completeness. You can manually add missing donations."}
                </p>
                <p>
                  {language === "de"
                    ? "Spenden sind bis zu 10% deines Jahreseinkommens absetzbar. Kirchenbeiträge bis zu 400€ pro Jahr."
                    : "Donations are deductible up to 10% of your annual income. Church contributions up to €400 per year."}
                </p>
              </div>
            )}

            {/* KI-Hilfe für Außergewöhnliche Belastungen */}
            {currentStep.id === "extraordinary-burdens" && (
              <div className="space-y-3">
                <p>
                  {language === "de"
                    ? "Außergewöhnliche Belastungen werden nur berücksichtigt, wenn sie den Selbstbehalt übersteigen. Dieser hängt von deinem Einkommen ab."
                    : "Extraordinary burdens are only considered if they exceed the deductible. This depends on your income."}
                </p>
                <p>
                  {language === "de"
                    ? "Tipp: Krankheitskosten, die nicht von der Versicherung übernommen werden, können als außergewöhnliche Belastung geltend gemacht werden."
                    : "Tip: Medical expenses not covered by insurance can be claimed as extraordinary burdens."}
                </p>
                <p>
                  {language === "de"
                    ? "Bei einer Behinderung entfällt der Selbstbehalt für behinderungsbedingte Ausgaben."
                    : "With a disability, there is no deductible for disability-related expenses."}
                </p>
              </div>
            )}

            {/* KI-Hilfe für Absetzbeträge */}
            {currentStep.id === "tax-credits" && (
              <div className="space-y-3">
                <p>
                  {language === "de"
                    ? "Absetzbeträge reduzieren deine Steuerlast direkt und nicht nur dein zu versteuerndes Einkommen."
                    : "Tax credits directly reduce your tax burden, not just your taxable income."}
                </p>
                <p>
                  {language === "de"
                    ? "Tipp: Der Familienbonus Plus ist mit bis zu 2.000€ pro Kind und Jahr der höchste Absetzbetrag und sollte unbedingt beantragt werden."
                    : "Tip: The Family Bonus Plus is the highest tax credit at up to €2,000 per child per year and should definitely be applied for."}
                </p>
                <p>
                  {language === "de"
                    ? "Der Verkehrsabsetzbetrag (400€) wird automatisch berücksichtigt und muss nicht extra beantragt werden."
                    : "The traffic tax credit (€400) is automatically considered and does not need to be applied for separately."}
                </p>
              </div>
            )}

            {/* KI-Hilfe für Zusammenfassung */}
            {currentStep.id === "summary" && (
              <div className="space-y-3">
                <p>
                  {language === "de"
                    ? "Überprüfe alle Angaben sorgfältig, bevor du deine Arbeitnehmerveranlagung einreichst."
                    : "Check all information carefully before submitting your tax return."}
                </p>
                <p>
                  {language === "de"
                    ? "Tipp: Bewahre alle Belege für 7 Jahre auf. Das Finanzamt kann diese jederzeit anfordern."
                    : "Tip: Keep all receipts for 7 years. The tax office may request them at any time."}
                </p>
                <p>
                  {language === "de"
                    ? "Nach der Einreichung wird deine Arbeitnehmerveranlagung vom Finanzamt bearbeitet. Dies kann einige Wochen dauern."
                    : "After submission, your tax return will be processed by the tax office. This may take a few weeks."}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
