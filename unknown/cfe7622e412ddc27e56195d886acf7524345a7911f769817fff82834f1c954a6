import { Suspense } from "react";
import { DashboardBlocks } from "../components/DashboardBlocks";
import { EmptyState } from "../components/EmptyState";
import { InvoiceGraph } from "../components/InvoiceGraph";
import { RecentInvoices } from "../components/RecentInvoices";
import { signOut } from "../utils/auth";
import prisma from "../utils/db";
import { requireUser } from "../utils/hooks";
import { Skeleton } from "@/components/ui/skeleton";
import { DashboardStats } from '../components/DashboardStats';

async function getData(userId: string) {
  const data = await prisma.invoice.findMany({
    where: {
      userId: userId,
    },
    select: {
      id: true,
    },
  });

  return data;
}

export default async function DashboardPage() {
  const session = await requireUser();
  const data = await getData(session.user?.id as string);

  // Fetch invoice data for the user
  const invoices = await prisma.invoice.findMany({
    where: {
      userId: session.user?.id as string,
    },
  });

  // Calculate statistics
  const totalRevenue = invoices.reduce((acc, invoice) => {
    return acc + invoice.total;
  }, 0);

  const paidInvoices = invoices.filter(invoice => invoice.status === 'PAID').length;
  const pendingInvoices = invoices.filter(invoice => invoice.status === 'PENDING').length;

  return (
    <>
      {data.length < 1 ? (
        <EmptyState
          title="No invoices found"
          description="Create an invoice to see it right here"
          buttontext="Create Invoice"
          href="/dashboard/invoices/create"
        />
      ) : (
        <Suspense fallback={<Skeleton className="w-full h-full flex-1" />}>
          <DashboardBlocks />
          <div className="grid gap-4 lg:grid-cols-3 md:gap-8">
            <InvoiceGraph />
            <RecentInvoices />
          </div>
        </Suspense>
      )}
    </>
  );
}
