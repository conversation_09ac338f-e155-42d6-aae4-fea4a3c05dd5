import { NextRequest, NextResponse } from "next/server";
import { verifyEmail } from "@/app/actions/auth";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const token = searchParams.get("token");

  if (!token) {
    return NextResponse.redirect(new URL("/weg-accounting/login?error=missing-token", request.url));
  }

  const result = await verifyEmail(token);

  if (result.error) {
    return NextResponse.redirect(new URL(`/weg-accounting/login?error=${encodeURIComponent(result.error)}`, request.url));
  }

  // Erfolgreiche Verifizierung, Weiterleitung zur Login-Seite mit Erfolgsmeldung
  return NextResponse.redirect(new URL("/weg-accounting/login?verified=true", request.url));
}
