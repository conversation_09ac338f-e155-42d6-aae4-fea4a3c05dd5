"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Building, Calendar, Download, Edit, Eye, FileText, Receipt, Trash2, Upload } from "lucide-react";
import { Property } from "../properties/PropertyList";
import { ExpenseCategory } from "../accounting/AccountingList";
import { Invoice, InvoiceStatus } from "./InvoiceList";

interface InvoiceDetailProps {
  invoice: Invoice;
  properties: Property[];
  expenseCategories: ExpenseCategory[];
  onBack: () => void;
  onUpdate: (invoice: Invoice) => void;
  onDelete: (id: string) => void;
}

export function InvoiceDetail({
  invoice,
  properties,
  expenseCategories,
  onBack,
  onUpdate,
  onDelete
}: InvoiceDetailProps) {
  const { language } = useLanguage();

  // State für die Bearbeitung der Rechnung
  const [isEditing, setIsEditing] = useState(false);
  const [editedInvoice, setEditedInvoice] = useState<Invoice>(invoice);

  // Handler für die Aktualisierung der Rechnung
  const handleUpdateInvoice = () => {
    onUpdate(editedInvoice);
    setIsEditing(false);
  };

  // Funktion zum Formatieren eines Betrags als Währung
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "de" ? "de-AT" : "en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  // Funktion zum Formatieren eines Datums
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === "de" ? "de-AT" : "en-US");
  };

  // Funktion zum Übersetzen des Status
  const translateStatus = (status: InvoiceStatus) => {
    switch (status) {
      case "pending":
        return language === "de" ? "Offen" : "Pending";
      case "paid":
        return language === "de" ? "Bezahlt" : "Paid";
      case "cancelled":
        return language === "de" ? "Storniert" : "Cancelled";
      default:
        return status;
    }
  };

  // Funktion zum Abrufen der Statusfarbe
  const getStatusColor = (status: InvoiceStatus) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "paid":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      case "cancelled":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
    }
  };

  // Funktion zum Abrufen des Objektnamens anhand der ID
  const getPropertyName = (propertyId: string) => {
    const property = properties.find(p => p.id === propertyId);
    return property ? property.name : "Unbekanntes Objekt";
  };

  // Funktion zum Abrufen des Kategorienamens anhand der ID
  const getCategoryName = (categoryId: string) => {
    const category = expenseCategories.find(c => c.id === categoryId);
    return category ? category.name : "Unbekannte Kategorie";
  };

  // Funktion zum Berechnen der Tage bis zur Fälligkeit
  const getDaysUntilDue = () => {
    const today = new Date();
    const dueDate = new Date(invoice.dueDate);
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Funktion zum Anzeigen der Fälligkeitsinformation
  const getDueInfo = () => {
    const daysUntilDue = getDaysUntilDue();

    if (invoice.status === "paid" || invoice.status === "cancelled") {
      return null;
    }

    if (daysUntilDue < 0) {
      return (
        <div className="text-red-600 dark:text-red-400 text-sm font-medium">
          {language === "de"
            ? `Überfällig seit ${Math.abs(daysUntilDue)} Tagen`
            : `Overdue by ${Math.abs(daysUntilDue)} days`}
        </div>
      );
    } else if (daysUntilDue === 0) {
      return (
        <div className="text-orange-600 dark:text-orange-400 text-sm font-medium">
          {language === "de" ? "Heute fällig" : "Due today"}
        </div>
      );
    } else if (daysUntilDue <= 7) {
      return (
        <div className="text-orange-600 dark:text-orange-400 text-sm font-medium">
          {language === "de"
            ? `Fällig in ${daysUntilDue} Tagen`
            : `Due in ${daysUntilDue} days`}
        </div>
      );
    } else {
      return (
        <div className="text-green-600 dark:text-green-400 text-sm font-medium">
          {language === "de"
            ? `Fällig in ${daysUntilDue} Tagen`
            : `Due in ${daysUntilDue} days`}
        </div>
      );
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" onClick={onBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-2xl font-bold tracking-tight">
          {language === "de" ? "Rechnung " : "Invoice "} {invoice.number}
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Rechnungsdetails" : "Invoice Details"}
            </CardTitle>
            <CardDescription>
              {getPropertyName(invoice.propertyId)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">
                  {language === "de" ? "Status" : "Status"}
                </span>
                <Badge className={`${getStatusColor(invoice.status)} border-none`}>
                  {translateStatus(invoice.status)}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Rechnungsdatum" : "Invoice Date"}
                </span>
                <span className="font-medium">{formatDate(invoice.date)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Fälligkeitsdatum" : "Due Date"}
                </span>
                <span className="font-medium">{formatDate(invoice.dueDate)}</span>
              </div>
              {getDueInfo()}
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Lieferant" : "Vendor"}
                </span>
                <span className="font-medium">{invoice.vendorName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Kategorie" : "Category"}
                </span>
                <span className="font-medium">{getCategoryName(invoice.categoryId)}</span>
              </div>
              <div className="border-t pt-2 flex justify-between font-medium">
                <span>
                  {language === "de" ? "Betrag" : "Amount"}
                </span>
                <span>{formatCurrency(invoice.amount)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Beschreibung" : "Description"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              {invoice.description || (language === "de" ? "Keine Beschreibung vorhanden" : "No description available")}
            </p>

            {invoice.notes && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-1">
                  {language === "de" ? "Notizen" : "Notes"}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {invoice.notes}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end gap-2">
        {invoice.status === "pending" && (
          <Button
            variant="outline"
            onClick={() => {
              const updatedInvoice = {
                ...invoice,
                status: "paid" as InvoiceStatus,
              };
              onUpdate(updatedInvoice);
            }}
          >
            <FileText className="h-4 w-4 mr-2" />
            {language === "de" ? "Als bezahlt markieren" : "Mark as Paid"}
          </Button>
        )}

        <Button
          variant="outline"
          onClick={() => setIsEditing(true)}
        >
          <Edit className="h-4 w-4 mr-2" />
          {language === "de" ? "Bearbeiten" : "Edit"}
        </Button>

        <Button
          variant="destructive"
          onClick={() => onDelete(invoice.id)}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {language === "de" ? "Löschen" : "Delete"}
        </Button>
      </div>

      <Tabs defaultValue="document" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="document">
            {language === "de" ? "Dokument" : "Document"}
          </TabsTrigger>
          <TabsTrigger value="accounting">
            {language === "de" ? "Buchhaltung" : "Accounting"}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="document" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {language === "de" ? "Rechnungsdokument" : "Invoice Document"}
              </CardTitle>
              <CardDescription>
                {language === "de"
                  ? "Laden Sie das Rechnungsdokument hoch oder sehen Sie es an."
                  : "Upload or view the invoice document."}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {invoice.filePath ? (
                <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-md">
                  <FileText className="h-10 w-10 text-primary mb-2" />
                  <p className="text-sm font-medium mb-2">
                    {invoice.filePath.split('/').pop()}
                  </p>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      {language === "de" ? "Herunterladen" : "Download"}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      {language === "de" ? "Anzeigen" : "View"}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-md">
                  <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground mb-4">
                    {language === "de"
                      ? "Kein Dokument hochgeladen"
                      : "No document uploaded"}
                  </p>
                  <Button variant="outline">
                    <Upload className="h-4 w-4 mr-2" />
                    {language === "de" ? "Dokument hochladen" : "Upload Document"}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="accounting" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {language === "de" ? "Buchhaltungsinformationen" : "Accounting Information"}
              </CardTitle>
              <CardDescription>
                {language === "de"
                  ? "Informationen zur Verbuchung dieser Rechnung."
                  : "Information about the accounting of this invoice."}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium mb-1">
                      {language === "de" ? "Objekt" : "Property"}
                    </h4>
                    <p className="text-sm">{getPropertyName(invoice.propertyId)}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium mb-1">
                      {language === "de" ? "Kategorie" : "Category"}
                    </h4>
                    <p className="text-sm">{getCategoryName(invoice.categoryId)}</p>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-1">
                    {language === "de" ? "Verteilerschlüssel" : "Distribution Key"}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {language === "de"
                      ? "Diese Rechnung wurde noch keiner Abrechnung zugeordnet."
                      : "This invoice has not been assigned to any accounting period yet."}
                  </p>
                </div>

                <div className="pt-2">
                  <Button variant="outline">
                    <Calendar className="h-4 w-4 mr-2" />
                    {language === "de" ? "Zu Abrechnung hinzufügen" : "Add to Accounting Period"}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Bearbeitungsdialog */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {language === "de" ? "Rechnung bearbeiten" : "Edit Invoice"}
            </DialogTitle>
            <DialogDescription>
              {language === "de"
                ? "Bearbeiten Sie die Details der Rechnung."
                : "Edit the details of the invoice."}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-propertyId">
                  {language === "de" ? "Objekt" : "Property"}
                </Label>
                <Select
                  value={editedInvoice.propertyId}
                  onValueChange={(value) => setEditedInvoice({...editedInvoice, propertyId: value})}
                >
                  <SelectTrigger id="edit-propertyId">
                    <SelectValue placeholder={language === "de" ? "Objekt auswählen" : "Select property"} />
                  </SelectTrigger>
                  <SelectContent>
                    {properties.map((property) => (
                      <SelectItem key={property.id} value={property.id}>
                        {property.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-categoryId">
                  {language === "de" ? "Kategorie" : "Category"}
                </Label>
                <Select
                  value={editedInvoice.categoryId}
                  onValueChange={(value) => setEditedInvoice({...editedInvoice, categoryId: value})}
                >
                  <SelectTrigger id="edit-categoryId">
                    <SelectValue placeholder={language === "de" ? "Kategorie auswählen" : "Select category"} />
                  </SelectTrigger>
                  <SelectContent>
                    {expenseCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-number">
                  {language === "de" ? "Rechnungsnummer" : "Invoice Number"}
                </Label>
                <Input
                  id="edit-number"
                  value={editedInvoice.number}
                  onChange={(e) => setEditedInvoice({...editedInvoice, number: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-vendorName">
                  {language === "de" ? "Lieferant" : "Vendor"}
                </Label>
                <Input
                  id="edit-vendorName"
                  value={editedInvoice.vendorName}
                  onChange={(e) => setEditedInvoice({...editedInvoice, vendorName: e.target.value})}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-date">
                  {language === "de" ? "Rechnungsdatum" : "Invoice Date"}
                </Label>
                <Input
                  id="edit-date"
                  type="date"
                  value={editedInvoice.date}
                  onChange={(e) => setEditedInvoice({...editedInvoice, date: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-dueDate">
                  {language === "de" ? "Fälligkeitsdatum" : "Due Date"}
                </Label>
                <Input
                  id="edit-dueDate"
                  type="date"
                  value={editedInvoice.dueDate}
                  onChange={(e) => setEditedInvoice({...editedInvoice, dueDate: e.target.value})}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">
                {language === "de" ? "Beschreibung" : "Description"}
              </Label>
              <Input
                id="edit-description"
                value={editedInvoice.description}
                onChange={(e) => setEditedInvoice({...editedInvoice, description: e.target.value})}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-amount">
                  {language === "de" ? "Betrag (€)" : "Amount (€)"}
                </Label>
                <Input
                  id="edit-amount"
                  type="number"
                  step="0.01"
                  value={editedInvoice.amount}
                  onChange={(e) => setEditedInvoice({...editedInvoice, amount: parseFloat(e.target.value) || 0})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-status">
                  {language === "de" ? "Status" : "Status"}
                </Label>
                <Select
                  value={editedInvoice.status}
                  onValueChange={(value: InvoiceStatus) => setEditedInvoice({...editedInvoice, status: value})}
                >
                  <SelectTrigger id="edit-status">
                    <SelectValue placeholder={language === "de" ? "Status auswählen" : "Select status"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">
                      {language === "de" ? "Offen" : "Pending"}
                    </SelectItem>
                    <SelectItem value="paid">
                      {language === "de" ? "Bezahlt" : "Paid"}
                    </SelectItem>
                    <SelectItem value="cancelled">
                      {language === "de" ? "Storniert" : "Cancelled"}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-notes">
                {language === "de" ? "Notizen" : "Notes"}
              </Label>
              <Textarea
                id="edit-notes"
                value={editedInvoice.notes || ""}
                onChange={(e) => setEditedInvoice({...editedInvoice, notes: e.target.value})}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button onClick={handleUpdateInvoice}>
              {language === "de" ? "Speichern" : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
