{"employees": "Employees", "addEmployee": "Add Employee", "addNewEmployee": "Add New Employee", "fillEmployeeDetails": "Fill in the details to add a new employee", "employeeName": "Employee Name", "email": "Email", "position": "Position", "department": "Department", "selectDepartment": "Select department", "startDate": "Start Date", "salary": "Salary", "cancel": "Cancel", "adding": "Adding...", "employeeList": "Employee List", "noEmployees": "No employees found", "name": "Name", "actions": "Actions", "employeeAdded": "Employee added", "employeeAddedSuccess": "The employee has been added successfully", "error": "Error", "employeeAddFailed": "Failed to add employee. Please try again.", "editEmployee": "Edit Employee", "editEmployeeDetails": "Edit Employee Details", "updateEmployeeInfo": "Update employee information", "updateEmployee": "Update Employee", "updating": "Updating...", "employeeUpdated": "Employee updated", "employeeUpdatedSuccess": "The employee has been updated successfully", "employeeUpdateFailed": "Failed to update employee. Please try again.", "deleteEmployee": "Delete Employee", "deleting": "Deleting...", "confirmDeleteEmployee": "Are you sure you want to delete this employee?", "employeeDeleted": "Employee deleted", "employeeDeletedSuccess": "The employee has been deleted successfully", "employeeDeleteFailed": "Failed to delete employee. Please try again."}