"use client";

import { useState, useEffect } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Advisor } from "@/app/lib/wegTypes";
import { toast } from "sonner";

interface EditAdvisorDialogProps {
  advisor: Advisor | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (updatedAdvisor: Advisor) => void;
  onRefreshData?: () => void;
}

export function EditAdvisorDialog({ advisor, isOpen, onClose, onUpdate, onRefreshData }: EditAdvisorDialogProps) {
  const { language } = useLanguage();
  const [editedAdvisor, setEditedAdvisor] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    notes: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  // Update form when advisor changes
  useEffect(() => {
    if (advisor) {
      setEditedAdvisor({
        name: advisor.name,
        email: advisor.email,
        phone: advisor.phone || "",
        address: advisor.address || "",
        notes: advisor.notes || "",
      });
    }
  }, [advisor]);

  const handleSave = async () => {
    if (!advisor) return;

    try {
      if (!editedAdvisor.name || !editedAdvisor.email) {
        toast.error(
          language === "de"
            ? "Name und E-Mail sind erforderlich"
            : "Name and email are required"
        );
        return;
      }

      setIsLoading(true);

      // Call API to update advisor
      const response = await fetch(`/api/weg-accounting/advisors/${advisor.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: editedAdvisor.name,
          email: editedAdvisor.email,
          phone: editedAdvisor.phone || null,
          company: editedAdvisor.address || null,
          notes: editedAdvisor.notes || null,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update advisor");
      }

      const updatedDbAdvisor = await response.json();

      // Convert to wegTypes Advisor format
      const updatedAdvisor: Advisor = {
        ...advisor,
        name: updatedDbAdvisor.name,
        email: updatedDbAdvisor.email,
        phone: updatedDbAdvisor.phone || undefined,
        address: updatedDbAdvisor.company || undefined,
        notes: updatedDbAdvisor.notes || undefined,
      };

      // Update local state first for immediate feedback
      onUpdate(updatedAdvisor);
      onClose();

      toast.success(
        language === "de"
          ? "Berater erfolgreich aktualisiert"
          : "Advisor updated successfully"
      );

      // Refresh data from server after a short delay to ensure consistency
      if (onRefreshData) {
        setTimeout(() => {
          onRefreshData();
        }, 50);
      }
    } catch (error) {
      console.error("Failed to update advisor:", error);
      toast.error(
        language === "de"
          ? "Fehler beim Aktualisieren des Beraters"
          : "Failed to update advisor"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (advisor) {
      setEditedAdvisor({
        name: advisor.name,
        email: advisor.email,
        phone: advisor.phone || "",
        address: advisor.address || "",
        notes: advisor.notes || "",
      });
    }
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {language === "de" ? "Berater bearbeiten" : "Edit Advisor"}
          </DialogTitle>
          <DialogDescription>
            {language === "de"
              ? "Bearbeiten Sie die Kontaktinformationen des Beraters"
              : "Edit the advisor's contact information"}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="edit-name" className="text-right">
              {language === "de" ? "Name" : "Name"}
            </Label>
            <Input
              id="edit-name"
              value={editedAdvisor.name}
              onChange={(e) => setEditedAdvisor({ ...editedAdvisor, name: e.target.value })}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="edit-email" className="text-right">
              {language === "de" ? "E-Mail" : "Email"}
            </Label>
            <Input
              id="edit-email"
              type="email"
              value={editedAdvisor.email}
              onChange={(e) => setEditedAdvisor({ ...editedAdvisor, email: e.target.value })}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="edit-phone" className="text-right">
              {language === "de" ? "Telefon" : "Phone"}
            </Label>
            <Input
              id="edit-phone"
              value={editedAdvisor.phone}
              onChange={(e) => setEditedAdvisor({ ...editedAdvisor, phone: e.target.value })}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="edit-address" className="text-right">
              {language === "de" ? "Adresse/Unternehmen" : "Address/Company"}
            </Label>
            <Input
              id="edit-address"
              value={editedAdvisor.address}
              onChange={(e) => setEditedAdvisor({ ...editedAdvisor, address: e.target.value })}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="edit-notes" className="text-right">
              {language === "de" ? "Notizen" : "Notes"}
            </Label>
            <Textarea
              id="edit-notes"
              value={editedAdvisor.notes}
              onChange={(e) => setEditedAdvisor({ ...editedAdvisor, notes: e.target.value })}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={isLoading}>
            {language === "de" ? "Abbrechen" : "Cancel"}
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading
              ? (language === "de" ? "Speichern..." : "Saving...")
              : (language === "de" ? "Speichern" : "Save")
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
