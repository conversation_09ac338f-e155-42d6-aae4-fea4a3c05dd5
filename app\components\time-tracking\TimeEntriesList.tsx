"use client";

import { useState, useEffect } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Search,
  CalendarIcon,
  MoreHorizontal,
  Edit,
  Trash2,
  Play,
  Pause,
  Clock,
  Filter
} from "lucide-react";
import { format, isToday, isYesterday, isThisWeek, isThisMonth } from "date-fns";
import { de } from "date-fns/locale";
import { TimeEntryForm } from "./TimeEntryForm";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/app/context/ToastContext";

// Mock data for time entries
const mockTimeEntries = [
  {
    id: "1",
    description: "Website Development",
    date: new Date(),
    startTime: new Date(new Date().setHours(9, 0, 0, 0)),
    endTime: new Date(new Date().setHours(12, 30, 0, 0)),
    duration: 210, // 3.5 hours in minutes
    projectId: "1",
    projectName: "Client Website",
    projectColor: "#4f46e5",
    employeeId: "",
    employeeName: "",
    isBillable: true
  },
  {
    id: "2",
    description: "Meeting with Client",
    date: new Date(),
    startTime: new Date(new Date().setHours(14, 0, 0, 0)),
    endTime: new Date(new Date().setHours(15, 0, 0, 0)),
    duration: 60, // 1 hour in minutes
    projectId: "1",
    projectName: "Client Website",
    projectColor: "#4f46e5",
    employeeId: "",
    employeeName: "",
    isBillable: true
  },
  {
    id: "3",
    description: "Documentation",
    date: new Date(new Date().setDate(new Date().getDate() - 1)), // Yesterday
    startTime: new Date(new Date().setDate(new Date().getDate() - 1)).setHours(10, 0, 0, 0),
    endTime: new Date(new Date().setDate(new Date().getDate() - 1)).setHours(12, 0, 0, 0),
    duration: 120, // 2 hours in minutes
    projectId: "2",
    projectName: "Internal Project",
    projectColor: "#10b981",
    employeeId: "",
    employeeName: "",
    isBillable: false
  },
  {
    id: "4",
    description: "Design Review",
    date: new Date(new Date().setDate(new Date().getDate() - 2)),
    startTime: new Date(new Date().setDate(new Date().getDate() - 2)).setHours(13, 0, 0, 0),
    endTime: new Date(new Date().setDate(new Date().getDate() - 2)).setHours(15, 30, 0, 0),
    duration: 150, // 2.5 hours in minutes
    projectId: "3",
    projectName: "Marketing Campaign",
    projectColor: "#f59e0b",
    employeeId: "1",
    employeeName: "Max Mustermann",
    isBillable: true
  }
];

export function TimeEntriesList() {
  const { language, t } = useLanguage();
  const { toast } = useToast();
  const [timeEntries, setTimeEntries] = useState<any[]>([]);
  const [filteredEntries, setFilteredEntries] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);
  const [projectFilter, setProjectFilter] = useState("all");
  const [employeeFilter, setEmployeeFilter] = useState("all");
  const [projects, setProjects] = useState<any[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [entryToEdit, setEntryToEdit] = useState<any | null>(null);
  const [activeTimer, setActiveTimer] = useState<string | null>(null);
  const [timerStartTime, setTimerStartTime] = useState<Date | null>(null);
  const [timerElapsed, setTimerElapsed] = useState(0);
  const [timerInterval, setTimerIntervalId] = useState<NodeJS.Timeout | null>(null);

  // Format date based on language
  const formatDate = (date: Date) => {
    return format(
      date,
      "PPP",
      { locale: language === "de" ? de : undefined }
    );
  };

  // Format time
  const formatTime = (date: Date) => {
    return format(date, "HH:mm");
  };

  // Format duration
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}:${mins.toString().padStart(2, '0')}`;
  };

  // Get relative date label
  const getDateLabel = (date: Date) => {
    if (isToday(date)) {
      return language === "de" ? "Heute" : "Today";
    } else if (isYesterday(date)) {
      return language === "de" ? "Gestern" : "Yesterday";
    } else if (isThisWeek(date)) {
      return format(date, language === "de" ? "EEEE" : "EEEE", { locale: language === "de" ? de : undefined });
    } else {
      return formatDate(date);
    }
  };

  // Group entries by date
  const groupEntriesByDate = (entries: any[]) => {
    const grouped: Record<string, any[]> = {};

    entries.forEach(entry => {
      const dateKey = format(new Date(entry.date), "yyyy-MM-dd");
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(entry);
    });

    // Sort each group by start time
    Object.keys(grouped).forEach(date => {
      grouped[date].sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
    });

    // Sort dates in descending order (newest first)
    return Object.keys(grouped)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
      .map(date => ({
        date: new Date(date),
        entries: grouped[date]
      }));
  };

  // Load data
  useEffect(() => {
    // Simulate loading data from API
    setTimeout(() => {
      setTimeEntries(mockTimeEntries);

      // Extract unique projects and employees
      const uniqueProjects = Array.from(
        new Set(mockTimeEntries.map(entry => entry.projectId))
      ).map(id => {
        const entry = mockTimeEntries.find(e => e.projectId === id);
        return {
          id,
          name: entry?.projectName || "",
          color: entry?.projectColor || "#000000"
        };
      });

      const uniqueEmployees = Array.from(
        new Set(mockTimeEntries.filter(e => e.employeeId).map(entry => entry.employeeId))
      ).map(id => {
        const entry = mockTimeEntries.find(e => e.employeeId === id);
        return {
          id,
          name: entry?.employeeName || ""
        };
      });

      setProjects(uniqueProjects);
      setEmployees(uniqueEmployees);
    }, 500);
  }, []);

  // Apply filters
  useEffect(() => {
    let filtered = [...timeEntries];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(entry =>
        entry.description.toLowerCase().includes(query) ||
        entry.projectName.toLowerCase().includes(query) ||
        (entry.employeeName && entry.employeeName.toLowerCase().includes(query))
      );
    }

    // Apply date filter
    if (dateFilter) {
      const filterDate = format(dateFilter, "yyyy-MM-dd");
      filtered = filtered.filter(entry =>
        format(new Date(entry.date), "yyyy-MM-dd") === filterDate
      );
    }

    // Apply project filter
    if (projectFilter && projectFilter !== "all") {
      filtered = filtered.filter(entry => entry.projectId === projectFilter);
    }

    // Apply employee filter
    if (employeeFilter) {
      if (employeeFilter === "self") {
        filtered = filtered.filter(entry => !entry.employeeId);
      } else if (employeeFilter !== "all") {
        filtered = filtered.filter(entry => entry.employeeId === employeeFilter);
      }
    }

    setFilteredEntries(filtered);
  }, [timeEntries, searchQuery, dateFilter, projectFilter, employeeFilter]);

  // Handle timer
  useEffect(() => {
    if (activeTimer && timerStartTime) {
      const interval = setInterval(() => {
        const now = new Date();
        const elapsed = Math.floor((now.getTime() - timerStartTime.getTime()) / 1000);
        setTimerElapsed(elapsed);
      }, 1000);

      setTimerIntervalId(interval);

      return () => {
        clearInterval(interval);
      };
    }
  }, [activeTimer, timerStartTime]);

  // Format timer display
  const formatTimerDisplay = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Start timer
  const startTimer = (description: string = "") => {
    if (activeTimer) {
      stopTimer();
    }

    const now = new Date();
    setActiveTimer(description || (language === "de" ? "Neue Zeiterfassung" : "New time tracking"));
    setTimerStartTime(now);
    setTimerElapsed(0);
  };

  // Stop timer and create entry
  const stopTimer = () => {
    if (activeTimer && timerStartTime) {
      const now = new Date();
      const durationMinutes = Math.floor((now.getTime() - timerStartTime.getTime()) / 60000);

      // Create new entry
      const newEntry = {
        id: (timeEntries.length + 1).toString(),
        description: activeTimer,
        date: new Date(),
        startTime: timerStartTime,
        endTime: now,
        duration: durationMinutes,
        projectId: "",
        projectName: "",
        projectColor: "",
        employeeId: "",
        employeeName: "",
        isBillable: true
      };

      // Add to entries
      setTimeEntries(prev => [newEntry, ...prev]);

      // Clear timer
      setActiveTimer(null);
      setTimerStartTime(null);
      setTimerElapsed(0);

      if (timerInterval) {
        clearInterval(timerInterval);
        setTimerIntervalId(null);
      }

      // Show success message
      toast.success(
        language === "de" ? "Zeiteintrag erstellt" : "Time entry created",
        { description: language === "de" ? "Der Zeiteintrag wurde erfolgreich erstellt." : "The time entry was successfully created." }
      );

      // Open edit dialog to add more details
      setEntryToEdit(newEntry);
      setIsEditDialogOpen(true);
    }
  };

  // Edit entry
  const handleEditEntry = (entry: any) => {
    setEntryToEdit(entry);
    setIsEditDialogOpen(true);
  };

  // Delete entry
  const handleDeleteEntry = (entryId: string) => {
    // Confirm deletion
    if (confirm(language === "de" ? "Möchten Sie diesen Zeiteintrag wirklich löschen?" : "Are you sure you want to delete this time entry?")) {
      setTimeEntries(prev => prev.filter(entry => entry.id !== entryId));

      toast.success(
        language === "de" ? "Zeiteintrag gelöscht" : "Time entry deleted",
        { description: language === "de" ? "Der Zeiteintrag wurde erfolgreich gelöscht." : "The time entry was successfully deleted." }
      );
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery("");
    setDateFilter(undefined);
    setProjectFilter("all");
    setEmployeeFilter("all");
  };

  // Group entries by date
  const groupedEntries = groupEntriesByDate(filteredEntries);

  return (
    <div className="space-y-6">
      {/* Active Timer */}
      {activeTimer && (
        <Card className="border-2 border-primary">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">{activeTimer}</h3>
                <div className="text-2xl font-mono mt-1">
                  {formatTimerDisplay(timerElapsed)}
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => stopTimer()}
                >
                  <Pause className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder={language === "de" ? "Suchen..." : "Search..."}
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {/* Date Filter */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4" />
                    {dateFilter ? formatDate(dateFilter) : (language === "de" ? "Datum" : "Date")}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dateFilter}
                    onSelect={setDateFilter}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>

              {/* Project Filter */}
              <Select value={projectFilter} onValueChange={setProjectFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder={language === "de" ? "Projekt" : "Project"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {language === "de" ? "Alle Projekte" : "All Projects"}
                  </SelectItem>
                  {projects.map(project => (
                    <SelectItem key={project.id} value={project.id}>
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: project.color }}
                        />
                        {project.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Employee Filter */}
              <Select value={employeeFilter} onValueChange={setEmployeeFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder={language === "de" ? "Mitarbeiter" : "Employee"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {language === "de" ? "Alle Mitarbeiter" : "All Employees"}
                  </SelectItem>
                  <SelectItem value="self">
                    {language === "de" ? "Ich selbst" : "Myself"}
                  </SelectItem>
                  {employees.map(employee => (
                    <SelectItem key={employee.id} value={employee.id}>
                      {employee.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Clear Filters */}
              {(searchQuery || dateFilter || projectFilter || employeeFilter) && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={clearFilters}
                  title={language === "de" ? "Filter zurücksetzen" : "Clear filters"}
                >
                  <Filter className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Timer Start */}
      {!activeTimer && (
        <Card className="border border-dashed">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  placeholder={language === "de" ? "Was machen Sie gerade?" : "What are you working on?"}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      startTimer(e.currentTarget.value);
                      e.currentTarget.value = '';
                    }
                  }}
                />
              </div>
              <Button
                variant="default"
                className="flex items-center gap-2"
                onClick={() => startTimer()}
              >
                <Play className="h-4 w-4" />
                <span>{language === "de" ? "Starten" : "Start"}</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Time Entries List */}
      {groupedEntries.length > 0 ? (
        <div className="space-y-6">
          {groupedEntries.map(group => (
            <div key={group.date.toISOString()} className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  {getDateLabel(group.date)}
                </h3>
                <div className="text-sm text-muted-foreground">
                  {language === "de" ? "Gesamt" : "Total"}: {
                    formatDuration(
                      group.entries.reduce((total, entry) => total + (entry.duration || 0), 0)
                    )
                  }
                </div>
              </div>

              <Card>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[40%]">
                        {language === "de" ? "Beschreibung" : "Description"}
                      </TableHead>
                      <TableHead className="w-[15%]">
                        {language === "de" ? "Projekt" : "Project"}
                      </TableHead>
                      <TableHead className="w-[15%]">
                        {language === "de" ? "Zeit" : "Time"}
                      </TableHead>
                      <TableHead className="w-[15%]">
                        {language === "de" ? "Dauer" : "Duration"}
                      </TableHead>
                      <TableHead className="w-[15%] text-right">
                        {language === "de" ? "Aktionen" : "Actions"}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {group.entries.map(entry => (
                      <TableRow key={entry.id}>
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span>{entry.description}</span>
                            {entry.employeeName && (
                              <span className="text-xs text-muted-foreground">
                                {entry.employeeName}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {entry.projectName ? (
                            <div className="flex items-center">
                              <div
                                className="w-3 h-3 rounded-full mr-2"
                                style={{ backgroundColor: entry.projectColor }}
                              />
                              <span>{entry.projectName}</span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {formatTime(new Date(entry.startTime))}
                          {entry.endTime && ` - ${formatTime(new Date(entry.endTime))}`}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                            {entry.duration ? formatDuration(entry.duration) : "-"}
                            {entry.isBillable && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                {language === "de" ? "Abrechenbar" : "Billable"}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditEntry(entry)}>
                                <Edit className="h-4 w-4 mr-2" />
                                {language === "de" ? "Bearbeiten" : "Edit"}
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDeleteEntry(entry.id)}>
                                <Trash2 className="h-4 w-4 mr-2" />
                                {language === "de" ? "Löschen" : "Delete"}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Card>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-10">
          <Clock className="h-12 w-12 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">
            {language === "de" ? "Keine Zeiteinträge gefunden" : "No time entries found"}
          </h3>
          <p className="text-muted-foreground mt-2">
            {searchQuery || dateFilter || projectFilter || employeeFilter
              ? (language === "de" ? "Versuchen Sie, Ihre Filter anzupassen." : "Try adjusting your filters.")
              : (language === "de" ? "Beginnen Sie mit der Zeiterfassung, indem Sie auf 'Neuer Eintrag' klicken." : "Start tracking time by clicking 'New Entry'.")}
          </p>
        </div>
      )}

      {/* Edit Dialog */}
      {isEditDialogOpen && (
        <TimeEntryForm
          isOpen={isEditDialogOpen}
          onClose={() => {
            setIsEditDialogOpen(false);
            setEntryToEdit(null);
          }}
          entryToEdit={entryToEdit}
        />
      )}
    </div>
  );
}
