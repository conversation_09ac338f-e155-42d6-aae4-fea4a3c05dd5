"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Calculator, FileText, PiggyBank, TrendingUp, Lightbulb } from "lucide-react";
import { useState, useEffect } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'

type Suggestion = {
  id: number;
  title: string;
  description: string;
  potentialSavings: string;
  icon: React.ReactNode;
  difficulty: "Easy" | "Medium" | "Advanced";
};

export function TaxOptimization() {
  const { t, language } = useLanguage();
  const [mounted, setMounted] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [analyzed, setAnalyzed] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleAnalyze = () => {
    setIsAnalyzing(true);
    setSuggestions([]);
    setAnalyzed(false);

    // Simulate analysis (in a real app, this would call an API)
    setTimeout(() => {
      // Different suggestions based on language
      if (mounted && language === "de") {
        setSuggestions([
          {
            id: 1,
            title: "Geschäftsausgaben optimieren",
            description: "Basierend auf Ihren Rechnungsdaten könnten Sie mehr Ausgaben als geschäftsbezogen kategorisieren, um Abzüge zu erhöhen.",
            potentialSavings: "€1.200 - €2.500",
            icon: <FileText className="h-5 w-5 text-blue-500" />,
            difficulty: "Easy",
          },
          {
            id: 2,
            title: "Mehrwertsteuer-Optimierung",
            description: "Erwägen Sie, Ihre Mehrwertsteuer-Einreichungsstrategie anzupassen, um den Cashflow zu verbessern. Ihr aktuelles Muster zeigt Optimierungspotenzial.",
            potentialSavings: "€800 - €1.500",
            icon: <Calculator className="h-5 w-5 text-green-500" />,
            difficulty: "Medium",
          },
          {
            id: 3,
            title: "Altersvorsorgebeiträge",
            description: "Eine Erhöhung Ihrer Altersvorsorgebeiträge könnte Ihr steuerpflichtiges Einkommen erheblich reduzieren.",
            potentialSavings: "€2.000 - €4.000",
            icon: <PiggyBank className="h-5 w-5 text-purple-500" />,
            difficulty: "Medium",
          },
          {
            id: 4,
            title: "Investitionssteuergutschriften",
            description: "Sie könnten Anspruch auf Investitionssteuergutschriften basierend auf Ihrem Geschäftsprofil und kürzlich getätigten Einkäufen haben.",
            potentialSavings: "€1.500 - €3.000",
            icon: <TrendingUp className="h-5 w-5 text-orange-500" />,
            difficulty: "Advanced",
          },
          {
            id: 5,
            title: "Homeoffice-Abzug",
            description: "Basierend auf Ihren Adressinformationen könnten Sie Anspruch auf Homeoffice-Abzüge haben, die Sie noch nicht geltend gemacht haben.",
            potentialSavings: "€600 - €1.200",
            icon: <Lightbulb className="h-5 w-5 text-yellow-500" />,
            difficulty: "Easy",
          },
        ]);
      } else {
        setSuggestions([
          {
            id: 1,
            title: "Optimize Business Expenses",
            description: "Based on your invoice data, you could categorize more expenses as business-related to increase deductions.",
            potentialSavings: "€1,200 - €2,500",
            icon: <FileText className="h-5 w-5 text-blue-500" />,
            difficulty: "Easy",
          },
          {
            id: 2,
            title: "VAT Optimization",
            description: "Consider adjusting your VAT filing strategy to improve cash flow. Your current pattern shows potential for optimization.",
            potentialSavings: "€800 - €1,500",
            icon: <Calculator className="h-5 w-5 text-green-500" />,
            difficulty: "Medium",
          },
          {
            id: 3,
            title: "Retirement Contributions",
            description: "Increasing your retirement contributions could reduce your taxable income significantly.",
            potentialSavings: "€2,000 - €4,000",
            icon: <PiggyBank className="h-5 w-5 text-purple-500" />,
            difficulty: "Medium",
          },
          {
            id: 4,
            title: "Investment Tax Credits",
            description: "You may qualify for investment tax credits based on your business profile and recent purchases.",
            potentialSavings: "€1,500 - €3,000",
            icon: <TrendingUp className="h-5 w-5 text-orange-500" />,
            difficulty: "Advanced",
          },
          {
            id: 5,
            title: "Home Office Deduction",
            description: "Based on your address information, you might be eligible for home office deductions you haven't claimed.",
            potentialSavings: "€600 - €1,200",
            icon: <Lightbulb className="h-5 w-5 text-yellow-500" />,
            difficulty: "Easy",
          },
        ]);
      }
      setIsAnalyzing(false);
      setAnalyzed(true);
    }, 2500);
  };

  return (
    <div className="space-y-6">
      {!analyzed && (
        <div className="text-center py-8">
          <h3 className="text-xl font-medium mb-2">
            {mounted ? t("aiTaxOptimizationReady") : "Ready to optimize your taxes?"}
          </h3>
          <p className="text-muted-foreground mb-6">
            {mounted ? t("aiTaxOptimizationReadyDesc") : "Our AI will analyze your financial data and provide personalized tax-saving suggestions."}
          </p>
          <Button
            onClick={handleAnalyze}
            disabled={isAnalyzing}
            size="lg"
          >
            {isAnalyzing
              ? (mounted ? t("aiTaxOptimizationAnalyzing") : "Analyzing your data...")
              : (mounted ? t("aiTaxOptimizationAnalyze") : "Analyze My Tax Situation")
            }
          </Button>
        </div>
      )}

      {isAnalyzing && (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="relative">
            <div className="h-16 w-16 rounded-full border-4 border-t-blue-500 border-b-blue-700 border-l-blue-600 border-r-blue-400 animate-spin"></div>
            <Calculator className="h-6 w-6 text-blue-600 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
          </div>
          <p className="mt-4 text-lg font-medium">
            {mounted ? t("aiTaxOptimizationAnalyzing") : "Analyzing your financial data..."}
          </p>
          <p className="text-muted-foreground">
            {mounted ? t("aiTaxOptimizationAnalyzingDesc") : "This may take a moment"}
          </p>
        </div>
      )}

      {analyzed && suggestions.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-medium">
              {mounted ? t("aiTaxOptimizationResults") : "Your Tax Optimization Suggestions"}
            </h3>
            <Button variant="outline" size="sm" onClick={handleAnalyze}>
              {mounted ? t("aiTaxOptimizationRefresh") : "Refresh Analysis"}
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {suggestions.map((suggestion) => (
              <Card key={suggestion.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      {suggestion.icon}
                      <CardTitle className="text-lg">{suggestion.title}</CardTitle>
                    </div>
                    <Badge variant={
                      suggestion.difficulty === "Easy" ? "default" :
                      suggestion.difficulty === "Medium" ? "secondary" :
                      "outline"
                    }>
                      {mounted
                        ? suggestion.difficulty === "Easy"
                          ? t("aiTaxOptimizationDifficultyEasy")
                          : suggestion.difficulty === "Medium"
                            ? t("aiTaxOptimizationDifficultyMedium")
                            : t("aiTaxOptimizationDifficultyAdvanced")
                        : suggestion.difficulty
                      }
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm text-foreground/80 mb-2">
                    {suggestion.description}
                  </CardDescription>
                  <p className="text-sm font-medium">
                    {mounted ? t("aiTaxOptimizationPotentialSavings") : "Potential savings:"} <span className="text-green-600 dark:text-green-400">{suggestion.potentialSavings}</span>
                  </p>
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" size="sm" className="w-full justify-between">
                    {mounted ? t("aiTaxOptimizationLearnMore") : "Learn more"} <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
