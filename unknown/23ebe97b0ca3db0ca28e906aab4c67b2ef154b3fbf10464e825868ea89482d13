"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Edit, Trash2, Mail, Phone, Building, Eye, Download, RefreshCw } from "lucide-react";
import { Advisor } from "@/app/lib/wegTypes";
import { addAdvisor, deleteAdvisor, regenerateAccessToken } from "@/app/actions/advisors";
import { toast } from "sonner";
import { EditAdvisorDialog } from "./EditAdvisorDialog";

export interface AdvisorListProps {
  advisors: Advisor[];
  onSelectAdvisor: (advisor: Advisor) => void;
  onUpdateAdvisors: (advisors: Advisor[]) => void;
  onRefreshData?: () => void;
}

export function AdvisorList({ advisors, onSelectAdvisor, onUpdateAdvisors, onRefreshData }: AdvisorListProps) {
  const { language } = useLanguage();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isRegenerateTokenDialogOpen, setIsRegenerateTokenDialogOpen] = useState(false);
  const [selectedAdvisorId, setSelectedAdvisorId] = useState<string | null>(null);
  const [selectedAdvisorForEdit, setSelectedAdvisorForEdit] = useState<Advisor | null>(null);
  const [newAdvisor, setNewAdvisor] = useState({
    name: "",
    email: "",
    phone: "",
    company: "",
    notes: "",
    canViewWEG: true,
    canViewInvoices: false,
    canViewEmployees: false,
    canViewAccounting: true,
    canViewReports: true,
    canViewReserves: true,
    canViewOwners: true,
    canViewProperties: true,
    canDownload: true,
    canEdit: false,
  });

  // Handler für das Hinzufügen eines neuen Beraters
  const handleAddAdvisor = async () => {
    try {
      const dbAdvisor = await addAdvisor({
        name: newAdvisor.name,
        email: newAdvisor.email,
        phone: newAdvisor.phone,
        company: newAdvisor.company,
        notes: newAdvisor.notes,
        canViewWEG: newAdvisor.canViewWEG,
        canViewInvoices: newAdvisor.canViewInvoices,
        canViewEmployees: newAdvisor.canViewEmployees,
        canViewAccounting: newAdvisor.canViewAccounting,
        canViewReports: newAdvisor.canViewReports,
        canViewReserves: newAdvisor.canViewReserves,
        canViewOwners: newAdvisor.canViewOwners,
        canViewProperties: newAdvisor.canViewProperties,
        canDownload: newAdvisor.canDownload,
        canEdit: newAdvisor.canEdit
      });

      // Convert to wegTypes Advisor
      const wegAdvisor: Advisor = {
        id: dbAdvisor.id,
        name: dbAdvisor.name,
        email: dbAdvisor.email,
        phone: dbAdvisor.phone || undefined,
        address: dbAdvisor.company || undefined,
        notes: dbAdvisor.notes || undefined,
        permissions: {
          canViewProperties: newAdvisor.canViewProperties,
          canViewOwners: newAdvisor.canViewOwners,
          canViewAccounting: newAdvisor.canViewAccounting,
          canViewInvoices: newAdvisor.canViewInvoices,
          canViewReports: newAdvisor.canViewReports,
          canDownloadDocuments: newAdvisor.canDownload,
          canEdit: newAdvisor.canEdit
        }
      };

      onUpdateAdvisors([...advisors, wegAdvisor]);
      setIsAddDialogOpen(false);
      setNewAdvisor({
        name: "",
        email: "",
        phone: "",
        company: "",
        notes: "",
        canViewWEG: true,
        canViewInvoices: false,
        canViewEmployees: false,
        canViewAccounting: true,
        canViewReports: true,
        canViewReserves: true,
        canViewOwners: true,
        canViewProperties: true,
        canDownload: true,
        canEdit: false,
      });
      toast.success(
        language === "de"
          ? "Berater erfolgreich hinzugefügt"
          : "Advisor added successfully"
      );
    } catch (error) {
      console.error("Failed to add advisor:", error);
      toast.error(
        language === "de"
          ? "Fehler beim Hinzufügen des Beraters"
          : "Failed to add advisor"
      );
    }
  };

  // Handler für das Löschen eines Beraters
  const handleDeleteAdvisor = async () => {
    if (!selectedAdvisorId) return;

    try {
      await deleteAdvisor(selectedAdvisorId);
      onUpdateAdvisors(advisors.filter((advisor) => advisor.id !== selectedAdvisorId));
      setIsDeleteDialogOpen(false);
      setSelectedAdvisorId(null);
      toast.success(
        language === "de"
          ? "Berater erfolgreich gelöscht"
          : "Advisor deleted successfully"
      );
    } catch (error) {
      console.error("Failed to delete advisor:", error);
      toast.error(
        language === "de"
          ? "Fehler beim Löschen des Beraters"
          : "Failed to delete advisor"
      );
    }
  };

  // Handler für die Regenerierung des Zugriffstokens
  const handleRegenerateToken = async () => {
    if (!selectedAdvisorId) return;

    try {
      const newToken = await regenerateAccessToken(selectedAdvisorId);
      setIsRegenerateTokenDialogOpen(false);
      setSelectedAdvisorId(null);
      toast.success(
        language === "de"
          ? "Zugriffstoken erfolgreich erneuert"
          : "Access token regenerated successfully"
      );
    } catch (error) {
      console.error("Failed to regenerate token:", error);
      toast.error(
        language === "de"
          ? "Fehler bei der Erneuerung des Zugriffstokens"
          : "Failed to regenerate access token"
      );
    }
  };

  // Handler für das Öffnen des Edit-Dialogs
  const handleEditAdvisor = (advisor: Advisor) => {
    setSelectedAdvisorForEdit(advisor);
    setIsEditDialogOpen(true);
  };

  // Handler für das Schließen des Edit-Dialogs
  const handleCloseEditDialog = () => {
    setIsEditDialogOpen(false);
    setSelectedAdvisorForEdit(null);
  };

  // Handler für das Aktualisieren eines Beraters
  const handleUpdateAdvisor = (updatedAdvisor: Advisor) => {
    const updatedAdvisors = advisors.map((advisor) =>
      advisor.id === updatedAdvisor.id ? updatedAdvisor : advisor
    );
    onUpdateAdvisors(updatedAdvisors);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>
            {language === "de" ? "Steuerberater & Berater" : "Tax Advisors & Consultants"}
          </CardTitle>
          <CardDescription>
            {language === "de"
              ? "Verwalten Sie Personen, die Ihre WEG-Abrechnungen einsehen können"
              : "Manage people who can view your property management accounting"}
          </CardDescription>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {language === "de" ? "Berater hinzufügen" : "Add Advisor"}
        </Button>
      </CardHeader>
      <CardContent>
        {advisors.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              {language === "de"
                ? "Keine Berater vorhanden. Fügen Sie einen Berater hinzu, um Ihre WEG-Abrechnungen zu teilen."
                : "No advisors available. Add an advisor to share your property management accounting."}
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "de" ? "Name" : "Name"}</TableHead>
                <TableHead>{language === "de" ? "Kontakt" : "Contact"}</TableHead>
                <TableHead>{language === "de" ? "Berechtigungen" : "Permissions"}</TableHead>
                <TableHead className="text-right">{language === "de" ? "Aktionen" : "Actions"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {advisors.map((advisor) => (
                <TableRow key={advisor.id}>
                  <TableCell className="font-medium">{advisor.name}</TableCell>
                  <TableCell>
                    <div className="flex flex-col space-y-1">
                      <div className="flex items-center text-sm">
                        <Mail className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                        {advisor.email}
                      </div>
                      {advisor.phone && (
                        <div className="flex items-center text-sm">
                          <Phone className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                          {advisor.phone}
                        </div>
                      )}
                      {advisor.address && (
                        <div className="flex items-center text-sm">
                          <Building className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                          {advisor.address}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-3.5 w-3.5 text-muted-foreground" />
                        <span className="text-xs">WEG</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Download className="h-3.5 w-3.5 text-muted-foreground" />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditAdvisor(advisor)}
                      >
                        <Edit className="h-3.5 w-3.5" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedAdvisorId(advisor.id);
                          setIsRegenerateTokenDialogOpen(true);
                        }}
                      >
                        <RefreshCw className="h-3.5 w-3.5" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedAdvisorId(advisor.id);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        {/* Dialog zum Hinzufügen eines Beraters */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {language === "de" ? "Berater hinzufügen" : "Add Advisor"}
              </DialogTitle>
              <DialogDescription>
                {language === "de"
                  ? "Fügen Sie einen Steuerberater oder Berater hinzu, der Ihre WEG-Abrechnungen einsehen kann."
                  : "Add a tax advisor or consultant who can view your property management accounting."}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  {language === "de" ? "Name" : "Name"}
                </Label>
                <Input
                  id="name"
                  value={newAdvisor.name}
                  onChange={(e) => setNewAdvisor({ ...newAdvisor, name: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  {language === "de" ? "E-Mail" : "Email"}
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={newAdvisor.email}
                  onChange={(e) => setNewAdvisor({ ...newAdvisor, email: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone" className="text-right">
                  {language === "de" ? "Telefon" : "Phone"}
                </Label>
                <Input
                  id="phone"
                  value={newAdvisor.phone}
                  onChange={(e) => setNewAdvisor({ ...newAdvisor, phone: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="company" className="text-right">
                  {language === "de" ? "Unternehmen" : "Company"}
                </Label>
                <Input
                  id="company"
                  value={newAdvisor.company}
                  onChange={(e) => setNewAdvisor({ ...newAdvisor, company: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="notes" className="text-right">
                  {language === "de" ? "Notizen" : "Notes"}
                </Label>
                <Textarea
                  id="notes"
                  value={newAdvisor.notes}
                  onChange={(e) => setNewAdvisor({ ...newAdvisor, notes: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">
                  {language === "de" ? "Berechtigungen" : "Permissions"}
                </Label>
                <div className="col-span-3 space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewWEG"
                      checked={newAdvisor.canViewWEG}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canViewWEG: checked as boolean })
                      }
                    />
                    <Label htmlFor="canViewWEG">
                      {language === "de" ? "WEG-Abrechnung einsehen" : "View property management accounting"}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewInvoices"
                      checked={newAdvisor.canViewInvoices}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canViewInvoices: checked as boolean })
                      }
                    />
                    <Label htmlFor="canViewInvoices">
                      {language === "de" ? "Rechnungen einsehen" : "View invoices"}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewEmployees"
                      checked={newAdvisor.canViewEmployees}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canViewEmployees: checked as boolean })
                      }
                    />
                    <Label htmlFor="canViewEmployees">
                      {language === "de" ? "Mitarbeiter einsehen" : "View employees"}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canDownload"
                      checked={newAdvisor.canDownload}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canDownload: checked as boolean })
                      }
                    />
                    <Label htmlFor="canDownload">
                      {language === "de" ? "Daten herunterladen" : "Download data"}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewAccounting"
                      checked={newAdvisor.canViewAccounting}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canViewAccounting: checked as boolean })
                      }
                    />
                    <Label htmlFor="canViewAccounting">
                      {language === "de" ? "Buchhaltung einsehen" : "View accounting"}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewReports"
                      checked={newAdvisor.canViewReports}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canViewReports: checked as boolean })
                      }
                    />
                    <Label htmlFor="canViewReports">
                      {language === "de" ? "Berichte einsehen" : "View reports"}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewReserves"
                      checked={newAdvisor.canViewReserves}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canViewReserves: checked as boolean })
                      }
                    />
                    <Label htmlFor="canViewReserves">
                      {language === "de" ? "Rücklagen einsehen" : "View reserves"}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewOwners"
                      checked={newAdvisor.canViewOwners}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canViewOwners: checked as boolean })
                      }
                    />
                    <Label htmlFor="canViewOwners">
                      {language === "de" ? "Eigentümer einsehen" : "View owners"}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canViewProperties"
                      checked={newAdvisor.canViewProperties}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canViewProperties: checked as boolean })
                      }
                    />
                    <Label htmlFor="canViewProperties">
                      {language === "de" ? "Eigenschaften einsehen" : "View properties"}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="canEdit"
                      checked={newAdvisor.canEdit}
                      onCheckedChange={(checked) =>
                        setNewAdvisor({ ...newAdvisor, canEdit: checked as boolean })
                      }
                    />
                    <Label htmlFor="canEdit">
                      {language === "de" ? "Bearbeiten erlaubt" : "Allow editing"}
                    </Label>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                {language === "de" ? "Abbrechen" : "Cancel"}
              </Button>
              <Button onClick={handleAddAdvisor}>
                {language === "de" ? "Hinzufügen" : "Add"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog zum Löschen eines Beraters */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {language === "de" ? "Berater löschen" : "Delete Advisor"}
              </DialogTitle>
              <DialogDescription>
                {language === "de"
                  ? "Sind Sie sicher, dass Sie diesen Berater löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden."
                  : "Are you sure you want to delete this advisor? This action cannot be undone."}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                {language === "de" ? "Abbrechen" : "Cancel"}
              </Button>
              <Button variant="destructive" onClick={handleDeleteAdvisor}>
                {language === "de" ? "Löschen" : "Delete"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Dialog zum Regenerieren des Zugriffstokens */}
        <Dialog open={isRegenerateTokenDialogOpen} onOpenChange={setIsRegenerateTokenDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {language === "de" ? "Zugriffstoken erneuern" : "Regenerate Access Token"}
              </DialogTitle>
              <DialogDescription>
                {language === "de"
                  ? "Sind Sie sicher, dass Sie das Zugriffstoken für diesen Berater erneuern möchten? Der Berater muss den neuen Link erhalten, um wieder Zugriff zu haben."
                  : "Are you sure you want to regenerate the access token for this advisor? The advisor will need to receive the new link to regain access."}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsRegenerateTokenDialogOpen(false)}>
                {language === "de" ? "Abbrechen" : "Cancel"}
              </Button>
              <Button onClick={handleRegenerateToken}>
                {language === "de" ? "Token erneuern" : "Regenerate Token"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Advisor Dialog */}
        <EditAdvisorDialog
          advisor={selectedAdvisorForEdit}
          isOpen={isEditDialogOpen}
          onClose={handleCloseEditDialog}
          onUpdate={handleUpdateAdvisor}
          onRefreshData={onRefreshData}
        />
      </CardContent>
    </Card>
  );
}
