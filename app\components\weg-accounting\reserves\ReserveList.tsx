"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Plus, Search, Edit, Trash2, FileText, Download, Eye, PiggyBank, Building, TrendingUp } from "lucide-react";
import { Property } from "../properties/PropertyList";

// Typen für die Datenstruktur
export type ReserveTransactionType = "contribution" | "withdrawal" | "interest" | "transfer";

export type ReserveTransaction = {
  id: string;
  reserveId: string;
  date: string;
  type: ReserveTransactionType;
  amount: number;
  description: string;
  documentId?: string;
};

export type Reserve = {
  id: string;
  propertyId: string;
  name: string;
  description: string;
  balance: number;
  targetAmount?: number;
  yearlyContribution: number;
  transactions: ReserveTransaction[];
};

interface ReserveListProps {
  reserves: Reserve[];
  properties: Property[];
  onSelectReserve: (reserve: Reserve) => void;
  onUpdateReserves: (reserves: Reserve[]) => void;
}

export function ReserveList({
  reserves,
  properties,
  onSelectReserve,
  onUpdateReserves
}: ReserveListProps) {
  const { language } = useLanguage();

  // State für das Hinzufügen einer neuen Rücklage
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newReserve, setNewReserve] = useState<Omit<Reserve, "id" | "balance" | "transactions">>({
    propertyId: properties.length > 0 ? properties[0].id : "",
    name: "",
    description: "",
    yearlyContribution: 0,
    targetAmount: undefined,
  });

  // State für die Suche
  const [searchQuery, setSearchQuery] = useState("");
  const [filterProperty, setFilterProperty] = useState<string>("all");

  // Gefilterte Rücklagen basierend auf der Suche und den Filtern
  const filteredReserves = reserves.filter(reserve => {
    // Suche
    const matchesSearch =
      reserve.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reserve.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Objekt-Filter
    const matchesProperty = filterProperty === "all" || reserve.propertyId === filterProperty;

    return matchesSearch && matchesProperty;
  });

  // Handler für das Hinzufügen einer neuen Rücklage
  const handleAddReserve = () => {
    const newId = (reserves.length + 1).toString();
    const reserveToAdd: Reserve = {
      id: newId,
      ...newReserve,
      balance: 0,
      transactions: [],
    };

    onUpdateReserves([...reserves, reserveToAdd]);
    setNewReserve({
      propertyId: properties.length > 0 ? properties[0].id : "",
      name: "",
      description: "",
      yearlyContribution: 0,
      targetAmount: undefined,
    });
    setIsAddDialogOpen(false);
  };

  // Handler für das Löschen einer Rücklage
  const handleDeleteReserve = (id: string) => {
    onUpdateReserves(reserves.filter(reserve => reserve.id !== id));
  };

  // Funktion zum Abrufen des Objektnamens anhand der ID
  const getPropertyName = (propertyId: string) => {
    const property = properties.find(p => p.id === propertyId);
    return property ? property.name : "Unbekanntes Objekt";
  };

  // Funktion zum Formatieren eines Betrags als Währung
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "de" ? "de-AT" : "en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  // Funktion zum Berechnen des Fortschritts einer Rücklage
  const calculateProgress = (reserve: Reserve) => {
    if (!reserve.targetAmount || reserve.targetAmount <= 0) return 100;
    const progress = (reserve.balance / reserve.targetAmount) * 100;
    return Math.min(progress, 100);
  };

  // Berechne die Gesamtsumme der Rücklagen
  const totalBalance = filteredReserves.reduce((total, reserve) => total + reserve.balance, 0);

  // Berechne die Gesamtsumme der jährlichen Beiträge
  const totalYearlyContribution = filteredReserves.reduce((total, reserve) => total + reserve.yearlyContribution, 0);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">
          {language === "de" ? "Rücklagen" : "Reserves"}
        </h2>

        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder={language === "de" ? "Rücklagen suchen..." : "Search reserves..."}
              className="pl-8 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={() => setIsAddDialogOpen(true)}
                className="hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer"
              >
                <Plus className="h-4 w-4 mr-2" />
                {language === "de" ? "Neue Rücklage" : "New Reserve"}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {language === "de" ? "Neue Rücklage erstellen" : "Create New Reserve"}
                </DialogTitle>
                <DialogDescription>
                  {language === "de"
                    ? "Geben Sie die Details der neuen Rücklage ein."
                    : "Enter the details of the new reserve."}
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="propertyId">
                    {language === "de" ? "Objekt" : "Property"}
                  </Label>
                  <Select
                    value={newReserve.propertyId}
                    onValueChange={(value) => setNewReserve({ ...newReserve, propertyId: value })}
                  >
                    <SelectTrigger id="propertyId">
                      <SelectValue placeholder={language === "de" ? "Objekt auswählen" : "Select property"} />
                    </SelectTrigger>
                    <SelectContent className="bg-background border">
                      {properties.map((property) => (
                        <SelectItem key={property.id} value={property.id}>
                          {property.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name">
                    {language === "de" ? "Name" : "Name"}
                  </Label>
                  <Input
                    id="name"
                    value={newReserve.name}
                    onChange={(e) => setNewReserve({ ...newReserve, name: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">
                    {language === "de" ? "Beschreibung" : "Description"}
                  </Label>
                  <Input
                    id="description"
                    value={newReserve.description}
                    onChange={(e) => setNewReserve({ ...newReserve, description: e.target.value })}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="yearlyContribution">
                      {language === "de" ? "Jährlicher Beitrag (€)" : "Yearly Contribution (€)"}
                    </Label>
                    <Input
                      id="yearlyContribution"
                      type="number"
                      step="0.01"
                      value={newReserve.yearlyContribution}
                      onChange={(e) => setNewReserve({ ...newReserve, yearlyContribution: parseFloat(e.target.value) || 0 })}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="targetAmount">
                      {language === "de" ? "Zielbetrag (€, optional)" : "Target Amount (€, optional)"}
                    </Label>
                    <Input
                      id="targetAmount"
                      type="number"
                      step="0.01"
                      value={newReserve.targetAmount || ""}
                      onChange={(e) => {
                        const value = e.target.value ? parseFloat(e.target.value) : undefined;
                        setNewReserve({ ...newReserve, targetAmount: value });
                      }}
                    />
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  {language === "de" ? "Abbrechen" : "Cancel"}
                </Button>
                <Button onClick={handleAddReserve}>
                  {language === "de" ? "Erstellen" : "Create"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filter */}
      <div className="flex flex-wrap gap-2">
        <div className="flex items-center space-x-2">
          <Label htmlFor="filterProperty" className="text-sm whitespace-nowrap">
            {language === "de" ? "Objekt:" : "Property:"}
          </Label>
          <Select
            value={filterProperty}
            onValueChange={setFilterProperty}
          >
            <SelectTrigger id="filterProperty" className="h-8 w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-background border">
              <SelectItem value="all">
                {language === "de" ? "Alle Objekte" : "All Properties"}
              </SelectItem>
              {properties.map((property) => (
                <SelectItem key={property.id} value={property.id}>
                  {property.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Zusammenfassung */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Gesamtbetrag" : "Total Balance"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalBalance)}</div>
            <div className="text-sm text-muted-foreground mt-1">
              {language === "de" ? "Anzahl Rücklagen: " : "Number of reserves: "} {filteredReserves.length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Jährliche Beiträge" : "Yearly Contributions"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalYearlyContribution)}</div>
            <div className="text-sm text-muted-foreground mt-1">
              {language === "de" ? "Pro Monat: " : "Per month: "} {formatCurrency(totalYearlyContribution / 12)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Aktionen" : "Actions"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="w-full">
                <Download className="h-4 w-4 mr-2" />
                {language === "de" ? "Exportieren" : "Export"}
              </Button>
              <Button variant="outline" size="sm" className="w-full">
                <FileText className="h-4 w-4 mr-2" />
                {language === "de" ? "Bericht" : "Report"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Rücklagenliste */}
      {filteredReserves.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredReserves.map((reserve) => (
            <Card
              key={reserve.id}
              className="overflow-hidden cursor-pointer hover:border-primary transition-colors"
              onClick={() => onSelectReserve(reserve)}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{reserve.name}</CardTitle>
                    <CardDescription className="mt-1">
                      {getPropertyName(reserve.propertyId)}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0"
                        onClick={(e) => e.stopPropagation()} // Verhindert, dass das Klicken auf das Menü die Karte auswählt
                      >
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>
                        {language === "de" ? "Aktionen" : "Actions"}
                      </DropdownMenuLabel>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        onSelectReserve(reserve);
                      }}>
                        <Eye className="h-4 w-4 mr-2" />
                        {language === "de" ? "Anzeigen" : "View"}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        // Hier könnte eine Funktion zum Exportieren der Rücklage aufgerufen werden
                      }}>
                        <Download className="h-4 w-4 mr-2" />
                        {language === "de" ? "Exportieren" : "Export"}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteReserve(reserve.id);
                      }} className="text-red-600 dark:text-red-400">
                        <Trash2 className="h-4 w-4 mr-2" />
                        {language === "de" ? "Löschen" : "Delete"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Aktueller Stand" : "Current Balance"}
                    </span>
                    <span className="font-medium">{formatCurrency(reserve.balance)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Jährlicher Beitrag" : "Yearly Contribution"}
                    </span>
                    <span className="font-medium">{formatCurrency(reserve.yearlyContribution)}</span>
                  </div>

                  {reserve.targetAmount && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">
                          {language === "de" ? "Zielbetrag" : "Target Amount"}
                        </span>
                        <span>{formatCurrency(reserve.targetAmount)}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2.5">
                        <div
                          className="bg-primary h-2.5 rounded-full"
                          style={{ width: `${calculateProgress(reserve)}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>
                          {language === "de" ? "Fortschritt" : "Progress"}
                        </span>
                        <span>
                          {Math.round(calculateProgress(reserve))}%
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="bg-muted/50 p-3">
                <div className="w-full">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      {language === "de" ? "Transaktionen" : "Transactions"}
                    </span>
                    <Badge variant="outline">
                      {reserve.transactions.length}
                    </Badge>
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-10">
          <PiggyBank className="h-10 w-10 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">
            {language === "de" ? "Keine Rücklagen gefunden" : "No reserves found"}
          </h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {language === "de"
              ? "Beginnen Sie damit, eine neue Rücklage zu erstellen."
              : "Start by creating a new reserve."}
          </p>
        </div>
      )}
    </div>
  );
}
