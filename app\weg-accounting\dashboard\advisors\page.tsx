"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/app/utils/clientHooks";
import { AdvisorsTab } from "@/app/components/weg-accounting/advisors/AdvisorsTab";
import { LoadingState } from "@/app/components/LoadingState";
import { Advisor } from "@/app/lib/wegTypes";

export default function AdvisorsPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [advisors, setAdvisors] = useState<Advisor[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const loadData = async () => {
    try {
      setIsLoading(true);
      // Fetch advisors from API
      const advisorsResponse = await fetch('/api/weg-accounting/advisors');
      if (!advisorsResponse.ok) {
        throw new Error(`Failed to fetch advisors: ${advisorsResponse.statusText}`);
      }
      const advisorsData = await advisorsResponse.json();
      setAdvisors(advisorsData);
    } catch (error) {
      console.error("Failed to load data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Only load data if the user is authenticated
    if (isAuthenticated) {
      loadData();
    }
  }, [isAuthenticated]);

  const refreshData = () => {
    if (isAuthenticated) {
      loadData();
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Steuerberater</h1>
        <p className="text-muted-foreground">
          Verwalten Sie Zugriffsrechte für Ihre Steuerberater
        </p>
      </div>

      {authLoading || isLoading ? (
        <LoadingState
          title="Steuerberaterdaten werden geladen..."
          description="Bitte warten Sie, während die Daten geladen werden."
        />
      ) : (
        <AdvisorsTab advisors={advisors} initialAdvisors={advisors} onRefreshData={refreshData} />
      )}
    </div>
  );
}
