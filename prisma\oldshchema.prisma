generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String          @id @default(cuid())
  firstName     String?
  lastName      String?
  address       String?
  email         String          @unique
  password      String?         // Password for username/password authentication
  emailVerified DateTime?
  image         String?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  accounts      Account[]
  Authenticator Authenticator[]
  employees     Employee[]
  invoices      Invoice[]
  sessions      Session[]
  settings      UserSettings?
  advisors      Advisor[]       // Steuerberater, die der Benutzer erstellt hat
  userAdvisors  UserAdvisor[]   // Beziehung zu Steuerberatern
}

model UserSettings {
  id          String   @id @default(cuid())
  userId      String   @unique
  language    String   @default("en")
  theme       String   @default("system")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  visibleTabs String   @default("dashboard,invoices,employees,income-expenses,ai,taxReturn,wegAccounting")
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
  user                 User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

model Invoice {
  id                     String        @id @default(uuid())
  invoiceName            String
  total                  Int
  status                 InvoiceStatus
  date                   DateTime
  dueDate                Int
  fromName               String
  fromEmail              String
  fromAddress            String
  clientName             String
  clientEmail            String
  clientAddress          String
  currency               String
  invoiceNumber          Int
  note                   String?
  invoiceItemDescription String
  invoiceItemQuantity    Int
  invoiceItemRate        Int
  createdAt              DateTime      @default(now())
  updatedAt              DateTime      @updatedAt
  userId                 String?
  User                   User?         @relation(fields: [userId], references: [id])
}

model Employee {
  id         String   @id @default(cuid())
  userId     String
  name       String
  email      String
  position   String
  department String
  startDate  DateTime
  salary     Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

enum InvoiceStatus {
  PAID
  PENDING
}

model Advisor {
  id           String        @id @default(cuid())
  name         String
  email        String        @unique
  phone        String?
  company      String?
  notes        String?
  isActive     Boolean       @default(true)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  userId       String
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  userAdvisors UserAdvisor[]

  @@index([userId])
}

model UserAdvisor {
  id             String    @id @default(cuid())
  userId         String
  advisorId      String
  canViewWEG     Boolean   @default(true)
  canViewInvoices Boolean   @default(false)
  canViewEmployees Boolean   @default(false)
  canDownload    Boolean   @default(true)
  accessToken    String    @unique
  expiresAt      DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  advisor        Advisor   @relation(fields: [advisorId], references: [id], onDelete: Cascade)

  @@unique([userId, advisorId])
  @@index([userId])
  @@index([advisorId])
  @@index([accessToken])
}
