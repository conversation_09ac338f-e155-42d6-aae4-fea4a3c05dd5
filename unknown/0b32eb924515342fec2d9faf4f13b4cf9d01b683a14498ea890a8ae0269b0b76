import { verifyAdvisorToken } from "@/app/utils/advisorAuth";
import { AdvisorViewDashboard } from "@/app/components/advisor-view/AdvisorViewDashboard";

type Params = { token: string };

export default async function AdvisorAccessPage({ params }: { params: Promise<Params> }) {
  const { token } = await params;
  const userAdvisor = await verifyAdvisorToken(token);
  
  return (
    <AdvisorViewDashboard 
      userAdvisor={userAdvisor} 
      token={token} 
    />
  );
}
