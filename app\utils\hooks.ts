import { redirect } from "next/navigation";
import { auth } from "./auth";

export async function requireUser(options?: { redirectToWegLogin?: boolean }) {
  const session = await auth();

  if (!session?.user) {
    // Wenn die Option gesetzt ist, leiten wir zum WEG-Accounting-Login weiter
    if (options?.redirectToWegLogin) {
      redirect("/weg-accounting/login");
    } else {
      // Ansonsten zum normalen Login
      redirect("/login");
    }
  }

  return session;
}
