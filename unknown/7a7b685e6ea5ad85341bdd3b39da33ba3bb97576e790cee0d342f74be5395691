"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { TranslationKey } from "@/i18n";
import { Employee } from "@/app/lib/definitions";
import { updateEmployee, deleteEmployee } from "@/app/actions/employees";
import { useToast } from "@/app/context/ToastContext";

export function EditEmployee({ data }: { data: Employee }) {
  const router = useRouter();
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [formData, setFormData] = useState({
    name: data.name,
    email: data.email,
    position: data.position,
    department: data.department,
    startDate: new Date(data.startDate).toISOString().split('T')[0],
    salary: typeof data.salary === 'string' ? data.salary : data.salary.toString(),
  });

  const { toast } = useToast();

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await updateEmployee(data.id, formData);
      toast.success(
        t('employeeUpdated' as TranslationKey),
        {
          description: t('employeeUpdatedSuccess' as TranslationKey)
        }
      );
      router.push("/dashboard/employees");
    } catch (err) {
      toast.error(
        t('error' as TranslationKey),
        {
          description: t('employeeUpdateFailed' as TranslationKey)
        }
      );
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (confirm(t('confirmDeleteEmployee' as TranslationKey))) {
      setIsDeleting(true);

      try {
        await deleteEmployee(data.id);
        toast.success(
          t('employeeDeleted' as TranslationKey),
          {
            description: t('employeeDeletedSuccess' as TranslationKey)
          }
        );
        router.push("/dashboard/employees");
      } catch (err) {
        toast.error(
          t('error' as TranslationKey),
          {
            description: t('employeeDeleteFailed' as TranslationKey)
          }
        );
        console.error(err);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">
          {mounted ? t('editEmployee' as TranslationKey) : "Mitarbeiter bearbeiten"}
        </h1>
        <Button variant="destructive" onClick={handleDelete} disabled={isDeleting}>
          {isDeleting
            ? mounted ? t('deleting' as TranslationKey) : "Wird gelöscht..."
            : mounted ? t('deleteEmployee' as TranslationKey) : "Mitarbeiter löschen"}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {mounted ? t('editEmployeeDetails' as TranslationKey) : "Mitarbeiterdetails bearbeiten"}
          </CardTitle>
          <CardDescription>
            {mounted ? t('updateEmployeeInfo' as TranslationKey) : "Mitarbeiterinformationen aktualisieren"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  {mounted ? t('employeeName' as TranslationKey) : "Name des Mitarbeiters"}
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">
                  {mounted ? t('email' as TranslationKey) : "E-Mail"}
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="position">
                  {mounted ? t('position' as TranslationKey) : "Position"}
                </Label>
                <Input
                  id="position"
                  name="position"
                  value={formData.position}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">
                  {mounted ? t('department' as TranslationKey) : "Abteilung"}
                </Label>
                <Select
                  value={formData.department}
                  onValueChange={(value) => handleSelectChange("department", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={mounted ? t('selectDepartment' as TranslationKey) : "Abteilung wählen"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hr">HR</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                    <SelectItem value="sales">Sales</SelectItem>
                    <SelectItem value="development">Development</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="startDate">
                  {mounted ? t('startDate' as TranslationKey) : "Startdatum"}
                </Label>
                <Input
                  id="startDate"
                  name="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={handleChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="salary">
                  {mounted ? t('salary' as TranslationKey) : "Gehalt"}
                </Label>
                <Input
                  id="salary"
                  name="salary"
                  type="number"
                  value={formData.salary}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                type="button"
                onClick={() => router.push("/dashboard/employees")}
              >
                {mounted ? t('cancel' as TranslationKey) : "Abbrechen"}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? mounted ? t('updating' as TranslationKey) : "Aktualisiere..."
                  : mounted ? t('updateEmployee' as TranslationKey) : "Mitarbeiter aktualisieren"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
