import { getEmployees } from "@/app/actions/employees";
import { EmployeesClient } from "@/app/components/employees/EmployeesClient";
import { requireUser } from "@/app/utils/hooks";

export const dynamic = "force-dynamic";

export default async function EmployeesPage() {
  await requireUser();
  
  // Fetch employees data
  const employees = await getEmployees();

  return <EmployeesClient initialEmployees={employees} />;
}
