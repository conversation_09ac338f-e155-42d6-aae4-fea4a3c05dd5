"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Building, Calendar, Home, Users } from "lucide-react";
import { Property } from "./PropertyList";
import { DistributionKeyManager } from "./DistributionKeyManager";
import { OwnerList, Owner } from "../owners/OwnerList";
import { OwnerDetail } from "../owners/OwnerDetail";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface PropertyDetailProps {
  property: Property;
  owners: Owner[];
  onBack: () => void;
  onUpdate: (property: Property) => void;
}

// Owners werden jetzt über Props übergeben

export function PropertyDetail({ property, owners: allOwners, onBack, onUpdate }: PropertyDetailProps) {
  const { language } = useLanguage();
  const [selectedOwner, setSelectedOwner] = useState<Owner | null>(null);
  const [owners, setOwners] = useState<Owner[]>(allOwners.filter((owner: Owner) => owner.units.some((unit: any) => unit.propertyId === property.id)));
  const [selectedUnit, setSelectedUnit] = useState<any | null>(null);
  const [isAddUnitDialogOpen, setIsAddUnitDialogOpen] = useState(false);
  const [newUnit, setNewUnit] = useState({
    ownerId: owners.length > 0 ? owners[0].id : "",
    unitNumber: "",
    area: 0,
    ownershipPercentage: 100,
  });
  const [addUnitError, setAddUnitError] = useState<string | null>(null);

  const handleSelectOwner = (owner: Owner) => setSelectedOwner(owner);
  const handleUpdateOwner = (updatedOwner: Owner) => {
    setOwners(prev => prev.map(o => o.id === updatedOwner.id ? updatedOwner : o));
    if (selectedOwner && selectedOwner.id === updatedOwner.id) setSelectedOwner(updatedOwner);
  };
  const handleUpdateOwners = (newOwners: Owner[]) => setOwners(newOwners);

  // Alle Einheiten dieses Objekts (über alle Eigentümer)
  const allUnits = owners.flatMap(owner => owner.units.map(unit => ({ ...unit, ownerId: owner.id, ownerName: owner.firstName + " " + owner.lastName }))).filter(unit => unit.propertyId === property.id);

  // Handler zum Hinzufügen einer neuen Einheit
  const handleAddUnit = () => {
    setAddUnitError(null);
    if (!newUnit.ownerId) {
      setAddUnitError(language === "de" ? "Bitte wählen Sie einen Eigentümer aus." : "Please select an owner.");
      return;
    }
    const ownerIdx = owners.findIndex(o => o.id === newUnit.ownerId);
    if (ownerIdx === -1) {
      setAddUnitError(language === "de" ? "Eigentümer nicht gefunden." : "Owner not found.");
      return;
    }
    const unitId = (owners[ownerIdx].units.length + 1).toString();
    const unitToAdd = { id: unitId, propertyId: property.id, unitNumber: newUnit.unitNumber, area: newUnit.area, ownershipPercentage: newUnit.ownershipPercentage };
    const updatedOwners = owners.map((o, i) => i === ownerIdx ? { ...o, units: [...o.units, unitToAdd] } : o);
    setOwners(updatedOwners);
    setIsAddUnitDialogOpen(false);
    setNewUnit({ ownerId: owners[0]?.id || "", unitNumber: "", area: 0, ownershipPercentage: 100 });
  };

  // Handler zum Bearbeiten einer Einheit
  const handleEditUnit = (unit: any) => setSelectedUnit(unit);
  const handleSaveEditUnit = (editedUnit: any) => {
    const updatedOwners = owners.map(owner => {
      if (owner.id !== editedUnit.ownerId) return owner;
      return {
        ...owner,
        units: owner.units.map(u => u.id === editedUnit.id ? { ...u, ...editedUnit } : u)
      };
    });
    setOwners(updatedOwners);
    setSelectedUnit(null);
  };
  const handleDeleteUnit = (unit: any) => {
    const updatedOwners = owners.map(owner => {
      if (owner.id !== unit.ownerId) return owner;
      return { ...owner, units: owner.units.filter(u => u.id !== unit.id) };
    });
    setOwners(updatedOwners);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" onClick={onBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-2xl font-bold tracking-tight">{property.name}</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Objektdetails" : "Property Details"}
            </CardTitle>
            <CardDescription>{property.address}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Einheiten" : "Units"}
                </span>
                <span className="font-medium">{property.units}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Gesamtfläche" : "Total Area"}
                </span>
                <span className="font-medium">{property.totalArea} m²</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Baujahr" : "Year Built"}
                </span>
                <span className="font-medium">{property.yearOfConstruction}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {language === "de" ? "Verteilerschlüssel" : "Distribution Keys"}
                </span>
                <span className="font-medium">{property.distributionKeys.length}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Übersicht" : "Overview"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="flex flex-col items-center justify-center p-4 bg-muted rounded-lg">
                <Users className="h-8 w-8 mb-2 text-primary" />
                <span className="text-xl font-bold">0</span>
                <span className="text-sm text-muted-foreground">
                  {language === "de" ? "Eigentümer" : "Owners"}
                </span>
              </div>

              <div className="flex flex-col items-center justify-center p-4 bg-muted rounded-lg">
                <Home className="h-8 w-8 mb-2 text-primary" />
                <span className="text-xl font-bold">{property.units}</span>
                <span className="text-sm text-muted-foreground">
                  {language === "de" ? "Wohneinheiten" : "Units"}
                </span>
              </div>

              <div className="flex flex-col items-center justify-center p-4 bg-muted rounded-lg">
                <Calendar className="h-8 w-8 mb-2 text-primary" />
                <span className="text-xl font-bold">0</span>
                <span className="text-sm text-muted-foreground">
                  {language === "de" ? "Abrechnungen" : "Statements"}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="distribution-keys" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="distribution-keys">
            {language === "de" ? "Verteilerschlüssel" : "Distribution Keys"}
          </TabsTrigger>
          <TabsTrigger value="owners">
            {language === "de" ? "Eigentümer" : "Owners"}
          </TabsTrigger>
          <TabsTrigger value="units">
            {language === "de" ? "Wohneinheiten" : "Units"}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="distribution-keys" className="space-y-4 mt-4">
          <DistributionKeyManager property={property} onUpdate={onUpdate} />
        </TabsContent>

        <TabsContent value="owners" className="space-y-4 mt-4">
          {selectedOwner ? (
            <OwnerDetail
              owner={selectedOwner}
              properties={[property]}
              onBack={() => setSelectedOwner(null)}
              onUpdate={handleUpdateOwner}
            />
          ) : (
            <OwnerList
              owners={owners}
              properties={[property]}
              onSelectOwner={handleSelectOwner}
              onUpdateOwners={handleUpdateOwners}
            />
          )}
        </TabsContent>

        <TabsContent value="units" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {language === "de" ? "Wohneinheiten" : "Units"}
              </CardTitle>
              <CardDescription>
                {language === "de"
                  ? "Verwalten Sie die Wohneinheiten dieses Objekts."
                  : "Manage the units of this property."}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-end mb-4">
                <Button onClick={() => setIsAddUnitDialogOpen(true)}>
                  + {language === "de" ? "Neue Einheit" : "New Unit"}
                </Button>
              </div>
              {allUnits.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full border rounded-lg">
                    <thead>
                      <tr className="bg-muted">
                        <th className="px-4 py-2 text-left">{language === "de" ? "Eigentümer" : "Owner"}</th>
                        <th className="px-4 py-2 text-left">{language === "de" ? "Einheit" : "Unit"}</th>
                        <th className="px-4 py-2 text-right">{language === "de" ? "Fläche" : "Area"}</th>
                        <th className="px-4 py-2 text-right">{language === "de" ? "Eigentumsanteil" : "Ownership"}</th>
                        <th className="px-4 py-2 text-right"></th>
                      </tr>
                    </thead>
                    <tbody>
                      {allUnits.map(unit => (
                        <tr key={unit.id + unit.ownerId} className="border-b hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                          <td className="px-4 py-2">{unit.ownerName}</td>
                          <td className="px-4 py-2">{unit.unitNumber}</td>
                          <td className="px-4 py-2 text-right">{unit.area} m²</td>
                          <td className="px-4 py-2 text-right">{unit.ownershipPercentage}%</td>
                          <td className="px-4 py-2 text-right">
                            <Button size="sm" variant="outline" onClick={() => handleEditUnit(unit)}>
                              {language === "de" ? "Bearbeiten" : "Edit"}
                            </Button>
                            <Button size="sm" variant="destructive" className="ml-2" onClick={() => handleDeleteUnit(unit)}>
                              {language === "de" ? "Löschen" : "Delete"}
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  {language === "de"
                    ? "Keine Einheiten vorhanden. Fügen Sie eine neue Einheit hinzu."
                    : "No units available. Add a new unit."}
                </div>
              )}
              {/* Add Unit Dialog */}
              <Dialog open={isAddUnitDialogOpen} onOpenChange={open => {
                setIsAddUnitDialogOpen(open);
                if (open && owners.length > 0) {
                  setNewUnit(n => ({ ...n, ownerId: owners[0].id }));
                  setAddUnitError(null);
                }
              }}>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle>{language === "de" ? "Neue Einheit hinzufügen" : "Add New Unit"}</DialogTitle>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <label htmlFor="ownerId" className="text-sm font-medium">{language === "de" ? "Eigentümer" : "Owner"}</label>
                      <select
                        id="ownerId"
                        value={newUnit.ownerId}
                        onChange={e => setNewUnit({ ...newUnit, ownerId: e.target.value })}
                        className="w-full border rounded p-2"
                        disabled={owners.length === 0}
                      >
                        {owners.map(owner => (
                          <option key={owner.id} value={owner.id}>{owner.firstName} {owner.lastName}</option>
                        ))}
                      </select>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="unitNumber" className="text-sm font-medium">{language === "de" ? "Einheit" : "Unit"}</label>
                      <input
                        id="unitNumber"
                        value={newUnit.unitNumber}
                        onChange={e => setNewUnit({ ...newUnit, unitNumber: e.target.value })}
                        className="w-full border rounded p-2"
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="area" className="text-sm font-medium">{language === "de" ? "Fläche (m²)" : "Area (m²)"}</label>
                      <input
                        id="area"
                        type="number"
                        value={newUnit.area}
                        onChange={e => setNewUnit({ ...newUnit, area: parseFloat(e.target.value) || 0 })}
                        className="w-full border rounded p-2"
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="ownershipPercentage" className="text-sm font-medium">{language === "de" ? "Eigentumsanteil (%)" : "Ownership (%)"}</label>
                      <input
                        id="ownershipPercentage"
                        type="number"
                        value={newUnit.ownershipPercentage}
                        onChange={e => setNewUnit({ ...newUnit, ownershipPercentage: parseFloat(e.target.value) || 0 })}
                        className="w-full border rounded p-2"
                      />
                    </div>
                    {addUnitError && (
                      <div className="text-red-500 text-sm mt-2">{addUnitError}</div>
                    )}
                    {owners.length === 0 && (
                      <div className="text-red-500 text-sm mt-2">
                        {language === "de" ? "Bitte fügen Sie zuerst einen Eigentümer hinzu." : "Please add an owner first."}
                      </div>
                    )}
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddUnitDialogOpen(false)}>
                      {language === "de" ? "Abbrechen" : "Cancel"}
                    </Button>
                    <Button onClick={handleAddUnit} disabled={owners.length === 0}>
                      {language === "de" ? "Hinzufügen" : "Add"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              {/* Edit Unit Dialog */}
              <Dialog open={!!selectedUnit} onOpenChange={open => !open && setSelectedUnit(null)}>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle>{language === "de" ? "Einheit bearbeiten" : "Edit Unit"}</DialogTitle>
                  </DialogHeader>
                  {selectedUnit && (
                    <div className="grid gap-4 py-4">
                      <div className="space-y-2">
                        <label htmlFor="edit-unitNumber" className="text-sm font-medium">{language === "de" ? "Einheit" : "Unit"}</label>
                        <input
                          id="edit-unitNumber"
                          value={selectedUnit.unitNumber}
                          onChange={e => setSelectedUnit({ ...selectedUnit, unitNumber: e.target.value })}
                          className="w-full border rounded p-2"
                        />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="edit-area" className="text-sm font-medium">{language === "de" ? "Fläche (m²)" : "Area (m²)"}</label>
                        <input
                          id="edit-area"
                          type="number"
                          value={selectedUnit.area}
                          onChange={e => setSelectedUnit({ ...selectedUnit, area: parseFloat(e.target.value) || 0 })}
                          className="w-full border rounded p-2"
                        />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="edit-ownershipPercentage" className="text-sm font-medium">{language === "de" ? "Eigentumsanteil (%)" : "Ownership (%)"}</label>
                        <input
                          id="edit-ownershipPercentage"
                          type="number"
                          value={selectedUnit.ownershipPercentage}
                          onChange={e => setSelectedUnit({ ...selectedUnit, ownershipPercentage: parseFloat(e.target.value) || 0 })}
                          className="w-full border rounded p-2"
                        />
                      </div>
                    </div>
                  )}
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setSelectedUnit(null)}>
                      {language === "de" ? "Abbrechen" : "Cancel"}
                    </Button>
                    <Button onClick={() => handleSaveEditUnit(selectedUnit)}>
                      {language === "de" ? "Speichern" : "Save"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
