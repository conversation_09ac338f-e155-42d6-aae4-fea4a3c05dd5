"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { AdvisorList } from "./AdvisorList";
import { AdvisorDetail } from "./AdvisorDetail";
import { AdvisorPermissions } from "./AdvisorPermissions";
import { Advisor } from "@/app/lib/wegTypes";

interface AdvisorsTabProps {
  advisors: Advisor[];
  initialAdvisors?: Advisor[];
  onUpdateAdvisors?: (advisors: Advisor[]) => void;
  onRefreshData?: () => void;
}

export function AdvisorsTab({ advisors: propAdvisors, initialAdvisors, onUpdateAdvisors, onRefreshData }: AdvisorsTabProps) {
  const [selectedAdvisor, setSelectedAdvisor] = useState<Advisor | null>(null);
  const [localAdvisors, setLocalAdvisors] = useState<Advisor[]>(initialAdvisors || propAdvisors || []);

  // Verwende entweder die übergebenen Advisors oder die lokalen Advisors
  const advisors = propAdvisors || localAdvisors;

  // Hand<PERSON> für die Auswahl eines Beraters
  const handleSelectAdvisor = (advisor: Advisor) => {
    setSelectedAdvisor(advisor);
  };

  // Handler für die Aktualisierung eines Beraters
  const handleUpdateAdvisor = (updatedAdvisor: Advisor) => {
    const updatedAdvisors = advisors.map((advisor) =>
      advisor.id === updatedAdvisor.id ? updatedAdvisor : advisor
    );

    if (onUpdateAdvisors) {
      onUpdateAdvisors(updatedAdvisors);
    } else {
      setLocalAdvisors(updatedAdvisors);
    }

    setSelectedAdvisor(null);
  };

  // Handler für die Aktualisierung der Advisors-Liste
  const handleUpdateAdvisors = (updatedAdvisors: Advisor[]) => {
    if (onUpdateAdvisors) {
      onUpdateAdvisors(updatedAdvisors);
    } else {
      setLocalAdvisors(updatedAdvisors);
    }
  };

  return (
    <div>
      {selectedAdvisor ? (
        <AdvisorDetail
          advisor={selectedAdvisor}
          onBack={() => setSelectedAdvisor(null)}
          onUpdate={handleUpdateAdvisor}
        />
      ) : (
        <Tabs defaultValue="list" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="list">Steuerberater</TabsTrigger>
            <TabsTrigger value="permissions">Berechtigungen</TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="space-y-4">
            <AdvisorList
              advisors={advisors}
              onSelectAdvisor={handleSelectAdvisor}
              onUpdateAdvisors={handleUpdateAdvisors}
              onRefreshData={onRefreshData}
            />
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <AdvisorPermissions
              advisors={advisors}
              onUpdateAdvisors={handleUpdateAdvisors}
            />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
