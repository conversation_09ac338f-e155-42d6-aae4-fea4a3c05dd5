"use client";

import { useState, useEffect } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Clock,
  Calendar as CalendarIcon,
  BarChart,
  Users,
  Briefcase,
  Plus
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { TimeEntryForm } from "./TimeEntryForm";
import { TimeEntriesList } from "./TimeEntriesList";
import { TimeCalendarView } from "./TimeCalendarView";
import { TimeReportsView } from "./TimeReportsView";
import { TimeProjectsView } from "./TimeProjectsView";
import { LoadingState } from "@/app/components/LoadingState";

export function TimeTrackingDashboard() {
  const { t, language } = useLanguage();
  const [activeTab, setActiveTab] = useState("entries");
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({
    entries: true,
    calendar: false,
    reports: false,
    projects: false
  });
  const [isAddEntryOpen, setIsAddEntryOpen] = useState(false);

  // Set loading state for tabs
  const setTabLoading = (tab: string, loading: boolean) => {
    setIsLoading(prev => ({ ...prev, [tab]: loading }));
  };

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);

    // Set loading state for the tab
    setTabLoading(tab, true);

    // Simulate loading time (shorter for entries, longer for other tabs)
    const loadingTime = tab === 'entries' ? 500 : 1000;
    setTimeout(() => {
      setTabLoading(tab, false);
    }, loadingTime);
  };

  // Load initial data
  useEffect(() => {
    // Simulate initial data loading
    setTimeout(() => {
      setTabLoading('entries', false);
    }, 1000);
  }, []);

  return (
    <div className="flex flex-col space-y-6 p-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          {language === "de" ? "Zeiterfassung" : "Time Tracking"}
        </h1>
        <p className="text-muted-foreground">
          {language === "de" 
            ? "Erfassen und verwalten Sie Ihre Arbeitszeiten und die Ihrer Mitarbeiter." 
            : "Track and manage your work hours and those of your employees."}
        </p>
      </div>

      <div className="flex justify-between items-center">
        <Tabs 
          defaultValue="entries" 
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <div className="flex justify-between items-center mb-4">
            <TabsList>
              <TabsTrigger value="entries" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>{language === "de" ? "Einträge" : "Entries"}</span>
              </TabsTrigger>
              <TabsTrigger value="calendar" className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                <span>{language === "de" ? "Kalender" : "Calendar"}</span>
              </TabsTrigger>
              <TabsTrigger value="reports" className="flex items-center gap-2">
                <BarChart className="h-4 w-4" />
                <span>{language === "de" ? "Berichte" : "Reports"}</span>
              </TabsTrigger>
              <TabsTrigger value="projects" className="flex items-center gap-2">
                <Briefcase className="h-4 w-4" />
                <span>{language === "de" ? "Projekte" : "Projects"}</span>
              </TabsTrigger>
            </TabsList>

            <Button 
              onClick={() => setIsAddEntryOpen(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              <span>{language === "de" ? "Neuer Eintrag" : "New Entry"}</span>
            </Button>
          </div>

          <TabsContent value="entries" className="mt-0">
            {isLoading.entries ? (
              <LoadingState />
            ) : (
              <TimeEntriesList />
            )}
          </TabsContent>

          <TabsContent value="calendar" className="mt-0">
            {isLoading.calendar ? (
              <LoadingState />
            ) : (
              <TimeCalendarView />
            )}
          </TabsContent>

          <TabsContent value="reports" className="mt-0">
            {isLoading.reports ? (
              <LoadingState />
            ) : (
              <TimeReportsView />
            )}
          </TabsContent>

          <TabsContent value="projects" className="mt-0">
            {isLoading.projects ? (
              <LoadingState />
            ) : (
              <TimeProjectsView />
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Time Entry Form Dialog */}
      <TimeEntryForm 
        isOpen={isAddEntryOpen} 
        onClose={() => setIsAddEntryOpen(false)} 
      />
    </div>
  );
}
