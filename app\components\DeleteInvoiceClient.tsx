"use client"

import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import Image from "next/image";
import WarningGif from "@/public/warning-gif.gif";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { SubmitButton } from "@/app/components/SubmitButtons";
import { useLanguage } from '@/app/contexts/LanguageContext';
import { useEffect, useState } from 'react';
import { DeleteInvoice } from "@/app/actions/deleteInvoice";

interface DeleteInvoiceClientProps {
  invoiceId: string;
}

export function DeleteInvoiceClient({ invoiceId }: DeleteInvoiceClientProps) {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="flex flex-1 justify-center items-center">
      <Card className="max-w-[500px]">
        <CardHeader>
          <CardTitle>
            {mounted ? t('deleteInvoice') : "Delete Invoice"}
          </CardTitle>
          <CardDescription>
            {mounted ? t('deleteInvoiceDesc') : "Are you sure that you want to delete this invoice?"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Image src={WarningGif} alt="Warning Gif" className="rounded-lg" />
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <Link
            className={buttonVariants({ variant: "outline" })}
            href="/dashboard/invoices"
          >
            {mounted ? t('cancel') : "Cancel"}
          </Link>
          <form
            action={async () => {
              await DeleteInvoice(invoiceId);
            }}
          >
            <SubmitButton 
              text={mounted ? t('deleteInvoiceButton') : "Delete Invoice"} 
              variant={"destructive"} 
            />
          </form>
        </CardFooter>
      </Card>
    </div>
  );
}
