import { ReactNode } from "react";
import { requireUser } from "../utils/hooks";
import { Toaster } from "@/components/ui/sonner";
import prisma from "../utils/db";
import { redirect } from "next/navigation";
import { DashboardLayoutClient } from "../components/DashboardLayoutClient";
import { Providers } from "../providers";

async function getUser(userId: string) {
  const data = await prisma.user.findUnique({
    where: {
      id: userId,
    },
    select: {
      firstName: true,
      lastName: true,
      address: true,
      settings: true,
    },
  });

  if (!data?.firstName || !data.lastName || !data.address) {
    redirect("/onboarding");
  }

  return data;
}

export default async function DashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  const session = await requireUser();
  const data = await getUser(session.user?.id as string);

  // Get user settings from database or use defaults
  const initialTheme = data.settings?.theme || "system";
  const initialLanguage = data.settings?.language || "en";

  return (
    <Providers initialTheme={initialTheme} initialLanguage={initialLanguage}>
      <DashboardLayoutClient>
        {children}
      </DashboardLayoutClient>
      <Toaster richColors closeButton theme="light" />
    </Providers>
  );
}
