"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useLanguage } from "@/app/contexts/LanguageContext";

export default function ExpensesPage() {
  const { t } = useLanguage();

  // Mock data for expenses with net amounts - updated VAT rate to 20%
  const expensesData = [
    { id: 1, description: "Büromaterial", netAmount: 120, vatRate: 20, date: "2023-05-10" },
    { id: 2, description: "Software-Lizenz", netAmount: 350, vatRate: 20, date: "2023-05-18" },
    { id: 3, description: "Hosting-Gebühren", netAmount: 75, vatRate: 20, date: "2023-05-22" },
  ];

  // Calculate totals
  const totalNet = expensesData.reduce((sum, item) => sum + item.netAmount, 0);
  const totalVat = expensesData.reduce((sum, item) => sum + (item.netAmount * item.vatRate / 100), 0);
  const totalGross = totalNet + totalVat;

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Ausgaben</h1>

      <Card>
        <CardHeader>
          <CardTitle>Ausgabenübersicht</CardTitle>
          <CardDescription>Detaillierte Übersicht aller Ausgaben</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Beschreibung</th>
                  <th className="text-left py-2">Datum</th>
                  <th className="text-right py-2">Netto (Euro)</th>
                  <th className="text-right py-2">USt (Euro)</th>
                  <th className="text-right py-2">Brutto (Euro)</th>
                </tr>
              </thead>
              <tbody>
                {expensesData.map((item) => {
                  const vatAmount = item.netAmount * item.vatRate / 100;
                  const grossAmount = item.netAmount + vatAmount;

                  return (
                    <tr key={item.id} className="border-b hover:bg-slate-50 transition-colors">
                      <td className="py-3">
                        <p className="font-medium">{item.description}</p>
                      </td>
                      <td className="py-3 text-sm text-muted-foreground">{item.date}</td>
                      <td className="py-3 text-right">{item.netAmount.toFixed(2)} €</td>
                      <td className="py-3 text-right">{vatAmount.toFixed(2)} €</td>
                      <td className="py-3 text-right font-semibold text-red-600">{grossAmount.toFixed(2)} €</td>
                    </tr>
                  );
                })}
              </tbody>
              <tfoot>
                <tr className="font-bold">
                  <td colSpan={2} className="pt-4">Gesamt</td>
                  <td className="pt-4 text-right">{totalNet.toFixed(2)} €</td>
                  <td className="pt-4 text-right">{totalVat.toFixed(2)} €</td>
                  <td className="pt-4 text-right text-red-600">{totalGross.toFixed(2)} €</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
