"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import { Employee } from "../lib/definitions";
import { requireUser } from "@/app/utils/hooks";

export async function getEmployees(): Promise<Employee[]> {
  const session = await requireUser();
  
  if (!session?.user) {
    throw new Error("Unauthorized");
  }
  
  try {
    const employees = await prisma.employee.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
    
    return employees;
  } catch (error) {
    console.error("Failed to fetch employees:", error);
    throw new Error("Failed to fetch employees");
  }
}

export async function addEmployee(formData: {
  name: string;
  email: string;
  position: string;
  department: string;
  startDate: string;
  salary: string;
}): Promise<Employee> {
  const session = await requireUser();
  
  if (!session?.user) {
    throw new Error("Unauthorized");
  }
  
  try {
    const employee = await prisma.employee.create({
      data: {
        name: formData.name,
        email: formData.email,
        position: formData.position,
        department: formData.department,
        startDate: new Date(formData.startDate),
        salary: parseFloat(formData.salary),
        user: {
          connect: {
            id: session.user.id,
          },
        },
      },
    });
    
    revalidatePath("/dashboard/employees");
    return employee;
  } catch (error) {
    console.error("Failed to add employee:", error);
    throw new Error("Failed to add employee");
  }
}

export async function updateEmployee(
  id: string,
  formData: {
    name: string;
    email: string;
    position: string;
    department: string;
    startDate: string;
    salary: string;
  }
): Promise<Employee> {
  const session = await requireUser();
  
  if (!session?.user) {
    throw new Error("Unauthorized");
  }
  
  try {
    const employee = await prisma.employee.update({
      where: {
        id,
        userId: session.user.id,
      },
      data: {
        name: formData.name,
        email: formData.email,
        position: formData.position,
        department: formData.department,
        startDate: new Date(formData.startDate),
        salary: parseFloat(formData.salary),
      },
    });
    
    revalidatePath("/dashboard/employees");
    return employee;
  } catch (error) {
    console.error("Failed to update employee:", error);
    throw new Error("Failed to update employee");
  }
}

export async function deleteEmployee(id: string): Promise<void> {
  const session = await requireUser();
  
  if (!session?.user) {
    throw new Error("Unauthorized");
  }
  
  try {
    await prisma.employee.delete({
      where: {
        id,
        userId: session.user.id,
      },
    });
    
    revalidatePath("/dashboard/employees");
  } catch (error) {
    console.error("Failed to delete employee:", error);
    throw new Error("Failed to delete employee");
  }
}
