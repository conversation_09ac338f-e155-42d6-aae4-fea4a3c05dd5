"use client";

import { useState } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Info, Plus, Trash2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

type SpecialExpense = {
  id: string;
  category: string;
  organization: string;
  amount: string;
  date: string;
  hasReceipt: boolean;
};

export function SpecialExpensesStep() {
  const { language } = useLanguage();
  
  // Beispieldaten für Sonderausgaben
  const [specialExpenses, setSpecialExpenses] = useState<SpecialExpense[]>([
    {
      id: "1",
      category: "donations",
      organization: "Rotes Kreuz",
      amount: "200",
      date: "2023-05-20",
      hasReceipt: true,
    },
    {
      id: "2",
      category: "churchContribution",
      organization: "Katholische Kirche",
      amount: "350",
      date: "2023-12-15",
      hasReceipt: true,
    },
  ]);
  
  const [automaticData, setAutomaticData] = useState({
    showAutomaticData: true,
    acceptAutomaticData: true,
  });
  
  const handleSpecialExpenseChange = (id: string, field: keyof SpecialExpense, value: string | boolean) => {
    setSpecialExpenses(
      specialExpenses.map((expense) =>
        expense.id === id ? { ...expense, [field]: value } : expense
      )
    );
  };
  
  const handleAutomaticDataChange = (field: string, value: boolean) => {
    setAutomaticData({
      ...automaticData,
      [field]: value,
    });
  };
  
  const addSpecialExpense = () => {
    const newId = (specialExpenses.length + 1).toString();
    setSpecialExpenses([
      ...specialExpenses,
      {
        id: newId,
        category: "",
        organization: "",
        amount: "",
        date: "",
        hasReceipt: false,
      },
    ]);
  };
  
  const removeSpecialExpense = (id: string) => {
    setSpecialExpenses(specialExpenses.filter((expense) => expense.id !== id));
  };
  
  // Berechne die Summe der Sonderausgaben
  const calculateTotalSpecialExpenses = () => {
    return specialExpenses.reduce((total, expense) => {
      return total + (expense.amount ? parseFloat(expense.amount) : 0);
    }, 0);
  };
  
  return (
    <div className="space-y-6">
      {/* Automatisch übermittelte Daten */}
      <div className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-lg font-medium">
            {language === "de" ? "Automatisch übermittelte Daten" : "Automatically Transmitted Data"}
          </h3>
          <p className="text-sm text-muted-foreground">
            {language === "de"
              ? "Viele Sonderausgaben werden automatisch an das Finanzamt übermittelt und sind in FinanzOnline bereits vorausgefüllt."
              : "Many special expenses are automatically transmitted to the tax office and are pre-filled in FinanzOnline."}
          </p>
        </div>
        
        <Card className="border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-900/20">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 mb-4">
              <Checkbox
                id="showAutomaticData"
                checked={automaticData.showAutomaticData}
                onCheckedChange={(checked) => 
                  handleAutomaticDataChange("showAutomaticData", checked as boolean)
                }
              />
              <Label htmlFor="showAutomaticData" className="cursor-pointer">
                {language === "de" ? "Automatisch übermittelte Daten anzeigen" : "Show Automatically Transmitted Data"}
              </Label>
            </div>
            
            {automaticData.showAutomaticData && (
              <div className="space-y-4">
                <div className="p-4 bg-white dark:bg-gray-800 rounded-md">
                  <h4 className="font-medium mb-2">
                    {language === "de" ? "Folgende Daten wurden automatisch übermittelt:" : "The following data has been automatically transmitted:"}
                  </h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex justify-between">
                      <span>{language === "de" ? "Kirchenbeitrag" : "Church Contribution"}</span>
                      <span className="font-medium">€350,00</span>
                    </li>
                    <li className="flex justify-between">
                      <span>{language === "de" ? "Spenden an begünstigte Organisationen" : "Donations to Eligible Organizations"}</span>
                      <span className="font-medium">€200,00</span>
                    </li>
                    <li className="flex justify-between">
                      <span>{language === "de" ? "Gewerkschaftsbeiträge" : "Union Dues"}</span>
                      <span className="font-medium">€120,00</span>
                    </li>
                  </ul>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="acceptAutomaticData"
                    checked={automaticData.acceptAutomaticData}
                    onCheckedChange={(checked) => 
                      handleAutomaticDataChange("acceptAutomaticData", checked as boolean)
                    }
                  />
                  <Label htmlFor="acceptAutomaticData" className="cursor-pointer">
                    {language === "de" 
                      ? "Ich bestätige, dass die automatisch übermittelten Daten korrekt sind" 
                      : "I confirm that the automatically transmitted data is correct"}
                  </Label>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Manuell einzutragende Sonderausgaben */}
      <div className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-lg font-medium">
            {language === "de" ? "Weitere Sonderausgaben" : "Additional Special Expenses"}
          </h3>
          <p className="text-sm text-muted-foreground">
            {language === "de"
              ? "Gib weitere Sonderausgaben an, die nicht automatisch übermittelt wurden."
              : "Enter additional special expenses that were not automatically transmitted."}
          </p>
        </div>
        
        {specialExpenses.map((expense) => (
          <Card key={expense.id} className="border-gray-200 dark:border-gray-800">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`category-${expense.id}`}>
                    {language === "de" ? "Kategorie" : "Category"}
                  </Label>
                  <Select
                    value={expense.category}
                    onValueChange={(value) => handleSpecialExpenseChange(expense.id, "category", value)}
                  >
                    <SelectTrigger id={`category-${expense.id}`}>
                      <SelectValue placeholder={language === "de" ? "Kategorie auswählen" : "Select category"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="donations">
                        {language === "de" ? "Spenden" : "Donations"}
                      </SelectItem>
                      <SelectItem value="churchContribution">
                        {language === "de" ? "Kirchenbeitrag" : "Church Contribution"}
                      </SelectItem>
                      <SelectItem value="taxConsultingCosts">
                        {language === "de" ? "Steuerberatungskosten" : "Tax Consulting Costs"}
                      </SelectItem>
                      <SelectItem value="voluntaryInsurance">
                        {language === "de" ? "Freiwillige Versicherungen" : "Voluntary Insurance"}
                      </SelectItem>
                      <SelectItem value="housingCreation">
                        {language === "de" ? "Wohnraumschaffung" : "Housing Creation"}
                      </SelectItem>
                      <SelectItem value="other">
                        {language === "de" ? "Sonstiges" : "Other"}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`organization-${expense.id}`}>
                    {language === "de" ? "Organisation/Empfänger" : "Organization/Recipient"}
                  </Label>
                  <Input
                    id={`organization-${expense.id}`}
                    value={expense.organization}
                    onChange={(e) => handleSpecialExpenseChange(expense.id, "organization", e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`amount-${expense.id}`}>
                    {language === "de" ? "Betrag (€)" : "Amount (€)"}
                  </Label>
                  <Input
                    id={`amount-${expense.id}`}
                    value={expense.amount}
                    onChange={(e) => handleSpecialExpenseChange(expense.id, "amount", e.target.value)}
                    type="number"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`date-${expense.id}`}>
                    {language === "de" ? "Datum" : "Date"}
                  </Label>
                  <Input
                    id={`date-${expense.id}`}
                    value={expense.date}
                    onChange={(e) => handleSpecialExpenseChange(expense.id, "date", e.target.value)}
                    type="date"
                  />
                </div>
              </div>
              
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`hasReceipt-${expense.id}`}
                    checked={expense.hasReceipt}
                    onCheckedChange={(checked) => 
                      handleSpecialExpenseChange(expense.id, "hasReceipt", checked as boolean)
                    }
                  />
                  <Label htmlFor={`hasReceipt-${expense.id}`} className="cursor-pointer">
                    {language === "de" ? "Beleg vorhanden" : "Receipt Available"}
                  </Label>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeSpecialExpense(expense.id)}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  {language === "de" ? "Entfernen" : "Remove"}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
        
        <Button
          variant="outline"
          onClick={addSpecialExpense}
          className="mt-2"
        >
          <Plus className="h-4 w-4 mr-2" />
          {language === "de" ? "Weitere Sonderausgaben hinzufügen" : "Add More Special Expenses"}
        </Button>
      </div>
      
      {/* Zusammenfassung */}
      <Card className="border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/20">
        <CardContent className="pt-6">
          <h4 className="font-medium text-green-700 dark:text-green-300 mb-2">
            {language === "de" ? "Zusammenfassung der Sonderausgaben" : "Summary of Special Expenses"}
          </h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>{language === "de" ? "Automatisch übermittelte Daten" : "Automatically Transmitted Data"}</span>
              <span className="font-medium">€670,00</span>
            </div>
            <div className="flex justify-between">
              <span>{language === "de" ? "Manuell eingetragene Daten" : "Manually Entered Data"}</span>
              <span className="font-medium">€{calculateTotalSpecialExpenses().toFixed(2)}</span>
            </div>
            <div className="border-t pt-2 mt-2 flex justify-between font-bold">
              <span>{language === "de" ? "Gesamtsumme" : "Total"}</span>
              <span>€{(670 + calculateTotalSpecialExpenses()).toFixed(2)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* FinanzOnline Hinweis */}
      <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-amber-800 dark:text-amber-300">
              {language === "de" ? "Wo finde ich das in FinanzOnline?" : "Where do I find this in FinanzOnline?"}
            </h4>
            <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
              {language === "de" 
                ? "Sonderausgaben werden in FinanzOnline unter 'Sonderausgaben' eingetragen. Viele Sonderausgaben (z.B. Spenden, Kirchenbeiträge) werden automatisch übermittelt und sind bereits vorausgefüllt. Überprüfe diese Daten auf Richtigkeit und ergänze fehlende Sonderausgaben." 
                : "Special expenses are entered in FinanzOnline under 'Special Expenses'. Many special expenses (e.g., donations, church contributions) are automatically transmitted and pre-filled. Check this data for accuracy and add any missing special expenses."}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
