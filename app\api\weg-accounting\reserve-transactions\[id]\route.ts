import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import prisma from "@/app/utils/db";

export async function POST(request: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { reserveId, date, type, amount, description, documentId } = body;

    // Validate required fields
    if (!reserveId || !date || !type || amount === undefined || !description) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Check if reserve belongs to user
    const reserve = await prisma.reserve.findFirst({
      where: {
        id: reserveId,
        userId: session.user.id,
      },
    });

    if (!reserve) {
      return NextResponse.json({ error: "Reserve not found" }, { status: 404 });
    }

    // Calculate new balance
    let newBalance = reserve.balance;
    switch (type) {
      case "contribution":
      case "interest":
        newBalance += amount;
        break;
      case "withdrawal":
        newBalance -= amount;
        break;
      case "transfer":
        // For transfers, the amount could be positive or negative
        newBalance += amount;
        break;
      default:
        return NextResponse.json({ error: "Invalid transaction type" }, { status: 400 });
    }

    // Create transaction and update reserve balance in a transaction
    const result = await prisma.$transaction([
      prisma.reserveTransaction.create({
        data: {
          reserveId,
          date: new Date(date),
          type,
          amount,
          description,
          documentId: documentId || null,
        },
      }),
      prisma.reserve.update({
        where: {
          id: reserveId,
        },
        data: {
          balance: newBalance,
        },
      }),
    ]);

    // Get updated reserve with transactions
    const updatedReserve = await prisma.reserve.findUnique({
      where: {
        id: reserveId,
      },
      include: {
        transactions: {
          orderBy: {
            date: "desc",
          },
        },
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json(updatedReserve);
  } catch (error) {
    console.error("Failed to create transaction:", error);
    return NextResponse.json({ error: "Failed to create transaction" }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;    // Get transaction and verify ownership
    const transaction = await prisma.reserveTransaction.findFirst({
      where: {
        id: id,
        reserve: {
          userId: session.user.id,
        },
      },
      include: {
        reserve: true,
      },
    });

    if (!transaction || !transaction.reserve) {
      return NextResponse.json({ error: "Transaction not found" }, { status: 404 });
    }

    // Calculate new balance (reverse the transaction)
    let newBalance = transaction.reserve.balance;
    switch (transaction.type) {
      case "contribution":
      case "interest":
        newBalance -= transaction.amount;
        break;
      case "withdrawal":
        newBalance += transaction.amount;
        break;
      case "transfer":
        newBalance -= transaction.amount;
        break;
    }    // Delete transaction and update reserve balance in a transaction
    await prisma.$transaction([
      prisma.reserveTransaction.delete({
        where: {
          id: id,
        },
      }),
      prisma.reserve.update({
        where: {
          id: transaction.reserveId,
        },
        data: {
          balance: newBalance,
        },
      }),
    ]);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete transaction:", error);
    return NextResponse.json({ error: "Failed to delete transaction" }, { status: 500 });
  }
}
