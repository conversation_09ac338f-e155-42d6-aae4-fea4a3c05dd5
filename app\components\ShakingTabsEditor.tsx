"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { useLanguage } from '@/app/contexts/LanguageContext'
import { useToast } from "@/app/context/ToastContext"
import { Edit, Check, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { dashboardLinks } from "./DashboardLinks"

// Definiere die verfügbaren Tabs
const availableTabs = dashboardLinks.map(link => ({
  id: link.nameKey,
  nameKey: link.nameKey,
  icon: link.icon,
  href: link.href
}));

export function ShakingTabsEditor() {
  const { t } = useLanguage();
  const [isEditing, setIsEditing] = useState(false);
  const [selectedTabs, setSelectedTabs] = useState<string[]>([]);
  const [mounted, setMounted] = useState(false);
  const { toast } = useToast();

  // Lade die aktuellen Einstellungen
  useEffect(() => {
    setMounted(true);
    const loadSettings = () => {
      try {
        // Versuche, die Einstellungen aus dem localStorage zu laden
        const savedTabs = localStorage.getItem('visibleTabs');
        if (savedTabs) {
          setSelectedTabs(savedTabs.split(','));
        } else {
          // Standardmäßig alle Tabs auswählen
          setSelectedTabs(availableTabs.map(tab => tab.id));
        }
      } catch (error) {
        console.error('Fehler beim Laden der Tab-Einstellungen:', error);
        // Standardmäßig alle Tabs auswählen
        setSelectedTabs(availableTabs.map(tab => tab.id));
      }
    };

    loadSettings();
  }, []);

  // Toggle Tab-Sichtbarkeit
  const toggleTab = (tabId: string) => {
    // Dashboard und Invoices können nicht deaktiviert werden
    if (tabId === 'dashboard' || tabId === 'invoices') return;

    if (selectedTabs.includes(tabId)) {
      setSelectedTabs(prev => prev.filter(id => id !== tabId));
    } else {
      setSelectedTabs(prev => [...prev, tabId]);
    }
  };

  // Speichern der Einstellungen
  const saveSettings = () => {
    // Stelle sicher, dass Dashboard und Invoices immer enthalten sind
    let tabsToSave = [...selectedTabs];
    if (!tabsToSave.includes('dashboard')) tabsToSave.push('dashboard');
    if (!tabsToSave.includes('invoices')) tabsToSave.push('invoices');

    try {
      // Speichere die Einstellungen im localStorage
      localStorage.setItem('visibleTabs', tabsToSave.join(','));

      toast.success(t('settings'), { description: t('settingsSaved') });

      // Beende den Bearbeitungsmodus
      setIsEditing(false);

      // Lade die Seite neu, um die Änderungen anzuwenden
      window.location.reload();
    } catch (error) {
      console.error('Fehler beim Speichern der Tab-Einstellungen:', error);
      toast.error(t('error'), { description: t('settingsError') });
    }
  };

  // Abbrechen der Bearbeitung
  const cancelEditing = () => {
    // Lade die ursprünglichen Einstellungen
    const savedTabs = localStorage.getItem('visibleTabs');
    if (savedTabs) {
      setSelectedTabs(savedTabs.split(','));
    }

    // Beende den Bearbeitungsmodus
    setIsEditing(false);
  };

  if (!mounted) return null;

  return (
    <div className="relative">
      {!isEditing ? (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsEditing(true)}
          className="absolute bottom-2 right-2"
          title={t('editTabs')}
        >
          <Edit className="h-4 w-4" />
        </Button>
      ) : (
        <div className="absolute bottom-2 right-2 flex space-x-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={cancelEditing}
            title={t('cancel')}
          >
            <X className="h-4 w-4 text-red-500" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={saveSettings}
            title={t('saveChanges')}
          >
            <Check className="h-4 w-4 text-green-500" />
          </Button>
        </div>
      )}

      {isEditing && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center">
          <div className="bg-card p-4 rounded-lg shadow-lg w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">{t('chooseVisibleTabs')}</h3>

            <div className="grid grid-cols-1 gap-2">
              {availableTabs.map((tab) => {
                const isSelected = selectedTabs.includes(tab.id);
                const isFixed = tab.id === 'dashboard' || tab.id === 'invoices';
                const Icon = tab.icon;

                return (
                  <div
                    key={tab.id}
                    className={cn(
                      "flex items-center justify-between p-2 rounded-md cursor-pointer",
                      isSelected ? "bg-primary/10" : "bg-muted",
                      isFixed && "opacity-50 cursor-not-allowed",
                      isEditing && "animate-[shake_0.5s_ease-in-out_infinite]"
                    )}
                    onClick={() => !isFixed && toggleTab(tab.id)}
                  >
                    <div className="flex items-center gap-2">
                      <Icon className="h-4 w-4" />
                      <span>{t(tab.nameKey)}</span>
                      {isFixed && <span className="text-xs text-muted-foreground">({t('alwaysVisible')})</span>}
                    </div>

                    {isSelected ? (
                      <div className="h-4 w-4 rounded-full bg-primary"></div>
                    ) : (
                      <div className="h-4 w-4 rounded-full border border-muted-foreground"></div>
                    )}
                  </div>
                );
              })}
            </div>

            <div className="flex justify-end mt-4 space-x-2">
              <Button variant="outline" onClick={cancelEditing}>
                {t('cancel')}
              </Button>
              <Button onClick={saveSettings}>
                {t('saveChanges')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
