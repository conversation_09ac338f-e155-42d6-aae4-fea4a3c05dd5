"use client";

import { cn } from "@/lib/utils";
import { HomeIcon, Users2, Users, DollarSign, ChevronDown, ArrowUpCircle, ArrowDownCircle, Sparkles, FileText, Building, Clock } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useLanguage } from '@/app/contexts/LanguageContext';
import { useEffect, useState } from 'react';
import { getUserSettings } from '../actions/settings';

import { TranslationKey } from "@/i18n";

export const dashboardLinks = [
  {
    id: 0,
    nameKey: "dashboard" as TranslationKey,
    href: "/dashboard",
    icon: HomeIcon,
  },
  {
    id: 1,
    nameKey: "invoices" as TranslationKey,
    href: "/dashboard/invoices",
    icon: Users2,
  },
  {
    id: 2,
    nameKey: "employees" as TranslationKey,
    href: "/dashboard/employees",
    icon: Users,
  },
  {
    id: 3,
    nameKey: "income-expenses" as TranslationKey,
    href: "/dashboard/income-expenses",
    icon: DollarSign,
  },
  {
    id: 4,
    nameKey: "ai" as <PERSON><PERSON><PERSON>,
    href: "/dashboard/ai",
    icon: Sparkles,
  },
  {
    id: 5,
    nameKey: "taxReturn" as TranslationKey,
    href: "/dashboard/tax-return",
    icon: FileText,
  },
  {
    id: 6,
    nameKey: "wegAccounting" as TranslationKey,
    href: "/weg-accounting/dashboard",
    icon: Building,
  },
  {
    id: 7,
    nameKey: "timeTracking" as TranslationKey,
    href: "/dashboard/time-tracking",
    icon: Clock,
  },
];

export function DashboardLinks() {
  const pathname = usePathname();
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);
  const [financesOpen, setFinancesOpen] = useState(false);
  const [visibleTabs, setVisibleTabs] = useState<TranslationKey[]>(['dashboard', 'invoices'] as TranslationKey[]);
  const [isLoading, setIsLoading] = useState(true);

  // Lade die Benutzereinstellungen beim Mounten der Komponente
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // Versuche zuerst, die Einstellungen aus dem localStorage zu laden
        const savedTabs = localStorage.getItem('visibleTabs');
        if (savedTabs) {
          const tabsArray = savedTabs.split(',') as TranslationKey[];
          // Stelle sicher, dass Dashboard und Invoices immer sichtbar sind
          if (!tabsArray.includes('dashboard' as TranslationKey)) tabsArray.push('dashboard' as TranslationKey);
          if (!tabsArray.includes('invoices' as TranslationKey)) tabsArray.push('invoices' as TranslationKey);
          setVisibleTabs(tabsArray);
        } else {
          // Versuche, die Einstellungen aus der Datenbank zu laden
          const settings = await getUserSettings();
          if (settings?.visibleTabs) {
            // Stelle sicher, dass Dashboard und Invoices immer sichtbar sind
            const tabsArray = settings.visibleTabs.split(',') as TranslationKey[];
            if (!tabsArray.includes('dashboard' as TranslationKey)) tabsArray.push('dashboard' as TranslationKey);
            if (!tabsArray.includes('invoices' as TranslationKey)) tabsArray.push('invoices' as TranslationKey);
            setVisibleTabs(tabsArray);
          }
        }
      } catch (error) {
        console.error('Fehler beim Laden der Tab-Einstellungen:', error);
      } finally {
        setIsLoading(false);
        setMounted(true);
      }
    };

    loadSettings();
  }, []);

  return (
    <>
      {isLoading ? (
        // Zeige Ladezustand an
        <div className="flex flex-col space-y-2 px-3 py-2">
          <div className="h-8 w-full animate-pulse rounded-lg bg-muted"></div>
          <div className="h-8 w-full animate-pulse rounded-lg bg-muted"></div>
        </div>
      ) : (
        // Filtere die Links basierend auf den sichtbaren Tabs
        dashboardLinks
          .filter(link => visibleTabs.includes(link.nameKey))
          .map((link) => {
            const linkName = mounted ? t(link.nameKey) :
              link.nameKey === "dashboard" ? "Dashboard" :
              link.nameKey === "invoices" ? "Rechnungen" :
              link.nameKey === "employees" ? "Mitarbeiter" :
              link.nameKey === "income-expenses" ? "Einnahmen & Ausgaben" :
              link.nameKey === "ai" ? "KI-Assistent" :
              link.nameKey === "taxReturn" ? "Steuerausgleich" :
              link.nameKey === "wegAccounting" ? "WEG-Abrechnung" :
              link.nameKey === "timeTracking" ? "Zeiterfassung" : "";

        // Special handling for income-expenses link
        if (link.nameKey === "income-expenses") {
          return (
            <div key={link.id} className="flex flex-col">
              <button
                className={cn(
                  pathname?.startsWith(link.href)
                    ? "text-primary bg-primary/10"
                    : "text-muted-foreground hover:text-foreground",
                  "flex items-center justify-between gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary"
                )}
                onClick={() => setFinancesOpen(!financesOpen)}
              >
                <div className="flex items-center gap-3">
                  <link.icon className="size-4" />
                  {linkName}
                </div>
                <ChevronDown className={cn("size-4 transition-transform", financesOpen && "rotate-180")} />
              </button>

              {financesOpen && (
                <div className="ml-6 mt-1 flex flex-col space-y-1">
                  <Link
                    href="/dashboard/income"
                    className={cn(
                      pathname?.startsWith("/dashboard/income")
                        ? "text-primary bg-primary/10"
                        : "text-muted-foreground hover:text-foreground",
                      "flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary"
                    )}
                  >
                    <ArrowUpCircle className="size-4 text-green-500" />
                    Einnahmen
                  </Link>
                  <Link
                    href="/dashboard/expenses"
                    className={cn(
                      pathname?.startsWith("/dashboard/expenses")
                        ? "text-primary bg-primary/10"
                        : "text-muted-foreground hover:text-foreground",
                      "flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary"
                    )}
                  >
                    <ArrowDownCircle className="size-4 text-red-500" />
                    Ausgaben
                  </Link>
                </div>
              )}
            </div>
          );
        }

        // Regular links
        return (
          <Link
            className={cn(
              pathname?.startsWith(link.href)
                ? "text-primary bg-primary/10"
                : "text-muted-foreground hover:text-foreground",
              "flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary"
            )}
            href={link.href}
            key={link.id}
          >
            <link.icon className="size-4" />
            {link.nameKey === "ai" ? (
              <div className="flex items-center gap-2">
                {linkName}
                <span className="px-1.5 py-0.5 text-[10px] font-semibold rounded bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                  BETA
                </span>
              </div>
            ) : (
              linkName
            )}
          </Link>
        );
      }))}
    </>
  );
}
