"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Info, CheckCircle, AlertTriangle, Download, ExternalLink } from "lucide-react";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";

export function SummaryStep() {
  const { language } = useLanguage();

  const [confirmations, setConfirmations] = useState({
    dataComplete: false,
    dataCorrect: false,
    termsAccepted: false,
  });

  const [notes, setNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleConfirmationChange = (field: string, value: boolean) => {
    setConfirmations({
      ...confirmations,
      [field]: value,
    });
  };

  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNotes(e.target.value);
  };

  const handleSubmit = () => {
    setIsSubmitting(true);

    // Simuliere eine Übermittlung an FinanzOnline
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
    }, 2000);
  };

  const allConfirmed = confirmations.dataComplete && confirmations.dataCorrect && confirmations.termsAccepted;

  // Beispieldaten für die Zusammenfassung
  const summaryData = {
    income: {
      totalIncome: "42000",
      taxWithheld: "12600",
      socialInsurance: "7560",
    },
    expenses: {
      workRelated: "2676",
      specialExpenses: "670",
      extraordinaryBurdens: "800",
    },
    taxCredits: {
      trafficCredit: "400",
      familyBonus: "4000",
      commuterCredit: "70",
    },
    result: {
      taxRefund: "1842",
    },
  };

  return (
    <div className="space-y-6">
      {!isSubmitted ? (
        <>
          <div className="space-y-2">
            <h3 className="text-lg font-medium">
              {language === "de" ? "Zusammenfassung deiner Arbeitnehmerveranlagung" : "Summary of Your Tax Return"}
            </h3>
            <p className="text-sm text-muted-foreground">
              {language === "de"
                ? "Überprüfe deine Angaben und reiche deine Arbeitnehmerveranlagung ein."
                : "Review your information and submit your tax return."}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Einkünfte */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">
                  {language === "de" ? "Einkünfte" : "Income"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Gesamteinkommen" : "Total Income"}
                    </span>
                    <span>€{summaryData.income.totalIncome}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Einbehaltene Lohnsteuer" : "Tax Withheld"}
                    </span>
                    <span>€{summaryData.income.taxWithheld}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Sozialversicherung" : "Social Insurance"}
                    </span>
                    <span>€{summaryData.income.socialInsurance}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Ausgaben */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">
                  {language === "de" ? "Ausgaben" : "Expenses"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Werbungskosten" : "Work-Related Expenses"}
                    </span>
                    <span>€{summaryData.expenses.workRelated}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Sonderausgaben" : "Special Expenses"}
                    </span>
                    <span>€{summaryData.expenses.specialExpenses}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Außergewöhnliche Belastungen" : "Extraordinary Burdens"}
                    </span>
                    <span>€{summaryData.expenses.extraordinaryBurdens}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Absetzbeträge */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">
                  {language === "de" ? "Absetzbeträge" : "Tax Credits"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Verkehrsabsetzbetrag" : "Traffic Tax Credit"}
                    </span>
                    <span>€{summaryData.taxCredits.trafficCredit}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Familienbonus Plus" : "Family Bonus Plus"}
                    </span>
                    <span>€{summaryData.taxCredits.familyBonus}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Pendlereuro" : "Commuter Euro"}
                    </span>
                    <span>€{summaryData.taxCredits.commuterCredit}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Ergebnis */}
            <Card className="border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/20">
              <CardHeader className="pb-2">
                <CardTitle className="text-base text-green-700 dark:text-green-300">
                  {language === "de" ? "Voraussichtliches Ergebnis" : "Expected Result"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <div className="flex justify-between font-bold">
                    <span className="text-green-700 dark:text-green-300">
                      {language === "de" ? "Steuerrückerstattung" : "Tax Refund"}
                    </span>
                    <span className="text-green-700 dark:text-green-300">€{summaryData.result.taxRefund}</span>
                  </div>
                  <p className="text-xs text-green-600 dark:text-green-400 mt-2">
                    {language === "de"
                      ? "Dies ist eine Schätzung. Der tatsächliche Betrag kann abweichen."
                      : "This is an estimate. The actual amount may vary."}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Bestätigungen */}
          <Card>
            <CardHeader>
              <CardTitle>
                {language === "de" ? "Bestätigungen" : "Confirmations"}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="dataComplete"
                  checked={confirmations.dataComplete}
                  onCheckedChange={(checked) =>
                    handleConfirmationChange("dataComplete", checked as boolean)
                  }
                  className="mt-1"
                />
                <Label htmlFor="dataComplete" className="cursor-pointer">
                  {language === "de"
                    ? "Ich bestätige, dass ich alle relevanten Informationen angegeben habe."
                    : "I confirm that I have provided all relevant information."}
                </Label>
              </div>

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="dataCorrect"
                  checked={confirmations.dataCorrect}
                  onCheckedChange={(checked) =>
                    handleConfirmationChange("dataCorrect", checked as boolean)
                  }
                  className="mt-1"
                />
                <Label htmlFor="dataCorrect" className="cursor-pointer">
                  {language === "de"
                    ? "Ich bestätige, dass alle angegebenen Informationen korrekt und wahrheitsgemäß sind."
                    : "I confirm that all information provided is correct and truthful."}
                </Label>
              </div>

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="termsAccepted"
                  checked={confirmations.termsAccepted}
                  onCheckedChange={(checked) =>
                    handleConfirmationChange("termsAccepted", checked as boolean)
                  }
                  className="mt-1"
                />
                <Label htmlFor="termsAccepted" className="cursor-pointer">
                  {language === "de"
                    ? "Ich akzeptiere die Bedingungen für die elektronische Einreichung der Arbeitnehmerveranlagung."
                    : "I accept the terms for electronic submission of the tax return."}
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Notizen */}
          <Card>
            <CardHeader>
              <CardTitle>
                {language === "de" ? "Notizen für das Finanzamt (optional)" : "Notes for the Tax Office (optional)"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={notes}
                onChange={handleNotesChange}
                placeholder={
                  language === "de"
                    ? "Hier kannst du zusätzliche Informationen für das Finanzamt angeben..."
                    : "You can provide additional information for the tax office here..."
                }
                className="min-h-[100px]"
              />
            </CardContent>
          </Card>

          {/* Einreichen */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
                  <div className="flex items-start">
                    <Info className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-amber-800 dark:text-amber-300">
                        {language === "de" ? "Wichtiger Hinweis" : "Important Note"}
                      </h4>
                      <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                        {language === "de"
                          ? "In einer echten Anwendung würdest du jetzt zu FinanzOnline weitergeleitet werden, um deine Arbeitnehmerveranlagung einzureichen. Für diese Demo wird die Einreichung simuliert."
                          : "In a real application, you would now be redirected to FinanzOnline to submit your tax return. For this demo, the submission is simulated."}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-between">
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    {language === "de" ? "Als PDF speichern" : "Save as PDF"}
                  </Button>

                  <Button
                    onClick={handleSubmit}
                    disabled={!allConfirmed || isSubmitting}
                    className="flex items-center gap-2"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        {language === "de" ? "Wird eingereicht..." : "Submitting..."}
                      </>
                    ) : (
                      <>
                        <ExternalLink className="h-4 w-4" />
                        {language === "de" ? "Bei FinanzOnline einreichen" : "Submit to FinanzOnline"}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      ) : (
        // Erfolgsseite nach Einreichung
        <Card className="border-green-200 dark:border-green-800">
          <CardContent className="pt-6 pb-4 text-center">
            <div className="flex justify-center mb-4">
              <div className="h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <h3 className="text-xl font-medium text-green-700 dark:text-green-300 mb-2">
              {language === "de" ? "Arbeitnehmerveranlagung erfolgreich eingereicht!" : "Tax Return Successfully Submitted!"}
            </h3>
            <p className="text-green-600 dark:text-green-400 mb-6">
              {language === "de"
                ? "Deine Arbeitnehmerveranlagung wurde erfolgreich an FinanzOnline übermittelt."
                : "Your tax return has been successfully transmitted to FinanzOnline."}
            </p>

            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-md text-left mb-6">
              <h4 className="font-medium text-green-700 dark:text-green-300 mb-2">
                {language === "de" ? "Nächste Schritte" : "Next Steps"}
              </h4>
              <ul className="space-y-2 text-sm text-green-600 dark:text-green-400">
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>
                    {language === "de"
                      ? "Deine Arbeitnehmerveranlagung wird vom Finanzamt bearbeitet. Dies kann einige Wochen dauern."
                      : "Your tax return will be processed by the tax office. This may take a few weeks."}
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>
                    {language === "de"
                      ? "Du erhältst eine Benachrichtigung, sobald dein Steuerbescheid verfügbar ist."
                      : "You will receive a notification once your tax assessment is available."}
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>
                    {language === "de"
                      ? "Die Steuerrückerstattung wird auf das angegebene Konto überwiesen."
                      : "The tax refund will be transferred to the specified account."}
                  </span>
                </li>
              </ul>
            </div>

            <Separator className="my-6" />

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                {language === "de" ? "Bestätigung herunterladen" : "Download Confirmation"}
              </Button>

              <Button className="flex items-center gap-2">
                <ExternalLink className="h-4 w-4" />
                {language === "de" ? "Zu FinanzOnline" : "Go to FinanzOnline"}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* FinanzOnline Hinweis */}
      {!isSubmitted && (
        <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-amber-800 dark:text-amber-300">
                {language === "de" ? "Wichtige Information" : "Important Information"}
              </h4>
              <p className="text-sm text-amber-700 dark:text-amber-400 mt-1">
                {language === "de"
                  ? "Bewahre alle Belege für 7 Jahre auf. Das Finanzamt kann diese jederzeit anfordern. Falsche Angaben können zu Strafen führen."
                  : "Keep all receipts for 7 years. The tax office may request them at any time. False information can lead to penalties."}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
