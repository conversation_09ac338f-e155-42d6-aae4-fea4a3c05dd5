import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import prisma from "@/app/utils/db";
import { NextRequest } from "next/server";

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();

    try {
      // Prüfen, ob der Berater dem Benutzer gehört
      const existingAdvisor = await prisma.advisor.findFirst({
        where: {
          id,
          userId: session.user.id,
        },
      });

      if (!existingAdvisor) {
        return NextResponse.json({ error: "Advisor not found" }, { status: 404 });
      }

      // Berater aktualisieren
      const updatedAdvisor = await prisma.advisor.update({
        where: {
          id,
        },
        data: {
          name: body.name,
          email: body.email,
          phone: body.phone,
          company: body.company,
          notes: body.notes,
        },
      });

      return NextResponse.json(updatedAdvisor);
    } catch (dbError) {
      console.error("Database error when updating advisor:", dbError);
      return NextResponse.json({ error: "Failed to update advisor" }, { status: 500 });
    }
  } catch (error) {
    console.error("Failed to update advisor:", error);
    return NextResponse.json({ error: "Failed to update advisor" }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;

    try {
      // Prüfen, ob der Berater dem Benutzer gehört
      const advisor = await prisma.advisor.findFirst({
        where: {
          id,
          userId: session.user.id,
        },
      });

      if (!advisor) {
        return NextResponse.json({ error: "Advisor not found" }, { status: 404 });
      }

      // Berater löschen
      await prisma.advisor.delete({
        where: {
          id,
        },
      });

      return NextResponse.json({ success: true });
    } catch (dbError) {
      console.error("Database error when deleting advisor:", dbError);
      return NextResponse.json({ error: "Failed to delete advisor" }, { status: 500 });
    }
  } catch (error) {
    console.error("Failed to delete advisor:", error);
    return NextResponse.json({ error: "Failed to delete advisor" }, { status: 500 });
  }
}

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  // ... existing code ...
}

export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  // ... existing code ...
}
