import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/utils/db";
import { requireUser } from "@/app/utils/hooks";

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await requireUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const invoiceData = await request.json();

    // Update the expense
    const expense = await prisma.expense.update({
      where: {
        id,
        accountingPeriod: {
          userId: session.user.id,
        },
      },
      data: {
        categoryId: invoiceData.categoryId,
        description: `${invoiceData.vendorName} - ${invoiceData.description}`,
        amount: parseFloat(invoiceData.amount),
        date: new Date(invoiceData.date),
      },
      include: {
        category: true,
        accountingPeriod: {
          include: {
            property: true,
          },
        },
      },
    });

    // Transform to match the expected Invoice interface
    const invoice = {
      id: expense.id,
      propertyId: expense.accountingPeriod.propertyId,
      number: `RE-${expense.date.getFullYear()}-${expense.id.slice(-3)}`,
      date: expense.date.toISOString().split("T")[0],
      dueDate: new Date(expense.date.getTime() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      vendorName: invoiceData.vendorName,
      description: invoiceData.description,
      amount: expense.amount,
      status: invoiceData.status || "pending",
      categoryId: expense.categoryId,
      notes: invoiceData.notes || "",
    };

    return NextResponse.json(invoice);
  } catch (error) {
    console.error("Failed to update invoice:", error);
    return NextResponse.json(
      { error: "Failed to update invoice" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const session = await requireUser();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Delete the expense
    await prisma.expense.delete({
      where: {
        id,
        accountingPeriod: {
          userId: session.user.id,
        },
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete invoice:", error);
    return NextResponse.json(
      { error: "Failed to delete invoice" },
      { status: 500 }
    );
  }
}
