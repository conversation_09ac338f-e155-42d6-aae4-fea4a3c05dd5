"use client"

import { useLanguage } from "@/app/contexts/LanguageContext"
import { Globe, AlertCircle, FileQuestion, Languages } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function LanguageDisplay() {
  const { language, t } = useLanguage()
  
  const examples = [
    {
      icon: <Globe className="h-5 w-5 text-blue-500" />,
      label: t('currentLanguage'),
      value: language === 'de' ? 'Deutsch' : 'English',
      badge: language.toUpperCase()
    },
    {
      icon: <AlertCircle className="h-5 w-5 text-orange-500" />,
      label: t('error'),
      value: t('error')
    },
    {
      icon: <FileQuestion className="h-5 w-5 text-purple-500" />,
      label: "404 Message",
      value: t('pageNotFound')
    }
  ]
  
  return (
    <Card className="overflow-hidden border-0 shadow-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="flex items-center gap-2">
          <Languages className="h-6 w-6" />
          <CardTitle className="text-xl">Language Settings</CardTitle>
        </div>
        <CardDescription className="text-blue-100">
          Current translation examples
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-4">
          {examples.map((example, index) => (
            <div 
              key={index}
              className="flex items-center justify-between p-4 rounded-lg bg-white dark:bg-slate-800 shadow-sm hover:shadow-md transition-all duration-200 border border-slate-200 dark:border-slate-700"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-slate-50 dark:bg-slate-900">
                  {example.icon}
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400">
                    {example.label}
                  </p>
                  <p className="text-base font-semibold text-slate-900 dark:text-slate-100">
                    {example.value}
                  </p>
                </div>
              </div>
              {example.badge && (
                <Badge variant="secondary" className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
                  {example.badge}
                </Badge>
              )}
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-4 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900">
              <Globe className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                Pro Tip
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                {language === 'de' 
                  ? 'Sie können die Sprache jederzeit über den Sprachumschalter im Header ändern.'
                  : 'You can change the language anytime using the language switcher in the header.'}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
