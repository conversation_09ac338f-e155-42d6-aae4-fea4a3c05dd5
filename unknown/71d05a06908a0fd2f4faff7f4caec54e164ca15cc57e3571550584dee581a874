import { PrismaClient } from "@prisma/client";

// Check if we are in a production environment
const isProduction = process.env.NODE_ENV === "production";

// Create a global variable for PrismaClient
const globalForPrisma = global as unknown as {
  prisma: PrismaClient | undefined;
};

// Initialize PrismaClient
const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: isProduction ? ["error"] : ["query", "error", "warn"],
});

// Save PrismaClient to the global object in development
if (!isProduction) globalForPrisma.prisma = prisma;

export default prisma;
