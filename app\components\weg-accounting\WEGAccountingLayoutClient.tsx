"use client"

import { <PERSON>actNode, useState } from "react";
import Link from "next/link";
import Logo from "@/public/logo.png";
import Image from "next/image";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Menu, Settings, User2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LanguageSwitcher } from '../LanguageSwitcher'
import { useLanguage } from '@/app/contexts/LanguageContext'
import { ThemeSwitcher } from "@/components/ui/ThemeSwitcher";
import { logout } from "../../actions/auth";
import { WEGAccountingLinks } from "./WEGAccountingLinks";

export function WEGAccountingLayoutClient({
  children,
}: {
  children: ReactNode;
}) {
  const { language } = useLanguage();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    await logout();
  };

  return (
    <div className="grid min-h-screen w-full md:gird-cols-[220px_1fr] lg:grid-cols-[280px_1fr]">
      <div className="hidden border-r bg-muted/40 md:block">
        <div className="flex flex-col max-h-screen h-full gap-2">
          <div className="h-14 flex items-center border-b px-4 lg:h-[60px] lg:px-6">
            <Link href="/weg-accounting" className="flex items-center gap-2">
              <Image src={Logo} alt="Logo" className="size-7" />
              <p className="text-2xl font-bold">
                WEG<span className="text-blue-600">Abrechnung</span>
              </p>
            </Link>
          </div>
          <div className="flex-1">
            <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
              <WEGAccountingLinks />
            </nav>
          </div>
        </div>
      </div>

      <div className="flex flex-col">
        <header className="flex h-14 items-center gap-4 border-b bg-muted/40 px-4 lg:h-[60px] lg:px-6">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="md:hidden">
                <Menu className="size-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="flex flex-col h-full">
              <nav className="grid gap-2 mt-10 flex-1">
                <WEGAccountingLinks />
              </nav>
            </SheetContent>
          </Sheet>

          <div className="flex items-center ml-auto gap-4">
            {/* <LanguageSwitcher /> */}
            <ThemeSwitcher />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <User2 className="size-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>
                  {language === "de" ? "Mein Konto" : "My Account"}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard">
                    {language === "de" ? "Zum Hauptdashboard" : "Main Dashboard"}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/settings">
                    <Settings className="size-4 mr-2" />
                    {language === "de" ? "Einstellungen" : "Settings"}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  disabled={isLoggingOut}
                  onClick={handleLogout}
                  className="text-red-500 cursor-pointer"
                >
                  {language === "de" ? "Abmelden" : "Logout"}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        <main className="flex-1 p-4 md:p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
