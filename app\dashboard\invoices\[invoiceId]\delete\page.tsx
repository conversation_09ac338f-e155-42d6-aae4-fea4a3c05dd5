import prisma from "@/app/utils/db";
import { requireUser } from "@/app/utils/hooks";
import { redirect } from "next/navigation";
import { DeleteInvoiceClient } from "@/app/components/DeleteInvoiceClient";

async function Authorize(invoiceId: string, userId: string) {
  const data = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
      userId: userId,
    },
  });

  if (!data) {
    return redirect("/dashboard/invoices");
  }
}

type Params = { invoiceId: string };

export default async function DeleteInvoiceRoute({
  params,
}: {
  params: Promise<Params>;
}) {
  const session = await requireUser();
  const { invoiceId } = await params;
  await Authorize(invoiceId, session.user?.id as string);
  
  return <DeleteInvoiceClient invoiceId={invoiceId} />;
}
