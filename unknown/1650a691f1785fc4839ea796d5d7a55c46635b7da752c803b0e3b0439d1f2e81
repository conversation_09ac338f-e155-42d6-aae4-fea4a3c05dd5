"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Providers } from "../../providers";
import Link from "next/link";
import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2 } from "lucide-react";

export default function WEGAccountingLogin() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get("callbackUrl") || "/weg-accounting/dashboard";
  const error = searchParams?.get("error");
  const verified = searchParams?.get("verified") === "true";
  const resetSuccess = searchParams?.get("reset") === "success";

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(error || null);
  const [verificationSuccess, setVerificationSuccess] = useState(verified);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setLoginError(null);

    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        if (result.error === "email-not-verified") {
          setLoginError("Bitte bestätigen Sie zuerst Ihre E-Mail-Adresse.");
        } else {
          setLoginError("Ungültige E-Mail oder Passwort.");
        }
        setIsLoading(false);
        return;
      }

      // Erfolgreich eingeloggt, Weiterleitung zum Dashboard
      router.push(callbackUrl);
    } catch (error) {
      console.error("Login error:", error);
      setLoginError("Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.");
      setIsLoading(false);
    }
  };

  return (
    <Providers>
      <div className="absolute inset-0 -z-10 h-full w-full bg-white bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] bg-[size:6rem_4rem] dark:bg-gray-950 dark:bg-[linear-gradient(to_right,#1f1f1f_1px,transparent_1px),linear-gradient(to_bottom,#1f1f1f_1px,transparent_1px)]">
        <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(circle_500px_at_50%_200px,#C9EBFF,transparent)] dark:bg-[radial-gradient(circle_500px_at_50%_200px,#193548,transparent)]"></div>
      </div>
      <div className="flex h-screen w-full items-center justify-center px-4">
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-4">
              <Link href="/weg-accounting" className="text-2xl font-bold">
                WEG<span className="text-blue-500">Abrechnung</span>
              </Link>
            </div>
            <CardTitle className="text-2xl text-center">Anmelden</CardTitle>
            <CardDescription className="text-center">
              Geben Sie Ihre Anmeldedaten ein, um auf Ihr WEG-Accounting zuzugreifen
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loginError && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Fehler</AlertTitle>
                <AlertDescription>{loginError}</AlertDescription>
              </Alert>
            )}

            {verificationSuccess && (
              <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertTitle>E-Mail bestätigt!</AlertTitle>
                <AlertDescription>
                  Ihre E-Mail-Adresse wurde erfolgreich bestätigt. Sie können sich jetzt anmelden.
                </AlertDescription>
              </Alert>
            )}

            {resetSuccess && (
              <Alert className="mb-4 bg-green-50 text-green-800 border-green-200">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertTitle>Passwort erfolgreich zurückgesetzt!</AlertTitle>
                <AlertDescription>
                  Ihr Passwort wurde erfolgreich geändert. Sie können sich jetzt mit Ihrem neuen Passwort anmelden.
                </AlertDescription>
              </Alert>
            )}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">E-Mail</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={isLoading}
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Passwort</Label>
                  <Link
                    href="/weg-accounting/reset-password"
                    className="text-sm text-blue-500 hover:text-blue-600"
                  >
                    Passwort vergessen?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={isLoading}
                />
              </div>
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "Wird angemeldet..." : "Anmelden"}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <div className="text-center text-sm">
              Noch kein Konto?{" "}
              <Link
                href="/weg-accounting/register"
                className="text-blue-500 hover:text-blue-600 font-medium"
              >
                Jetzt registrieren
              </Link>
            </div>
            <div className="text-center text-xs text-muted-foreground">
              Mit der Anmeldung akzeptieren Sie unsere{" "}
              <Link href="/terms" className="underline underline-offset-4 hover:text-primary">
                Nutzungsbedingungen
              </Link>{" "}
              und{" "}
              <Link href="/privacy" className="underline underline-offset-4 hover:text-primary">
                Datenschutzrichtlinie
              </Link>
              .
            </div>
          </CardFooter>
        </Card>
      </div>
    </Providers>
  );
}
