"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { EmployeeForm } from "./EmployeeForm";
import { EmployeeList } from "./EmployeeList";
import { Employee } from "@/app/lib/definitions";
import { useLanguage } from '@/app/contexts/LanguageContext'
import { TranslationKey } from "@/i18n";

export function EmployeesClient({
  initialEmployees,
}: {
  initialEmployees: Employee[];
}) {
  const { t } = useLanguage();
  const [mounted, setMounted] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [employees, setEmployees] = useState<Employee[]>(initialEmployees);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleEmployeeAdded = (newEmployee: Employee) => {
    setEmployees((prev) => [...prev, newEmployee]);
    setShowForm(false);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">
          {mounted ? t('employees' as TranslationKey) : "Mitarbeiter"}
        </h1>
        <Button onClick={() => setShowForm(!showForm)}>
          <Plus className="mr-2 h-4 w-4" />
          {mounted ? t('addEmployee' as TranslationKey) : "Mitarbeiter hinzufügen"}
        </Button>
      </div>

      {showForm && (
        <Card className="border-[1px] border-border shadow-md">
          <CardHeader className="pb-2">
            <CardTitle>{mounted ? t('addNewEmployee' as TranslationKey) : "Neuen Mitarbeiter hinzufügen"}</CardTitle>
            <CardDescription>
              {mounted ? t('fillEmployeeDetails' as TranslationKey) : "Füllen Sie die Details aus, um einen neuen Mitarbeiter hinzuzufügen"}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            <EmployeeForm onEmployeeAdded={handleEmployeeAdded} onCancel={() => setShowForm(false)} />
          </CardContent>
        </Card>
      )}

      <EmployeeList employees={employees} />
    </div>
  );
}
