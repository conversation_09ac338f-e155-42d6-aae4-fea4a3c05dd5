"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Send, Bot, User } from "lucide-react";
import { useState, useEffect } from "react";
import { useLanguage } from '@/app/contexts/LanguageContext'

type Message = {
  role: "user" | "assistant";
  content: string;
};

export function AIChat() {
  const { language } = useLanguage();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Initialisiere die Nachrichten mit der Willkommensnachricht
  useEffect(() => {
    const welcomeMessage = language === "de"
      ? "Hallo! Ich bin Ihr KI-Steuerassistent. Wie kann ich Ihnen heute helfen?"
      : "Hello! I'm your AI tax assistant. How can I help you today?";

    setMessages([{
      role: "assistant",
      content: welcomeMessage
    }]);
  }, [language]);

  const handleSendMessage = async () => {
    if (!input.trim()) return;

    // Add user message to chat
    const userMessage: Message = { role: "user", content: input };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    // Simulate AI response (in a real app, this would call an API)
    setTimeout(() => {
      // Different responses based on language
      const aiResponses = language === "de" ? [
        "Ich kann Ihnen helfen, Ihre Steuersituation anhand Ihrer Geschäftsdaten zu optimieren.",
        "Basierend auf Ihren Rechnungen könnten Sie Anspruch auf zusätzliche Abzüge haben.",
        "Haben Sie darüber nachgedacht, Geld für Ihre Steuerzahlungen zurückzulegen?",
        "Ich kann Ihre Einnahmen und Ausgaben analysieren, um steuerliche Einsparungsstrategien vorzuschlagen.",
        "Möchten Sie, dass ich Ihnen erkläre, wie die Mehrwertsteuer für Ihren Unternehmenstyp funktioniert?",
      ] : [
        "I can help you optimize your tax situation based on your business data.",
        "Based on your invoices, you might be eligible for additional deductions.",
        "Have you considered setting aside funds for your tax payments?",
        "I can analyze your income and expenses to suggest tax-saving strategies.",
        "Would you like me to explain how VAT works for your business type?",
      ];

      const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];

      const assistantMessage: Message = {
        role: "assistant",
        content: randomResponse,
      };

      setMessages((prev) => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="flex flex-col h-[500px]">
      <ScrollArea className="flex-1 p-4 border rounded-md mb-4">
        <div className="space-y-4">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`flex ${
                message.role === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`flex items-start gap-2 max-w-[80%] ${
                  message.role === "user"
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted"
                } p-3 rounded-lg`}
              >
                {message.role === "assistant" && (
                  <Bot className="h-5 w-5 mt-1 flex-shrink-0" />
                )}
                <div className="break-words">{message.content}</div>
                {message.role === "user" && (
                  <User className="h-5 w-5 mt-1 flex-shrink-0" />
                )}
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="flex items-center gap-2 bg-muted p-3 rounded-lg">
                <Bot className="h-5 w-5" />
                <div className="flex space-x-1">
                  <div className="h-2 w-2 bg-current rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
                  <div className="h-2 w-2 bg-current rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
                  <div className="h-2 w-2 bg-current rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>
      <div className="flex gap-2">
        <Input
          placeholder={language === "de" ? "Nachricht eingeben..." : "Type your message..."}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !isLoading) {
              handleSendMessage();
            }
          }}
          disabled={isLoading}
        />
        <Button
          size="icon"
          onClick={handleSendMessage}
          disabled={isLoading || !input.trim()}
          title={language === "de" ? "Senden" : "Send"}
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
