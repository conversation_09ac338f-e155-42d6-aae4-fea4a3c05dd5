import { buttonVariants } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertCircle, ArrowLeft, Mail } from "lucide-react";
import Link from "next/link";
import { Providers } from "../providers";

export default function Verify() {
  return (
    <Providers>
      <div className="min-h-screen w-full flex items-center justify-center">
        <div className="absolute inset-0 -z-10 h-full w-full bg-white bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] bg-[size:6rem_4rem] dark:bg-gray-950 dark:bg-[linear-gradient(to_right,#1f1f1f_1px,transparent_1px),linear-gradient(to_bottom,#1f1f1f_1px,transparent_1px)]">
          <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(circle_500px_at_50%_200px,#C9EBFF,transparent)] dark:bg-[radial-gradient(circle_500px_at_50%_200px,#193548,transparent)]"></div>
        </div>
        <Card className="w-[380px] px-5">
          <CardHeader className="text-center">
            <div className="mb-4 mx-auto flex size-20 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-950">
              <Mail className="size-12 text-blue-500" />
            </div>

            <CardTitle className="text-2xl font-bold">Check your Email</CardTitle>
            <CardDescription>
              We have sent a verifcation link to your email address.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mt-4 rounded-md bg-yellow-50 border border-yellow-300 p-4 dark:bg-yellow-950 dark:border-yellow-800">
              <div className="flex items-center">
                <AlertCircle className="size-5 text-yellow-400" />

                <p className="text-sm font-medium text-yellow-700 dark:text-yellow-400 ml-3">
                  Be sure to check your spam folder!
                </p>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Link
              href="/"
              className={buttonVariants({
                className: "w-full",
                variant: "outline",
              })}
            >
              <ArrowLeft className="size-4 mr-2" /> Back to Homepage
            </Link>
          </CardFooter>
        </Card>
      </div>
    </Providers>
  );
}
