"use client";

import { useAuth } from "@/app/utils/clientHooks";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, Calculator, FileText, PiggyBank, Users } from "lucide-react";
import Link from "next/link";
import { LoadingState } from "@/app/components/LoadingState";

export default function WEGAccountingDashboardPage() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <LoadingState
        title="Dashboard wird geladen..."
        description="Bitte warten Sie, während die Daten geladen werden."
      />
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">WEG-Abrechnung Dashboard</h1>
        <p className="text-muted-foreground">
          Verwalten Sie Ihre Wohnungseigentümergemeinschaften und erstellen Sie Abrechnungen
        </p>
      </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <DashboardCard
            title="Immobilien"
            description="Verwalten Sie Ihre Immobilien"
            icon={<Building className="size-5" />}
            href="/weg-accounting/dashboard/properties"
          />
          <DashboardCard
            title="Eigentümer"
            description="Verwalten Sie die Eigentümer"
            icon={<Users className="size-5" />}
            href="/weg-accounting/dashboard/owners"
          />
          <DashboardCard
            title="Abrechnung"
            description="Erstellen Sie Abrechnungen"
            icon={<Calculator className="size-5" />}
            href="/weg-accounting/dashboard/accounting"
          />
          <DashboardCard
            title="Rücklagen"
            description="Verwalten Sie Instandhaltungsrücklagen"
            icon={<PiggyBank className="size-5" />}
            href="/weg-accounting/dashboard/reserves"
          />
          <DashboardCard
            title="Berichte"
            description="Erstellen Sie Berichte und Auswertungen"
            icon={<FileText className="size-5" />}
            href="/weg-accounting/dashboard/reports"
          />
        </div>

        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Schnelleinstieg</CardTitle>
              <CardDescription>
                Hier finden Sie die wichtigsten Funktionen für die WEG-Abrechnung
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                Die WEG-Abrechnung-Software ermöglicht Ihnen die einfache Verwaltung Ihrer Wohnungseigentümergemeinschaften.
                Beginnen Sie mit der Erfassung Ihrer Immobilien und Eigentümer, um anschließend Abrechnungen zu erstellen.
              </p>
              <div className="flex flex-wrap gap-2">
                <Link
                  href="/weg-accounting/dashboard/properties"
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                >
                  Immobilie hinzufügen
                </Link>
                <Link
                  href="/weg-accounting/dashboard/owners"
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                >
                  Eigentümer hinzufügen
                </Link>
                <Link
                  href="/weg-accounting/dashboard/accounting"
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                >
                  Abrechnung erstellen
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
}

function DashboardCard({
  title,
  description,
  icon,
  href
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
}) {
  return (
    <Link href={href}>
      <Card className="h-full transition-all hover:shadow-md">
        <CardHeader>
          <div className="flex items-center gap-2">
            {icon}
            <CardTitle>{title}</CardTitle>
          </div>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
      </Card>
    </Link>
  );
}
