import { MailtrapClient } from "mailtrap";
import nodemailer from 'nodemailer';

// Create a Nodemailer transporter using Mailtrap credentials
// Only create transporter if environment variables are available
export const transporter = process.env.EMAIL_SERVER_HOST ? nodemailer.createTransport({
  host: process.env.EMAIL_SERVER_HOST,
  port: Number(process.env.EMAIL_SERVER_PORT),
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD,
  },
  secure: false, // true for 465, false for other ports
}) : null;

// Keep the Mailtrap client for backward compatibility
// Only create client if token is available
export const emailClient = process.env.MAILTRAP_TOKEN ? new MailtrapClient({
  token: process.env.MAILTRAP_TOKEN,
}) : null;
