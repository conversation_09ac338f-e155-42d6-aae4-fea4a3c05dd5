"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Plus, Search, Edit, Trash2, FileText, Download, Eye, Calculator } from "lucide-react";
import { Property } from "../properties/PropertyList";
import { Owner } from "../owners/OwnerList";

// Typen für die Datenstruktur
export type ExpenseCategory = {
  id: string;
  name: string;
  isAllocatable: boolean;
  isHouseholdRelated: boolean;
  isCraftsman: boolean;
};

export type Expense = {
  id: string;
  accountingId: string;
  categoryId: string;
  description: string;
  amount: number;
  date: string;
  distributionKeyId: string;
  householdRelatedAmount: number;
  craftsmanAmount: number;
};

export type AccountingPeriod = {
  id: string;
  propertyId: string;
  year: number;
  startDate: string;
  endDate: string;
  status: "draft" | "completed" | "approved";
  expenses: Expense[];
  openingBalance: number;
  closingBalance: number;
  maintenanceReserveOpening: number;
  maintenanceReserveClosing: number;
  maintenanceReserveContribution: number;
};

interface AccountingListProps {
  accountingPeriods: AccountingPeriod[];
  properties: Property[];
  expenseCategories: ExpenseCategory[];
  onSelectAccounting: (accounting: AccountingPeriod) => void;
  onUpdateAccountings: (accountings: AccountingPeriod[]) => void;
  onRefreshAccountingPeriods?: () => Promise<void>;
}

export function AccountingList({ 
  accountingPeriods, 
  properties, 
  expenseCategories,
  onSelectAccounting, 
  onUpdateAccountings,
  onRefreshAccountingPeriods
}: AccountingListProps) {
  const { language } = useLanguage();
  
  // State für das Hinzufügen einer neuen Abrechnung
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newAccounting, setNewAccounting] = useState<Omit<AccountingPeriod, "id" | "expenses">>({
    propertyId: properties.length > 0 ? properties[0].id : "",
    year: new Date().getFullYear(),
    startDate: `${new Date().getFullYear()}-01-01`,
    endDate: `${new Date().getFullYear()}-12-31`,
    status: "draft",
    openingBalance: 0,
    closingBalance: 0,
    maintenanceReserveOpening: 0,
    maintenanceReserveClosing: 0,
    maintenanceReserveContribution: 0,
  });
  
  // State für die Suche
  const [searchQuery, setSearchQuery] = useState("");
  const [filterYear, setFilterYear] = useState<string>("all");
  const [filterProperty, setFilterProperty] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  
  // State für das Löschen einer Abrechnung
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [accountingToDelete, setAccountingToDelete] = useState<AccountingPeriod | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Gefilterte Abrechnungen basierend auf der Suche und den Filtern
  const filteredAccountings = accountingPeriods.filter(accounting => {
    // Suche
    const matchesSearch = 
      properties.find(p => p.id === accounting.propertyId)?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      accounting.year.toString().includes(searchQuery.toLowerCase());
    
    // Jahr-Filter
    const matchesYear = filterYear === "all" || accounting.year.toString() === filterYear;
    
    // Objekt-Filter
    const matchesProperty = filterProperty === "all" || accounting.propertyId === filterProperty;
    
    // Status-Filter
    const matchesStatus = filterStatus === "all" || accounting.status === filterStatus;
    
    return matchesSearch && matchesYear && matchesProperty && matchesStatus;
  });
  
  // Handler für das Hinzufügen einer neuen Abrechnung
  const handleAddAccounting = async () => {
    console.log("handleAddAccounting called");
    console.log("Current newAccounting:", newAccounting);
    console.log("Current accountingPeriods:", accountingPeriods);
    console.log("Properties available:", properties);
    
    // Validierung der erforderlichen Daten
    if (!newAccounting.propertyId) {
      console.error("No propertyId selected");
      return;
    }
    
    if (properties.length === 0) {
      console.error("No properties available");
      return;
    }
    
    try {
      console.log("Calling API to create accounting period with:", newAccounting);
      
      // API Call statt Server Action
      const response = await fetch('/api/weg-accounting/accounting-periods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newAccounting),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create accounting period');
      }
      
      const addedAccounting = await response.json();
      console.log("API returned new accounting:", addedAccounting);
      
      // Use refresh function if available, otherwise fallback to local state update
      if (onRefreshAccountingPeriods) {
        console.log("Refreshing data from server...");
        await onRefreshAccountingPeriods();
      } else {
        console.log("No refresh function available, updating local state...");
        // Konvertiere die API-Antwort zum erwarteten Format
        const formattedAccounting: AccountingPeriod = {
          id: addedAccounting.id,
          propertyId: addedAccounting.propertyId,
          year: addedAccounting.year,
          startDate: addedAccounting.startDate.split('T')[0], // Nur das Datum
          endDate: addedAccounting.endDate.split('T')[0], // Nur das Datum
          status: addedAccounting.status,
          expenses: addedAccounting.expenses || [],
          openingBalance: addedAccounting.openingBalance,
          closingBalance: addedAccounting.closingBalance,
          maintenanceReserveOpening: addedAccounting.maintenanceReserveOpening,
          maintenanceReserveClosing: addedAccounting.maintenanceReserveClosing,
          maintenanceReserveContribution: addedAccounting.maintenanceReserveContribution,
        };
        
        // Lokale Liste aktualisieren
        const updatedAccountings = [...accountingPeriods, formattedAccounting];
        console.log("Updated accountings array:", updatedAccountings);
        
        onUpdateAccountings(updatedAccountings);
      }
      
      // Reset form
      setNewAccounting({
        propertyId: properties.length > 0 ? properties[0].id : "",
        year: new Date().getFullYear(),
        startDate: `${new Date().getFullYear()}-01-01`,
        endDate: `${new Date().getFullYear()}-12-31`,
        status: "draft",
        openingBalance: 0,
        closingBalance: 0,
        maintenanceReserveOpening: 0,
        maintenanceReserveClosing: 0,
        maintenanceReserveContribution: 0,
      });
      
      setIsAddDialogOpen(false);
      console.log("Dialog closed, form reset");
    } catch (error) {
      console.error("Error adding accounting:", error);
      // Optional: Toast oder Error-Anzeige hinzufügen
    }
  };
  
  // Handler für das Löschen einer Abrechnung
  const handleDeleteAccounting = async (accounting: AccountingPeriod) => {
    setAccountingToDelete(accounting);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteAccounting = async () => {
    if (!accountingToDelete) return;
    
    setIsDeleting(true);
    try {
      console.log("Deleting accounting period:", accountingToDelete.id);
      
      // API Call to delete the accounting period
      const response = await fetch(`/api/weg-accounting/accounting-periods/${accountingToDelete.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete accounting period');
      }
      
      console.log("Accounting period deleted successfully");
      
      // Use refresh function if available, otherwise fallback to local state update
      if (onRefreshAccountingPeriods) {
        console.log("Refreshing data from server...");
        await onRefreshAccountingPeriods();
      } else {
        console.log("No refresh function available, updating local state...");
        // Remove from local state
        const updatedAccountings = accountingPeriods.filter(accounting => accounting.id !== accountingToDelete.id);
        onUpdateAccountings(updatedAccountings);
      }
      
      // Close dialog and reset state
      setIsDeleteDialogOpen(false);
      setAccountingToDelete(null);
    } catch (error) {
      console.error("Error deleting accounting:", error);
      // Optional: Show error toast notification here
    } finally {
      setIsDeleting(false);
    }
  };
  
  // Funktion zum Abrufen des Objektnamens anhand der ID
  const getPropertyName = (propertyId: string) => {
    const property = properties.find(p => p.id === propertyId);
    return property ? property.name : "Unbekanntes Objekt";
  };
  
  // Funktion zum Abrufen der Gesamtsumme der Ausgaben einer Abrechnung
  const getTotalExpenses = (accounting: AccountingPeriod) => {
    return accounting.expenses.reduce((total, expense) => total + expense.amount, 0);
  };
  
  // Funktion zum Formatieren eines Betrags als Währung
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "de" ? "de-AT" : "en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };
  
  // Funktion zum Übersetzen des Status
  const translateStatus = (status: string) => {
    switch (status) {
      case "draft":
        return language === "de" ? "Entwurf" : "Draft";
      case "completed":
        return language === "de" ? "Abgeschlossen" : "Completed";
      case "approved":
        return language === "de" ? "Genehmigt" : "Approved";
      default:
        return status;
    }
  };
  
  // Funktion zum Abrufen der Statusfarbe
  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "completed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
    }
  };
  
  // Verfügbare Jahre für den Filter
  const availableYears = [...new Set(accountingPeriods.map(accounting => accounting.year))].sort((a, b) => b - a);
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">
          {language === "de" ? "Abrechnungen" : "Accounting Periods"}
        </h2>
        
        <div className="flex items-center gap-2 relative z-0">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder={language === "de" ? "Abrechnungen suchen..." : "Search accounting periods..."}
              className="pl-8 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen} modal={true}>
            <DialogTrigger asChild>
              <Button 
                className="h-9 relative z-10 pointer-events-auto cursor-pointer hover:bg-primary/90 focus:ring-2 focus:ring-ring focus:ring-offset-2" 
                onClick={(e) => {
                  e.stopPropagation();
                  console.log("Button clicked!", isAddDialogOpen);
                  setIsAddDialogOpen(true);
                }}
                type="button"
                disabled={false}
              >
                <Plus className="h-4 w-4 mr-2" />
                {language === "de" ? "Neue Abrechnung" : "New Accounting Period"}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto z-[100] bg-background border shadow-xl">
              <DialogHeader>
                <DialogTitle>
                  {language === "de" ? "Neue Abrechnung erstellen" : "Create New Accounting Period"}
                </DialogTitle>
                <DialogDescription>
                  {language === "de" 
                    ? "Geben Sie die Details der neuen Abrechnung ein." 
                    : "Enter the details of the new accounting period."}
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="propertyId">
                    {language === "de" ? "Objekt" : "Property"}
                  </Label>
                  <Select
                    value={newAccounting.propertyId}
                    onValueChange={(value) => setNewAccounting({...newAccounting, propertyId: value})}
                  >
                    <SelectTrigger id="propertyId">
                      <SelectValue placeholder={language === "de" ? "Objekt auswählen" : "Select property"} />
                    </SelectTrigger>
                    <SelectContent>
                      {properties.map((property) => (
                        <SelectItem key={property.id} value={property.id}>
                          {property.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="year">
                    {language === "de" ? "Jahr" : "Year"}
                  </Label>
                  <Input
                    id="year"
                    type="number"
                    value={newAccounting.year}
                    onChange={(e) => setNewAccounting({...newAccounting, year: parseInt(e.target.value) || new Date().getFullYear()})}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startDate">
                      {language === "de" ? "Startdatum" : "Start Date"}
                    </Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={newAccounting.startDate}
                      onChange={(e) => setNewAccounting({...newAccounting, startDate: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="endDate">
                      {language === "de" ? "Enddatum" : "End Date"}
                    </Label>
                    <Input
                      id="endDate"
                      type="date"
                      value={newAccounting.endDate}
                      onChange={(e) => setNewAccounting({...newAccounting, endDate: e.target.value})}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="openingBalance">
                      {language === "de" ? "Anfangsbestand (€)" : "Opening Balance (€)"}
                    </Label>
                    <Input
                      id="openingBalance"
                      type="number"
                      value={newAccounting.openingBalance}
                      onChange={(e) => setNewAccounting({...newAccounting, openingBalance: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="maintenanceReserveOpening">
                      {language === "de" ? "Rücklage Anfang (€)" : "Reserve Opening (€)"}
                    </Label>
                    <Input
                      id="maintenanceReserveOpening"
                      type="number"
                      value={newAccounting.maintenanceReserveOpening}
                      onChange={(e) => setNewAccounting({...newAccounting, maintenanceReserveOpening: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="maintenanceReserveContribution">
                    {language === "de" ? "Rücklagenzuführung (€)" : "Reserve Contribution (€)"}
                  </Label>
                  <Input
                    id="maintenanceReserveContribution"
                    type="number"
                    value={newAccounting.maintenanceReserveContribution}
                    onChange={(e) => setNewAccounting({...newAccounting, maintenanceReserveContribution: parseFloat(e.target.value) || 0})}
                  />
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  {language === "de" ? "Abbrechen" : "Cancel"}
                </Button>
                <Button 
                  onClick={handleAddAccounting}
                  disabled={!newAccounting.propertyId || properties.length === 0}
                >
                  {language === "de" ? "Erstellen" : "Create"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-red-600 dark:text-red-400">
              {language === "de" ? "Abrechnung löschen" : "Delete Accounting Period"}
            </DialogTitle>
            <DialogDescription className="text-base pt-2">
              {language === "de" 
                ? `Möchten Sie die Abrechnung für "${getPropertyName(accountingToDelete?.propertyId || "")}" aus dem Jahr ${accountingToDelete?.year} wirklich löschen?`
                : `Are you sure you want to delete the accounting period for "${getPropertyName(accountingToDelete?.propertyId || "")}" from ${accountingToDelete?.year}?`
              }
              <br /><br />
              <span className="font-medium text-red-600 dark:text-red-400">
                {language === "de" 
                  ? "Diese Aktion kann nicht rückgängig gemacht werden. Alle zugehörigen Ausgaben werden ebenfalls gelöscht."
                  : "This action cannot be undone. All associated expenses will also be deleted."
                }
              </span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2 sm:gap-0">
            <Button 
              variant="outline" 
              onClick={() => {
                setIsDeleteDialogOpen(false);
                setAccountingToDelete(null);
              }}
              disabled={isDeleting}
            >
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteAccounting}
              disabled={isDeleting}
              className="gap-2"
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                  {language === "de" ? "Wird gelöscht..." : "Deleting..."}
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  {language === "de" ? "Endgültig löschen" : "Delete permanently"}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Filter */}
      <div className="flex flex-wrap gap-2">
        <div className="flex items-center space-x-2">
          <Label htmlFor="filterYear" className="text-sm whitespace-nowrap">
            {language === "de" ? "Jahr:" : "Year:"}
          </Label>
          <Select
            value={filterYear}
            onValueChange={setFilterYear}
          >
            <SelectTrigger id="filterYear" className="h-8 w-[100px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {language === "de" ? "Alle" : "All"}
              </SelectItem>
              {availableYears.map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center space-x-2">
          <Label htmlFor="filterProperty" className="text-sm whitespace-nowrap">
            {language === "de" ? "Objekt:" : "Property:"}
          </Label>
          <Select
            value={filterProperty}
            onValueChange={setFilterProperty}
          >
            <SelectTrigger id="filterProperty" className="h-8 w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {language === "de" ? "Alle Objekte" : "All Properties"}
              </SelectItem>
              {properties.map((property) => (
                <SelectItem key={property.id} value={property.id}>
                  {property.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center space-x-2">
          <Label htmlFor="filterStatus" className="text-sm whitespace-nowrap">
            {language === "de" ? "Status:" : "Status:"}
          </Label>
          <Select
            value={filterStatus}
            onValueChange={setFilterStatus}
          >
            <SelectTrigger id="filterStatus" className="h-8 w-[150px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {language === "de" ? "Alle Status" : "All Statuses"}
              </SelectItem>
              <SelectItem value="draft">
                {language === "de" ? "Entwurf" : "Draft"}
              </SelectItem>
              <SelectItem value="completed">
                {language === "de" ? "Abgeschlossen" : "Completed"}
              </SelectItem>
              <SelectItem value="approved">
                {language === "de" ? "Genehmigt" : "Approved"}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Abrechnungsliste */}
      {filteredAccountings.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredAccountings.map((accounting) => (
            <Card 
              key={accounting.id} 
              className="overflow-hidden cursor-pointer hover:border-primary transition-colors"
              onClick={() => onSelectAccounting(accounting)}
            >
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{getPropertyName(accounting.propertyId)}</CardTitle>
                    <CardDescription className="mt-1">
                      {language === "de" ? "Abrechnung " : "Statement "} {accounting.year}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button 
                        variant="ghost" 
                        className="h-8 w-8 p-0"
                        onClick={(e) => e.stopPropagation()} // Verhindert, dass das Klicken auf das Menü die Karte auswählt
                      >
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>
                        {language === "de" ? "Aktionen" : "Actions"}
                      </DropdownMenuLabel>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        onSelectAccounting(accounting);
                      }}>
                        <Eye className="h-4 w-4 mr-2" />
                        {language === "de" ? "Anzeigen" : "View"}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        // Hier könnte eine Funktion zum Exportieren der Abrechnung aufgerufen werden
                      }}>
                        <Download className="h-4 w-4 mr-2" />
                        {language === "de" ? "Exportieren" : "Export"}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAccounting(accounting);
                      }} className="text-red-600 dark:text-red-400">
                        <Trash2 className="h-4 w-4 mr-2" />
                        {language === "de" ? "Löschen" : "Delete"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex flex-col">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Zeitraum" : "Period"}
                    </span>
                    <span className="font-medium">
                      {new Date(accounting.startDate).toLocaleDateString(language === "de" ? "de-AT" : "en-US", { day: "2-digit", month: "2-digit" })} - {new Date(accounting.endDate).toLocaleDateString(language === "de" ? "de-AT" : "en-US", { day: "2-digit", month: "2-digit" })}
                    </span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Status" : "Status"}
                    </span>
                    <span className="font-medium">
                      <Badge className={`${getStatusColor(accounting.status)} border-none`}>
                        {translateStatus(accounting.status)}
                      </Badge>
                    </span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Ausgaben" : "Expenses"}
                    </span>
                    <span className="font-medium">{formatCurrency(getTotalExpenses(accounting))}</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-muted-foreground">
                      {language === "de" ? "Rücklage" : "Reserve"}
                    </span>
                    <span className="font-medium">{formatCurrency(accounting.maintenanceReserveClosing)}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-muted/50 p-3">
                <div className="w-full">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">
                      {language === "de" ? "Anzahl Ausgaben" : "Number of Expenses"}
                    </span>
                    <Badge variant="outline">
                      {accounting.expenses.length}
                    </Badge>
                  </div>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-10">
          <Calculator className="h-10 w-10 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">
            {language === "de" ? "Keine Abrechnungen gefunden" : "No accounting periods found"}
          </h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {language === "de" 
              ? "Beginnen Sie damit, eine neue Abrechnung zu erstellen." 
              : "Start by creating a new accounting period."}
          </p>
        </div>
      )}
    </div>
  );
}
