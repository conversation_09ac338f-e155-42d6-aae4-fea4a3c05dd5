"use client";

import { useState, useEffect } from "react";
import { ReserveList, Reserve } from "./ReserveList";
import { ReserveDetail } from "./ReserveDetail";
import { Property } from "@/app/lib/wegTypes";
import { LoadingState } from "@/app/components/LoadingState";

interface ReserveTabProps {
  properties: Property[];
}

export function ReserveTab({ properties }: ReserveTabProps) {
  const [selectedReserve, setSelectedReserve] = useState<Reserve | null>(null);
  const [reserves, setReserves] = useState<Reserve[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load reserves from API
  useEffect(() => {
    const loadReserves = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/weg-accounting/reserves');
        if (!response.ok) {
          throw new Error(`Failed to fetch reserves: ${response.statusText}`);
        }

        const reservesData = await response.json();
        setReserves(reservesData);
      } catch (error) {
        console.error("Failed to load reserves:", error);
        setError("Failed to load reserves. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    loadReserves();
  }, []);

  // Handler for updating reserves
  const handleUpdateReserves = async (updatedReserves: Reserve[]) => {
    setReserves(updatedReserves);
  };

  // Handler for updating a single reserve
  const handleUpdateReserve = async (updatedReserve: Reserve) => {
    try {
      const response = await fetch(`/api/weg-accounting/reserves/${updatedReserve.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: updatedReserve.name,
          description: updatedReserve.description,
          targetAmount: updatedReserve.targetAmount,
          yearlyContribution: updatedReserve.yearlyContribution,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update reserve: ${response.statusText}`);
      }

      const updated = await response.json();

      // Update the reserve in the list
      setReserves(prev => prev.map(r => r.id === updated.id ? updated : r));

      // Update selected reserve if it's the one being updated
      if (selectedReserve?.id === updated.id) {
        setSelectedReserve(updated);
      }
    } catch (error) {
      console.error("Failed to update reserve:", error);
      setError("Failed to update reserve. Please try again.");
    }
  };

  // Handler for deleting a reserve
  const handleDeleteReserve = async (id: string) => {
    try {
      const response = await fetch(`/api/weg-accounting/reserves/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete reserve: ${response.statusText}`);
      }

      // Remove the reserve from the list
      setReserves(prev => prev.filter(r => r.id !== id));

      // Clear selected reserve if it's the one being deleted
      if (selectedReserve?.id === id) {
        setSelectedReserve(null);
      }
    } catch (error) {
      console.error("Failed to delete reserve:", error);
      setError("Failed to delete reserve. Please try again.");
    }
  };

  // Handler for creating a new reserve
  const handleCreateReserve = async (newReserve: Omit<Reserve, "id" | "balance" | "transactions">) => {
    try {
      const response = await fetch('/api/weg-accounting/reserves', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newReserve),
      });

      if (!response.ok) {
        throw new Error(`Failed to create reserve: ${response.statusText}`);
      }

      const created = await response.json();
      setReserves(prev => [...prev, created]);
    } catch (error) {
      console.error("Failed to create reserve:", error);
      setError("Failed to create reserve. Please try again.");
    }
  };

  // Handler for creating a new transaction
  const handleCreateTransaction = async (reserveId: string, transaction: {
    date: string;
    type: string;
    amount: number;
    description: string;
    documentId?: string;
  }) => {
    try {
      const response = await fetch('/api/weg-accounting/reserve-transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reserveId,
          ...transaction,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create transaction: ${response.statusText}`);
      }

      // Reload reserves to get updated data
      const reservesResponse = await fetch('/api/weg-accounting/reserves');
      if (reservesResponse.ok) {
        const reservesData = await reservesResponse.json();
        setReserves(reservesData);

        // Update selected reserve
        const updatedReserve = reservesData.find((r: Reserve) => r.id === reserveId);
        if (updatedReserve && selectedReserve?.id === reserveId) {
          setSelectedReserve(updatedReserve);
        }
      }
    } catch (error) {
      console.error("Failed to create transaction:", error);
      setError("Failed to create transaction. Please try again.");
    }
  };

  // Handler for deleting a transaction
  const handleDeleteTransaction = async (transactionId: string) => {
    try {
      const response = await fetch(`/api/weg-accounting/reserve-transactions/${transactionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete transaction: ${response.statusText}`);
      }

      // Reload reserves to get updated data
      const reservesResponse = await fetch('/api/weg-accounting/reserves');
      if (reservesResponse.ok) {
        const reservesData = await reservesResponse.json();
        setReserves(reservesData);

        // Update selected reserve
        if (selectedReserve) {
          const updatedReserve = reservesData.find((r: Reserve) => r.id === selectedReserve.id);
          if (updatedReserve) {
            setSelectedReserve(updatedReserve);
          }
        }
      }
    } catch (error) {
      console.error("Failed to delete transaction:", error);
      setError("Failed to delete transaction. Please try again.");
    }
  };

  if (isLoading) {
    return (
      <LoadingState
        title="Rücklagen werden geladen..."
        description="Bitte warten Sie, während die Rücklagendaten geladen werden."
      />
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <div className="text-destructive text-lg font-medium">Fehler beim Laden der Daten</div>
        <div className="text-muted-foreground">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
        >
          Seite neu laden
        </button>
      </div>
    );
  }

  // Handler for selecting a reserve
  const handleSelectReserve = (reserve: Reserve) => {
    setSelectedReserve(reserve);
  };

  return (
    <div>
      {selectedReserve ? (
        <ReserveDetail
          reserve={selectedReserve}
          properties={properties}
          onBack={() => setSelectedReserve(null)}
          onUpdate={handleUpdateReserve}
          onDelete={(id: string) => handleDeleteReserve(id)}
        />
      ) : (
        <ReserveList
          reserves={reserves}
          properties={properties}
          onSelectReserve={handleSelectReserve}
          onUpdateReserves={handleUpdateReserves}
        />
      )}
    </div>
  );
}
