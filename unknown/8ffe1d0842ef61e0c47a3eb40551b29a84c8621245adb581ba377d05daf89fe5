import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Activity, CreditCard, DollarSign, Users } from "lucide-react";
import prisma from "../utils/db";
import { requireUser } from "../utils/hooks";
import { formatCurrency } from "../utils/formatCurrency";
import { DashboardBlocksClient } from "./DashboardBlocksClient";

async function getData(userId: string) {
  const [data, openInvoices, paidinvoices] = await Promise.all([
    prisma.invoice.findMany({
      where: {
        userId: userId,
      },
      select: {
        total: true,
      },
    }),
    prisma.invoice.findMany({
      where: {
        userId: userId,
        status: "PENDING",
      },
      select: {
        id: true,
      },
    }),

    prisma.invoice.findMany({
      where: {
        userId: userId,
        status: "PAID",
      },
      select: {
        id: true,
      },
    }),
  ]);

  return {
    data,
    openInvoices,
    paidinvoices,
  };
}

export async function DashboardBlocks() {
  const session = await requireUser();
  const { data, openInvoices, paidinvoices } = await getData(
    session.user?.id as string
  );

  const totalRevenue = formatCurrency({
    amount: data.reduce((acc, invoice) => acc + invoice.total, 0),
    currency: "USD",
  });

  return (
    <DashboardBlocksClient 
      totalRevenue={totalRevenue}
      totalInvoices={data.length}
      paidInvoices={paidinvoices.length}
      pendingInvoices={openInvoices.length}
    />
  );
}
