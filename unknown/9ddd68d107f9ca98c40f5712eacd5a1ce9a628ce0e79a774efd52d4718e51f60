import { auth } from "@/app/utils/auth";
import { NextResponse } from "next/server";
import prisma from "@/app/utils/db";

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;

    const reserves = await prisma.reserve.findMany({
      where: {
        userId: userId,
      },
      include: {
        transactions: {
          orderBy: {
            date: "desc",
          },
        },
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json(reserves);
  } catch (error) {
    console.error("Failed to fetch reserves:", error);
    return NextResponse.json({ error: "Failed to fetch reserves" }, { status: 500 });
  }
}

export async function POST(request: Request) {  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    const body = await request.json();
    const { propertyId, name, description, targetAmount, yearlyContribution } = body;

    // Validate required fields
    if (!propertyId || !name || !description) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }    // Check if property belongs to user
    const property = await prisma.property.findFirst({
      where: {
        id: propertyId,
        userId: userId,
      },
    });

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    const reserve = await prisma.reserve.create({
      data: {
        userId: userId,
        propertyId,
        name,
        description,
        targetAmount: targetAmount || null,
        yearlyContribution: yearlyContribution || 0,
      },
      include: {
        transactions: true,
        property: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json(reserve);
  } catch (error) {
    console.error("Failed to create reserve:", error);
    return NextResponse.json({ error: "Failed to create reserve" }, { status: 500 });
  }
}
