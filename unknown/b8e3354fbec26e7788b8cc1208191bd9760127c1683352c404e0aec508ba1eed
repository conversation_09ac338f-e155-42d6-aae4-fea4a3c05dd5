"use client"

import { usePathname } from "next/navigation";
import Link from "next/link";
import { useLanguage } from "../../contexts/LanguageContext";
import { cn } from "@/lib/utils";
import {
  Building,
  Users,
  Calculator,
  Receipt,
  PiggyBank,
  FileText,
  UserCog,
  LayoutDashboard,
} from "lucide-react";

export const wegAccountingLinks = [
  {
    id: 0,
    nameKey: "dashboard",
    href: "/weg-accounting/dashboard",
    icon: LayoutDashboard,
  },
  {
    id: 1,
    nameKey: "properties",
    href: "/weg-accounting/dashboard/properties",
    icon: Building,
  },
  {
    id: 2,
    nameKey: "owners",
    href: "/weg-accounting/dashboard/owners",
    icon: Users,
  },
  {
    id: 3,
    nameKey: "accounting",
    href: "/weg-accounting/dashboard/accounting",
    icon: Calculator,
  },
  {
    id: 4,
    nameKey: "invoices",
    href: "/weg-accounting/dashboard/invoices",
    icon: Receipt,
  },
  {
    id: 5,
    nameKey: "reserves",
    href: "/weg-accounting/dashboard/reserves",
    icon: PiggyBank,
  },
  {
    id: 6,
    nameKey: "reports",
    href: "/weg-accounting/dashboard/reports",
    icon: FileText,
  },
  {
    id: 7,
    nameKey: "advisors",
    href: "/weg-accounting/dashboard/advisors",
    icon: UserCog,
  },
];

export function WEGAccountingLinks() {
  const pathname = usePathname();
  const { language, t } = useLanguage();

  return (
    <>
      {wegAccountingLinks.map((link) => {
        const linkName = 
          link.nameKey === "dashboard" ? (language === "de" ? "Dashboard" : "Dashboard") :
          link.nameKey === "properties" ? (language === "de" ? "Immobilien" : "Properties") :
          link.nameKey === "owners" ? (language === "de" ? "Eigentümer" : "Owners") :
          link.nameKey === "accounting" ? (language === "de" ? "Abrechnung" : "Accounting") :
          link.nameKey === "invoices" ? (language === "de" ? "Rechnungen" : "Invoices") :
          link.nameKey === "reserves" ? (language === "de" ? "Rücklagen" : "Reserves") :
          link.nameKey === "reports" ? (language === "de" ? "Berichte" : "Reports") :
          link.nameKey === "advisors" ? (language === "de" ? "Steuerberater" : "Advisors") : "";

        return (
          <Link
            key={link.id}
            href={link.href}
            className={cn(
              pathname === link.href
                ? "text-primary bg-primary/10"
                : "text-muted-foreground hover:text-foreground",
              "flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary"
            )}
          >
            <link.icon className="size-4" />
            {linkName}
          </Link>
        );
      })}
    </>
  );
}
