import prisma from "@/app/utils/db";
import { NextResponse } from "next/server";
import jsPDF from "jspdf";
import { formatCurrency } from "@/app/utils/formatCurrency";

export async function GET(
  request: Request,
  {
    params,
  }: {
    params: Promise<{ invoiceId: string }>;
  }
) {
  const { invoiceId } = await params;

  const data = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
    },
    select: {
      invoiceName: true,
      invoiceNumber: true,
      currency: true,
      fromName: true,
      fromEmail: true,
      fromAddress: true,
      clientName: true,
      clientAddress: true,
      clientEmail: true,
      date: true,
      dueDate: true,
      invoiceItemDescription: true,
      invoiceItemQuantity: true,
      invoiceItemRate: true,
      total: true,
      note: true,
      // TODO: Folgende Felder müssen zur Datenbank hinzugefügt werden:
      // fromUID: string, // UID-Nummer (Umsatzsteuer-Identifikationsnummer)
      // fromCompanyRegister: string, // Firmenbuchnummer
      // fromCompanyRegisterCourt: string, // Firmenbuchgericht
      // fromVATRate: number, // Mehrwertsteuersatz (z.B. 20 für 20%)
      // fromBankName: string, // Name der Bank
      // fromIBAN: string, // IBAN
      // fromBIC: string, // BIC/SWIFT
      // serviceDate: Date, // Leistungsdatum
      // fullAddress: string, // Vollständige Adresse mit PLZ
      // taxOffice: string, // Finanzamt
    },
  });

  if (!data) {
    return NextResponse.json({ error: "Rechnung nicht gefunden" }, { status: 404 });
  }

  // Statische Daten für fehlende Werte (später durch Datenbankwerte ersetzen)
  // TODO: Diese Werte sollten aus der Datenbank kommen
  const fromUID = "ATU12345678"; // Beispiel UID-Nummer
  const fromCompanyRegister = "FN 123456a"; // Beispiel Firmenbuchnummer
  const fromCompanyRegisterCourt = "Handelsgericht Wien"; // Beispiel Firmenbuchgericht
  const fromVATRate = 20; // 20% Mehrwertsteuer in Österreich
  const fromBankName = "Erste Bank"; // Beispiel Bankname
  const fromIBAN = "********************"; // Beispiel IBAN
  const fromBIC = "GIBAATWWXXX"; // Beispiel BIC
  const taxOffice = "Finanzamt Wien"; // Beispiel Finanzamt
  const fullAddress = "Musterstraße 1, 1010 Wien"; // Vollständige Adresse

  const pdf = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4",
  });

  // Schriftart setzen
  pdf.setFont("helvetica");

  // Rechnung Überschrift
  pdf.setFontSize(24);
  pdf.text("RECHNUNG", 20, 20);

  // Verkäufer/Absender Sektion - Linke Seite
  pdf.setFontSize(12);
  pdf.text("Verkäufer:", 20, 35);
  pdf.setFontSize(10);

  // Verbesserte Platzierung für Verkäufer-Informationen
  const sellerInfo = [
    data.fromName,
    fullAddress, // Vollständige Adresse mit PLZ
    `E-Mail: ${data.fromEmail}`,
    `UID-Nr.: ${fromUID}`,
  ];

  pdf.text(sellerInfo, 20, 40);

  // Firmen-Details - unterer linker Bereich
  pdf.setFontSize(10);
  const companyInfo = [
    `Firmenbuch: ${fromCompanyRegister}`,
    `Firmenbuchgericht: ${fromCompanyRegisterCourt}`,
    `Finanzamt: ${taxOffice}`
  ];

  pdf.text(companyInfo, 20, 60);

  // Kunde Sektion - getrennt von Verkäufer und mit mehr Platz
  pdf.setFontSize(12);
  pdf.text("Kunde:", 20, 80);
  pdf.setFontSize(10);
  pdf.text([
    data.clientName,
    data.clientAddress,
    `E-Mail: ${data.clientEmail}`
  ], 20, 85);

  // Rechnungsdetails - Rechte Seite
  pdf.setFontSize(10);
  pdf.text(`Rechnungsnummer: ${data.invoiceNumber}`, 120, 35);

  // Datumsformat auf deutsch (TT.MM.YYYY)
  const formattedDate = new Date(data.date).toLocaleDateString('de-AT', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  pdf.text(`Rechnungsdatum: ${formattedDate}`, 120, 40);
  pdf.text(`Zahlungsziel: ${data.dueDate} Tage`, 120, 45);
  pdf.text(`Leistungsdatum: ${formattedDate}`, 120, 50);

  // Mehr Platz zwischen den Kopfsektionen und der Tabelle
  const tableStartY = 110;

  // Tabellenkopf
  pdf.setFontSize(10);
  pdf.setFont("helvetica", "bold");
  pdf.text("Beschreibung", 20, tableStartY);
  pdf.text("Menge", 90, tableStartY);
  pdf.text("Einzelpreis", 110, tableStartY);
  pdf.text("Netto", 135, tableStartY);
  pdf.text(`USt. ${fromVATRate}%`, 160, tableStartY);
  pdf.text("Brutto", 180, tableStartY);

  // Trennlinie für Kopfzeile
  pdf.line(20, tableStartY + 2, 190, tableStartY + 2);

  // Artikel Details
  pdf.setFont("helvetica", "normal");

  // Einzelpreis (netto)
  const singlePrice = data.invoiceItemRate;

  // Gesamtpreis (netto)
  const netTotal = data.total;

  // Mehrwertsteuer berechnen
  const vatAmount = netTotal * (fromVATRate / 100);

  // Bruttopreis berechnen
  const grossTotal = netTotal + vatAmount;

  // Bessere formatierung der Eurobeträge
  const formatEuro = (amount: number) => `${amount.toFixed(2)} €`;

  const itemRowY = tableStartY + 10;
  pdf.text(data.invoiceItemDescription, 20, itemRowY);
  pdf.text(data.invoiceItemQuantity.toString(), 90, itemRowY);
  pdf.text(formatEuro(singlePrice), 110, itemRowY);
  pdf.text(formatEuro(netTotal), 135, itemRowY);
  pdf.text(formatEuro(vatAmount), 160, itemRowY);
  pdf.text(formatEuro(grossTotal), 180, itemRowY);

  // Summe
  pdf.line(20, itemRowY + 5, 190, itemRowY + 5);
  pdf.setFont("helvetica", "bold");

  const summaryStartY = itemRowY + 20;

  // Rechtsbündige Ausrichtung für die Beträge
  pdf.text("Zwischensumme (Netto):", 110, summaryStartY);
  pdf.text(formatEuro(netTotal), 180, summaryStartY);

  pdf.text(`USt. (${fromVATRate}%):`, 110, summaryStartY + 5);
  pdf.text(formatEuro(vatAmount), 180, summaryStartY + 5);

  pdf.text("Gesamtbetrag (Brutto):", 110, summaryStartY + 10);
  pdf.text(formatEuro(grossTotal), 180, summaryStartY + 10);

  // Bankverbindung - mit mehr Abstand zur Summe
  const bankStartY = summaryStartY + 25;
  pdf.setFont("helvetica", "normal");
  pdf.setFontSize(10);
  pdf.text("Bankverbindung:", 20, bankStartY);
  pdf.text([
    `Bank: ${fromBankName}`,
    `IBAN: ${fromIBAN}`,
    `BIC: ${fromBIC}`
  ], 20, bankStartY + 5);

  // Zahlungsbedingungen
  const paymentTermsY = bankStartY + 25;
  pdf.setFontSize(10);
  pdf.text("Zahlungsbedingungen:", 20, paymentTermsY);
  pdf.text([
    `Bitte überweisen Sie den Gesamtbetrag innerhalb von ${data.dueDate} Tagen auf das angegebene Konto.`,
    `Bei Fragen zur Rechnung wenden Sie sich bitte an: ${data.fromEmail}`
  ], 20, paymentTermsY + 5);

  // Bemerkung/Notiz
  let currentY = paymentTermsY + 20;
  if (data.note) {
    pdf.setFont("helvetica", "normal");
    pdf.setFontSize(10);
    pdf.text("Anmerkung:", 20, currentY);
    pdf.text(data.note, 20, currentY + 5);
    currentY += 20;
  }

  // Kleinunternehmerregelung (falls anwendbar)
  const isSmallBusiness = false; // Beispiel, sollte aus der Datenbank kommen
  if (isSmallBusiness) {
    pdf.setFontSize(9);
    pdf.text("Gemäß § 6 Abs. 1 Z 27 UStG wird keine Umsatzsteuer ausgewiesen, da der Unternehmer unter die Kleinunternehmerregelung fällt.", 20, currentY);
    currentY += 5;
  }

  // Reverse Charge Hinweis (falls anwendbar)
  const isReverseCharge = false; // Beispiel, sollte aus der Datenbank kommen
  if (isReverseCharge) {
    pdf.setFontSize(9);
    pdf.text("Steuerschuldnerschaft des Leistungsempfängers (Reverse Charge) gemäß § 19 UStG.", 20, currentY);
    currentY += 5;
  }

  // Österreichischer rechtlicher Hinweis - ans Ende der Seite
  pdf.setFontSize(8);
  pdf.text([
    "Diese Rechnung entspricht den Anforderungen des österreichischen Umsatzsteuergesetzes.",
    `Leistungsdatum entspricht dem Rechnungsdatum, sofern nicht anders angegeben.`,
    `Firmenbuch: ${fromCompanyRegister}, ${fromCompanyRegisterCourt}`,
    `UID-Nr.: ${fromUID}`
  ], 20, 270);

  // PDF als Buffer generieren
  const pdfBuffer = Buffer.from(pdf.output("arraybuffer"));

  // PDF zum Download zurückgeben
  return new NextResponse(pdfBuffer, {
    headers: {
      "Content-Type": "application/pdf",
      "Content-Disposition": "inline; filename=Rechnung.pdf",
    },
  });
}
