"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Mail, Phone, Building, Copy, Check } from "lucide-react";
import { Advisor } from "@/app/lib/wegTypes";
import { updateAdvisor, getAdvisorPermissions, regenerateAccessToken } from "@/app/actions/advisors";
import { toast } from "sonner";
import { useEffect } from "react";

interface AdvisorDetailProps {
  advisor: Advisor;
  onBack: () => void;
  onUpdate: (advisor: Advisor) => void;
}

export function AdvisorDetail({ advisor, onBack, onUpdate }: AdvisorDetailProps) {
  const { language } = useLanguage();
  const [isEditing, setIsEditing] = useState(false);
  const [editedAdvisor, setEditedAdvisor] = useState<Advisor>(advisor);
  // Lokale Definition für die Berechtigungen
  interface AdvisorPermissionsWithToken {
    canViewWEG: boolean;
    canViewInvoices: boolean;
    canViewEmployees: boolean;
    canDownload: boolean;
    accessToken: string;
  }

  const [permissions, setPermissions] = useState<AdvisorPermissionsWithToken | null>(null);
  const [accessLink, setAccessLink] = useState<string>("");
  const [copied, setCopied] = useState(false);

  // Laden der Berechtigungen beim Mounten der Komponente
  useEffect(() => {
    const loadPermissions = async () => {
      try {
        // Hole die Berechtigungen und das Zugriffstoken
        const userAdvisor = await getAdvisorPermissions(advisor.id);

        if (!userAdvisor) {
          throw new Error("Advisor permissions not found");
        }

        // Erstelle ein Permissions-Objekt basierend auf den UserAdvisor-Permissions
        const perms: AdvisorPermissionsWithToken = {
          canViewWEG: userAdvisor.canViewWEG,
          canViewInvoices: userAdvisor.canViewInvoices,
          canViewEmployees: userAdvisor.canViewEmployees,
          canDownload: userAdvisor.canDownload,
          accessToken: userAdvisor.accessToken
        };

        setPermissions(perms);

        // Erstellen des Zugriffslinks
        const baseUrl = window.location.origin;
        setAccessLink(`${baseUrl}/weg-accounting/advisor-access/${perms.accessToken}`);
      } catch (error) {
        console.error("Failed to load advisor permissions:", error);
      }
    };

    loadPermissions();
  }, [advisor.id, advisor.permissions]);

  // Handler für die Aktualisierung des Beraters
  const handleUpdateAdvisor = async () => {
    try {
      if (!editedAdvisor.name || !editedAdvisor.email) {
        toast.error(
          language === "de"
            ? "Name und E-Mail sind erforderlich"
            : "Name and email are required"
        );
        return;
      }

      console.log("Updating advisor with data:", {
        id: advisor.id,
        name: editedAdvisor.name,
        email: editedAdvisor.email,
        phone: editedAdvisor.phone,
        company: editedAdvisor.address, // Map address to company
        notes: editedAdvisor.notes,
        canViewWEG: permissions?.canViewWEG,
        canViewInvoices: permissions?.canViewInvoices,
        canViewEmployees: permissions?.canViewEmployees,
        canDownload: permissions?.canDownload
      });

      // Aktualisiere den Berater mit allen verfügbaren Berechtigungen
      const updatedAdvisor = await updateAdvisor(advisor.id, {
        name: editedAdvisor.name,
        email: editedAdvisor.email,
        phone: editedAdvisor.phone || "",
        company: editedAdvisor.address || "", // Map address to company
        notes: editedAdvisor.notes || "",
        canViewWEG: permissions?.canViewWEG ?? true,
        canViewInvoices: permissions?.canViewInvoices ?? false,
        canViewEmployees: permissions?.canViewEmployees ?? false,
        canViewAccounting: permissions?.canViewWEG ?? true,
        canViewReports: permissions?.canViewWEG ?? true,
        canViewReserves: permissions?.canViewWEG ?? true,
        canViewOwners: permissions?.canViewWEG ?? true,
        canViewProperties: permissions?.canViewWEG ?? true,
        canDownload: permissions?.canDownload ?? true,
        canEdit: false
      });

      // Lade die aktualisierten Berechtigungen nach dem Update
      const updatedPermissions = await getAdvisorPermissions(advisor.id);

      // Convert the database Advisor to the wegTypes Advisor
      const wegAdvisor: Advisor = {
        id: updatedAdvisor.id,
        name: updatedAdvisor.name,
        email: updatedAdvisor.email,
        phone: updatedAdvisor.phone || undefined,
        address: updatedAdvisor.company || undefined,
        notes: updatedAdvisor.notes || undefined,
        permissions: {
          canViewProperties: updatedPermissions?.canViewProperties || false,
          canViewOwners: updatedPermissions?.canViewOwners || false,
          canViewAccounting: updatedPermissions?.canViewAccounting || false,
          canViewInvoices: updatedPermissions?.canViewInvoices || false,
          canViewReports: updatedPermissions?.canViewReports || false,
          canDownloadDocuments: updatedPermissions?.canDownload || false,
          canEdit: updatedPermissions?.canEdit || false
        }
      };

      // Aktualisiere die lokalen Berechtigungen
      if (updatedPermissions) {
        setPermissions({
          canViewWEG: updatedPermissions.canViewWEG,
          canViewInvoices: updatedPermissions.canViewInvoices,
          canViewEmployees: updatedPermissions.canViewEmployees,
          canDownload: updatedPermissions.canDownload,
          accessToken: updatedPermissions.accessToken
        });
      }

      onUpdate(wegAdvisor);
      setIsEditing(false);
      toast.success(
        language === "de"
          ? "Berater erfolgreich aktualisiert"
          : "Advisor updated successfully"
      );
    } catch (error) {
      console.error("Failed to update advisor:", error);
      toast.error(
        language === "de"
          ? "Fehler bei der Aktualisierung des Beraters"
          : "Failed to update advisor"
      );
    }
  };

  // Handler für das Kopieren des Zugriffslinks
  const handleCopyLink = () => {
    navigator.clipboard.writeText(accessLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    toast.success(
      language === "de"
        ? "Link in die Zwischenablage kopiert"
        : "Link copied to clipboard"
    );
  };

  // Handler für die Regenerierung des Zugriffstokens
  const handleRegenerateToken = async () => {
    try {
      const newToken = await regenerateAccessToken(advisor.id);

      // Aktualisiere das Zugriffstoken und den Link
      if (permissions) {
        const updatedPermissions = { ...permissions, accessToken: newToken };
        setPermissions(updatedPermissions);

        // Aktualisiere den Zugriffslink
        const baseUrl = window.location.origin;
        const newLink = `${baseUrl}/weg-accounting/advisor-access/${newToken}`;
        setAccessLink(newLink);
      }

      toast.success(
        language === "de"
          ? "Zugriffstoken erfolgreich regeneriert"
          : "Access token regenerated successfully"
      );
    } catch (error) {
      console.error("Failed to regenerate access token:", error);
      toast.error(
        language === "de"
          ? "Fehler bei der Regenerierung des Zugriffstokens"
          : "Failed to regenerate access token"
      );
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center">
        <Button variant="ghost" size="sm" onClick={onBack} className="mr-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <CardTitle>
            {isEditing
              ? language === "de"
                ? "Berater bearbeiten"
                : "Edit Advisor"
              : advisor.name}
          </CardTitle>
          <CardDescription>
            {isEditing
              ? language === "de"
                ? "Bearbeiten Sie die Informationen und Berechtigungen des Beraters"
                : "Edit the advisor's information and permissions"
              : advisor.email}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">{language === "de" ? "Name" : "Name"}</Label>
                <Input
                  id="name"
                  value={editedAdvisor.name}
                  onChange={(e) => setEditedAdvisor({ ...editedAdvisor, name: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">{language === "de" ? "E-Mail" : "Email"}</Label>
                <Input
                  id="email"
                  type="email"
                  value={editedAdvisor.email}
                  onChange={(e) => setEditedAdvisor({ ...editedAdvisor, email: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">{language === "de" ? "Telefon" : "Phone"}</Label>
                <Input
                  id="phone"
                  value={editedAdvisor.phone || ""}
                  onChange={(e) => setEditedAdvisor({ ...editedAdvisor, phone: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="company">{language === "de" ? "Unternehmen" : "Company"}</Label>
                <Input
                  id="company"
                  value={editedAdvisor.address || ""}
                  onChange={(e) => setEditedAdvisor({ ...editedAdvisor, address: e.target.value })}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes">{language === "de" ? "Notizen" : "Notes"}</Label>
              <Textarea
                id="notes"
                value={editedAdvisor.notes || ""}
                onChange={(e) => setEditedAdvisor({ ...editedAdvisor, notes: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label>{language === "de" ? "Berechtigungen" : "Permissions"}</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="canViewWEG"
                    checked={permissions?.canViewWEG}
                    onCheckedChange={(checked) =>
                      setPermissions(permissions ? { ...permissions, canViewWEG: checked as boolean } : null)
                    }
                  />
                  <Label htmlFor="canViewWEG">
                    {language === "de" ? "WEG-Abrechnung einsehen" : "View property management accounting"}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="canViewInvoices"
                    checked={permissions?.canViewInvoices}
                    onCheckedChange={(checked) =>
                      setPermissions(permissions ? { ...permissions, canViewInvoices: checked as boolean } : null)
                    }
                  />
                  <Label htmlFor="canViewInvoices">
                    {language === "de" ? "Rechnungen einsehen" : "View invoices"}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="canViewEmployees"
                    checked={permissions?.canViewEmployees}
                    onCheckedChange={(checked) =>
                      setPermissions(permissions ? { ...permissions, canViewEmployees: checked as boolean } : null)
                    }
                  />
                  <Label htmlFor="canViewEmployees">
                    {language === "de" ? "Mitarbeiter einsehen" : "View employees"}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="canDownload"
                    checked={permissions?.canDownload}
                    onCheckedChange={(checked) =>
                      setPermissions(permissions ? { ...permissions, canDownload: checked as boolean } : null)
                    }
                  />
                  <Label htmlFor="canDownload">
                    {language === "de" ? "Daten herunterladen" : "Download data"}
                  </Label>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {language === "de" ? "Kontaktinformationen" : "Contact Information"}
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{advisor.email}</span>
                  </div>
                  {advisor.phone && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{advisor.phone}</span>
                    </div>
                  )}
                  {advisor.address && (
                    <div className="flex items-center">
                      <Building className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{advisor.address}</span>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {language === "de" ? "Berechtigungen" : "Permissions"}
                </h3>
                <div className="space-y-1">
                  <div className="flex items-center">
                    <Checkbox checked={permissions?.canViewWEG} disabled />
                    <span className="ml-2">
                      {language === "de" ? "WEG-Abrechnung einsehen" : "View property management accounting"}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Checkbox checked={permissions?.canViewInvoices} disabled />
                    <span className="ml-2">
                      {language === "de" ? "Rechnungen einsehen" : "View invoices"}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Checkbox checked={permissions?.canViewEmployees} disabled />
                    <span className="ml-2">
                      {language === "de" ? "Mitarbeiter einsehen" : "View employees"}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Checkbox checked={permissions?.canDownload} disabled />
                    <span className="ml-2">
                      {language === "de" ? "Daten herunterladen" : "Download data"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            {advisor.notes && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {language === "de" ? "Notizen" : "Notes"}
                </h3>
                <p className="text-sm">{advisor.notes}</p>
              </div>
            )}
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">
                {language === "de" ? "Zugriffslink" : "Access Link"}
              </h3>
              <div className="flex items-center space-x-2">
                <Input value={accessLink} readOnly />
                <Button size="sm" onClick={handleCopyLink}>
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {language === "de"
                  ? "Teilen Sie diesen Link mit dem Berater, damit er auf Ihre Daten zugreifen kann."
                  : "Share this link with the advisor to allow them to access your data."}
              </p>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {isEditing ? (
          <>
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              {language === "de" ? "Abbrechen" : "Cancel"}
            </Button>
            <Button onClick={handleUpdateAdvisor}>
              {language === "de" ? "Speichern" : "Save"}
            </Button>
          </>
        ) : (
          <>
            <Button variant="outline" onClick={onBack}>
              {language === "de" ? "Zurück" : "Back"}
            </Button>
            <Button onClick={() => setIsEditing(true)}>
              {language === "de" ? "Bearbeiten" : "Edit"}
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  );
}
