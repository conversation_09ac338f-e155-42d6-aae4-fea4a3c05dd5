"use client";

import { useState, useEffect } from "react";
import { PropertiesTab } from "@/app/components/weg-accounting/properties/PropertiesTab";
import { LoadingState } from "@/app/components/LoadingState";
import { Property } from "@/app/lib/wegTypes";
import { useAuth } from "@/app/utils/clientHooks";

export default function PropertiesPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [properties, setProperties] = useState<Property[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Only load properties if the user is authenticated
    if (isAuthenticated) {
      const loadProperties = async () => {
        try {
          setIsLoading(true);
          // Fetch properties from API
          const response = await fetch('/api/weg-accounting/properties');
          if (!response.ok) {
            throw new Error(`Failed to fetch properties: ${response.statusText}`);
          }
          const data = await response.json();
          setProperties(data);
        } catch (error) {
          console.error("Failed to load properties:", error);
        } finally {
          setIsLoading(false);
        }
      };

      loadProperties();
    }
  }, [isAuthenticated]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Immobilien</h1>
        <p className="text-muted-foreground">
          Verwalten Sie Ihre Immobilien und deren Eigenschaften
        </p>
      </div>

      {authLoading || isLoading ? (
        <LoadingState
          title="Objekte werden geladen..."
          description="Bitte warten Sie, während die Daten geladen werden."
        />
      ) : (
        <PropertiesTab initialProperties={properties} />
      )}
    </div>
  );
}
