"use server"

import { signOut } from "../utils/auth";
import { hashPassword } from "../utils/auth";
import prisma from "../utils/db";
import { randomBytes } from "crypto";
import { transporter } from "../utils/mailtrap";

// Funktion zum Ausloggen
export async function logout() {
  await signOut();
}

// Funktion zur Benutzerregistrierung
export async function registerUser(email: string, password: string) {
  try {
    // Prüfen, ob die E-Mail bereits existiert
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return { error: "Diese E-Mail-Adresse wird bereits verwendet." };
    }

    // Passwort hashen
    const hashedPassword = await hashPassword(password);

    // Verifikationstoken erstellen
    const token = randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 Stunden

    // Benutzer erstellen
    const user = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
      }
    });

    // Verifikationstoken speichern
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token,
        expires,
      }
    });

    // Bestätigungs-E-Mail manuell senden mit dem vordefinierten Transporter

    // Verifikations-URL erstellen
    const baseUrl =
      process.env.NODE_ENV === 'production'
        ? 'https://tax-mate.at'
        : 'http://localhost:3000';
    const verificationUrl = `${baseUrl}/api/auth/verify-email?token=${token}`;    // E-Mail senden - only if transporter is available
    if (transporter) {
      await transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: email,
        subject: 'Bestätigen Sie Ihre E-Mail-Adresse',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Willkommen bei TaxMate!</h2>
            <p>Vielen Dank für Ihre Registrierung. Bitte bestätigen Sie Ihre E-Mail-Adresse, indem Sie auf den folgenden Link klicken:</p>
            <p><a href="${verificationUrl}" style="display: inline-block; padding: 10px 20px; background-color: #4F46E5; color: white; text-decoration: none; border-radius: 5px;">E-Mail bestätigen</a></p>
            <p>Oder kopieren Sie diesen Link in Ihren Browser:</p>
            <p>${verificationUrl}</p>
            <p>Dieser Link ist 24 Stunden gültig.</p>            <p>Wenn Sie sich nicht bei TaxMate registriert haben, können Sie diese E-Mail ignorieren.</p>
            <p>Mit freundlichen Grüßen,<br>Ihr TaxMate-Team</p>
          </div>
        `,
      });

      console.log(`Verification email sent to ${email}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Registration error:", error);
    return { error: "Bei der Registrierung ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut." };
  }
}

// Funktion zur E-Mail-Verifizierung
export async function verifyEmail(token: string) {
  try {
    // Token in der Datenbank suchen
    const verificationToken = await prisma.verificationToken.findFirst({
      where: {
        token,
        expires: {
          gt: new Date(),
        },
      },
    });

    if (!verificationToken) {
      return { error: "Ungültiger oder abgelaufener Token." };
    }

    // Benutzer aktualisieren
    await prisma.user.update({
      where: {
        email: verificationToken.identifier,
      },
      data: {
        emailVerified: new Date(),
      },
    });

    // Token löschen
    await prisma.verificationToken.delete({
      where: {
        identifier_token: {
          identifier: verificationToken.identifier,
          token,
        },
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Email verification error:", error);
    return { error: "Bei der E-Mail-Verifizierung ist ein Fehler aufgetreten." };
  }
}

// Funktion zum Anfordern eines Passwort-Resets
export async function requestPasswordReset(email: string) {
  try {
    // Prüfen, ob der Benutzer existiert
    const user = await prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      // Aus Sicherheitsgründen geben wir immer eine Erfolgsmeldung zurück
      return { success: "Falls ein Konto mit dieser E-Mail existiert, wurde ein Reset-Link gesendet." };
    }

    // Reset-Token erstellen
    const token = randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 60 * 60 * 1000); // 1 Stunde

    // Alten Token löschen falls vorhanden
    await prisma.verificationToken.deleteMany({
      where: {
        identifier: email,
      },
    });

    // Neuen Token erstellen
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token,
        expires,
      },
    });

    // Reset-URL erstellen
    const baseUrl =
      process.env.NODE_ENV === 'production'
        ? 'https://tax-mate.at'
        : 'http://localhost:3000';
    const resetUrl = `${baseUrl}/weg-accounting/reset-password/confirm?token=${token}`;

    // E-Mail senden
    if (transporter) {
      await transporter.sendMail({
        from: process.env.EMAIL_FROM,
        to: email,
        subject: 'Passwort zurücksetzen - TaxMate',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
            <div style="text-align: center; padding: 40px 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
              <img src="${baseUrl}/logo.png" alt="TaxMate Logo" style="max-width: 120px; height: auto; margin-bottom: 20px;">
              <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">TaxMate</h1>
            </div>

            <div style="padding: 40px 20px; background-color: white;">
              <h2 style="color: #333; margin-bottom: 20px; font-size: 24px;">Passwort zurücksetzen</h2>

              <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                Hallo,
              </p>

              <p style="color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
                Sie haben eine Anfrage zum Zurücksetzen Ihres Passworts gestellt. Klicken Sie auf den Button unten, um ein neues Passwort zu erstellen:
              </p>

              <div style="text-align: center; margin: 40px 0;">
                <a href="${resetUrl}" style="display: inline-block; padding: 16px 32px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                  Passwort zurücksetzen
                </a>
              </div>

              <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
                Falls der Button nicht funktioniert, kopieren Sie diesen Link in Ihren Browser:
              </p>

              <p style="color: #667eea; font-size: 14px; word-break: break-all; background-color: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #667eea;">
                ${resetUrl}
              </p>

              <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
                <p style="color: #999; font-size: 12px; line-height: 1.6;">
                  <strong>Wichtige Hinweise:</strong><br>
                  • Dieser Link ist nur 1 Stunde gültig<br>
                  • Falls Sie diese Anfrage nicht gestellt haben, ignorieren Sie diese E-Mail<br>
                  • Ihr aktuelles Passwort bleibt unverändert, bis Sie ein neues festlegen
                </p>
              </div>
            </div>

            <div style="text-align: center; padding: 20px; background-color: #f8f9fa; color: #999; font-size: 12px;">
              <p style="margin: 0;">© 2024 TaxMate. Alle Rechte vorbehalten.</p>
              <p style="margin: 5px 0 0 0;">Diese E-Mail wurde automatisch generiert. Bitte antworten Sie nicht auf diese E-Mail.</p>
            </div>
          </div>
        `,
      });
    }

    return { success: "Falls ein Konto mit dieser E-Mail existiert, wurde ein Reset-Link gesendet." };
  } catch (error) {
    console.error("Password reset request error:", error);
    return { error: "Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut." };
  }
}

// Funktion zum Zurücksetzen des Passworts mit Token
export async function resetPasswordWithToken(token: string, newPassword: string) {
  try {
    // Token in der Datenbank suchen
    const verificationToken = await prisma.verificationToken.findFirst({
      where: {
        token,
        expires: {
          gt: new Date(),
        },
      },
    });

    if (!verificationToken) {
      return { error: "Ungültiger oder abgelaufener Token." };
    }

    // Benutzer finden
    const user = await prisma.user.findUnique({
      where: {
        email: verificationToken.identifier,
      },
    });

    if (!user) {
      return { error: "Benutzer nicht gefunden." };
    }

    // Neues Passwort hashen
    const hashedPassword = await hashPassword(newPassword);

    // Passwort aktualisieren
    await prisma.user.update({
      where: {
        email: verificationToken.identifier,
      },
      data: {
        password: hashedPassword,
      },
    });

    // Token löschen
    await prisma.verificationToken.delete({
      where: {
        identifier_token: {
          identifier: verificationToken.identifier,
          token,
        },
      },
    });

    return { success: "Passwort erfolgreich zurückgesetzt!" };
  } catch (error) {
    console.error("Password reset error:", error);
    return { error: "Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut." };
  }
}
