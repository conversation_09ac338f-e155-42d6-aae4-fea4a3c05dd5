export type Employee = {
  id: string;
  userId: string;
  name: string;
  email: string;
  position: string;
  department: string;
  startDate: string | Date;
  salary: string | number;
  createdAt: Date;
  updatedAt: Date;
};

export type Advisor = {
  id: string;
  name: string;
  email: string;
  phone: string | null;
  company: string | null;
  notes: string | null;
  isActive: boolean;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
};

export type UserAdvisor = {
  id: string;
  userId: string;
  advisorId: string;
  advisor?: Advisor;
  canViewWEG: boolean;
  canViewInvoices: boolean;
  canViewEmployees: boolean;
  canViewAccounting: boolean;
  canViewReports: boolean;
  canViewReserves: boolean;
  canViewOwners: boolean;
  canViewProperties: boolean;
  canDownload: boolean;
  canEdit: boolean;
  accessToken: string;
  expiresAt: Date | null;
  lastAccessedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
};