import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { Providers } from './providers'
import { Toaster } from "sonner";
import { ToastProvider } from "./context/ToastContext";
import { Inter } from "next/font/google";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "TaxMate",
  description: "TaxMate hilft Selbstständigen & Einzelunternehmern in Österreich, ihre Finanzen im Blick zu behalten, Steuern zu optimieren und Einnahmen einfach zu verwalten.",
  icons: {
    icon: "/favicon.ico", 
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="de" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased ${inter.className}`}
        suppressHydrationWarning
      >
        <ToastProvider>
          <Providers>
            {children}
          </Providers>
        </ToastProvider>
        <Toaster />
      </body>
    </html>
  );
}
