"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Download, FileText, Printer } from "lucide-react";
import { Property } from "../properties/PropertyList";
import { Owner } from "../owners/OwnerList";
import { ExpenseCategory } from "../accounting/AccountingList";

interface ReportGeneratorProps {
  reportType: string;
  property: Property;
  dateRange: {
    from: Date;
    to: Date;
  };
  owners?: Owner[];
  expenseCategories?: ExpenseCategory[];
}

export function ReportGenerator({ 
  reportType, 
  property, 
  dateRange, 
  owners = [], 
  expenseCategories = [] 
}: ReportGeneratorProps) {
  const { language } = useLanguage();
  
  // Format currency based on language
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "de" ? "de-AT" : "en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };
  
  // Format date based on language
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(
      language === "de" ? "de-AT" : "en-US",
      { dateStyle: "medium" }
    ).format(date);
  };
  
  // Handle report download
  const handleDownload = () => {
    // In a real application, this would generate and download a PDF report
    console.log("Downloading report:", {
      reportType,
      property,
      dateRange
    });
  };
  
  // Handle report print
  const handlePrint = () => {
    // In a real application, this would open the print dialog
    console.log("Printing report:", {
      reportType,
      property,
      dateRange
    });
  };
  
  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>
          {language === "de" ? "Bericht: " : "Report: "}
          {reportType === "property-overview" 
            ? (language === "de" ? "Objektübersicht" : "Property Overview")
            : reportType === "owner-overview"
            ? (language === "de" ? "Eigentümerübersicht" : "Owner Overview")
            : reportType === "expense-overview"
            ? (language === "de" ? "Ausgabenübersicht" : "Expense Overview")
            : reportType === "reserve-overview"
            ? (language === "de" ? "Rücklagenübersicht" : "Reserve Overview")
            : reportType === "annual-financial-statement"
            ? (language === "de" ? "Jahresabschluss" : "Annual Financial Statement")
            : ""}
        </CardTitle>
        <CardDescription>
          {language === "de" ? "Objekt: " : "Property: "}{property.name}
          <br />
          {language === "de" ? "Zeitraum: " : "Period: "}
          {formatDate(dateRange.from)} - {formatDate(dateRange.to)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Placeholder for report content - would be different based on report type */}
          <p className="text-sm text-muted-foreground text-center py-4">
            {language === "de"
              ? "In einer vollständigen Implementierung würde hier der generierte Bericht angezeigt werden."
              : "In a complete implementation, the generated report would be displayed here."}
          </p>
          
          {/* Example table for demonstration */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "de" ? "Bezeichnung" : "Description"}</TableHead>
                <TableHead className="text-right">{language === "de" ? "Wert" : "Value"}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell>{language === "de" ? "Gesamtfläche" : "Total Area"}</TableCell>
                <TableCell className="text-right">{property.totalArea} m²</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>{language === "de" ? "Anzahl Einheiten" : "Number of Units"}</TableCell>
                <TableCell className="text-right">{property.units}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>{language === "de" ? "Baujahr" : "Year of Construction"}</TableCell>
                <TableCell className="text-right">{property.yearOfConstruction}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>{language === "de" ? "Anzahl Eigentümer" : "Number of Owners"}</TableCell>
                <TableCell className="text-right">
                  {owners.filter(owner => 
                    owner.units.some(unit => unit.propertyId === property.id)
                  ).length}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={handlePrint}>
          <Printer className="mr-2 h-4 w-4" />
          {language === "de" ? "Drucken" : "Print"}
        </Button>
        <Button onClick={handleDownload}>
          <Download className="mr-2 h-4 w-4" />
          {language === "de" ? "Als PDF herunterladen" : "Download as PDF"}
        </Button>
      </CardFooter>
    </Card>
  );
}
