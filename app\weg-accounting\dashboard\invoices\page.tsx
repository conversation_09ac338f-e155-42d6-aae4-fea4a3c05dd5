"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/app/utils/clientHooks";
import { InvoiceTab } from "@/app/components/weg-accounting/invoices/InvoiceTab";
import { LoadingState } from "@/app/components/LoadingState";
import { Property, ExpenseCategory } from "@/app/lib/wegTypes";

export default function InvoicesPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const [properties, setProperties] = useState<Property[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<ExpenseCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Only load data if the user is authenticated
    if (isAuthenticated) {
      const loadData = async () => {
        try {
          setIsLoading(true);
          // Fetch properties from API
          const propertiesResponse = await fetch('/api/weg-accounting/properties');
          if (!propertiesResponse.ok) {
            throw new Error(`Failed to fetch properties: ${propertiesResponse.statusText}`);
          }
          const propertiesData = await propertiesResponse.json();
          setProperties(propertiesData);

          // Fetch expense categories from API
          const expenseCategoriesResponse = await fetch('/api/weg-accounting/expense-categories');
          if (!expenseCategoriesResponse.ok) {
            throw new Error(`Failed to fetch expense categories: ${expenseCategoriesResponse.statusText}`);
          }
          const expenseCategoriesData = await expenseCategoriesResponse.json();
          setExpenseCategories(expenseCategoriesData);
        } catch (error) {
          console.error("Failed to load data:", error);
        } finally {
          setIsLoading(false);
        }
      };

      loadData();
    }
  }, [isAuthenticated]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Rechnungen</h1>
        <p className="text-muted-foreground">
          Verwalten Sie Rechnungen für Ihre Immobilien
        </p>
      </div>

      {authLoading || isLoading ? (
        <LoadingState
          title="Rechnungsdaten werden geladen..."
          description="Bitte warten Sie, während die Daten geladen werden."
        />
      ) : (
        <InvoiceTab properties={properties} expenseCategories={expenseCategories} />
      )}
    </div>
  );
}
