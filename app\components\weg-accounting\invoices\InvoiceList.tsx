"use client";

import { useState } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Plus, Search, Edit, Trash2, FileText, Download, Eye, Receipt, Building } from "lucide-react";
import { Property } from "../properties/PropertyList";
import { ExpenseCategory } from "../accounting/AccountingList";

// Typen für die Datenstruktur
export type InvoiceStatus = "pending" | "paid" | "cancelled";

export type Invoice = {
  id: string;
  propertyId: string;
  number: string;
  date: string;
  dueDate: string;
  vendorName: string;
  description: string;
  amount: number;
  status: InvoiceStatus;
  categoryId: string;
  filePath?: string;
  notes?: string;
};

interface InvoiceListProps {
  invoices: Invoice[];
  properties: Property[];
  expenseCategories: ExpenseCategory[];
  onSelectInvoice: (invoice: Invoice) => void;
  onUpdateInvoices: (invoices: Invoice[]) => void;
}

export function InvoiceList({ 
  invoices, 
  properties, 
  expenseCategories,
  onSelectInvoice, 
  onUpdateInvoices 
}: InvoiceListProps) {
  const { language } = useLanguage();
  
  // State für das Hinzufügen einer neuen Rechnung
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newInvoice, setNewInvoice] = useState<Omit<Invoice, "id">>({
    propertyId: properties.length > 0 ? properties[0].id : "",
    number: "",
    date: new Date().toISOString().split("T")[0],
    dueDate: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split("T")[0],
    vendorName: "",
    description: "",
    amount: 0,
    status: "pending",
    categoryId: expenseCategories.length > 0 ? expenseCategories[0].id : "",
    notes: "",
  });
  
  // State für die Suche
  const [searchQuery, setSearchQuery] = useState("");
  const [filterProperty, setFilterProperty] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  
  // Gefilterte Rechnungen basierend auf der Suche und den Filtern
  const filteredInvoices = invoices.filter(invoice => {
    // Suche
    const matchesSearch = 
      invoice.number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.vendorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Objekt-Filter
    const matchesProperty = filterProperty === "all" || invoice.propertyId === filterProperty;
    
    // Status-Filter
    const matchesStatus = filterStatus === "all" || invoice.status === filterStatus;
    
    return matchesSearch && matchesProperty && matchesStatus;
  });
  
  // Handler für das Hinzufügen einer neuen Rechnung
  const handleAddInvoice = async () => {
    try {
      // Importiere die addInvoice-Funktion dynamisch
      const { addInvoice } = await import("@/app/actions/wegAccounting");

      // Füge die neue Rechnung hinzu
      const newInvoiceData = await addInvoice(newInvoice);

      // Aktualisiere die lokale Liste
      onUpdateInvoices([...invoices, newInvoiceData]);

      // Setze das Formular zurück
      setNewInvoice({
        propertyId: properties.length > 0 ? properties[0].id : "",
        number: "",
        date: new Date().toISOString().split("T")[0],
        dueDate: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split("T")[0],
        vendorName: "",
        description: "",
        amount: 0,
        status: "pending",
        categoryId: expenseCategories.length > 0 ? expenseCategories[0].id : "",
        notes: "",
      });
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error("Fehler beim Hinzufügen der Rechnung:", error);
      // Hier könnte eine Fehlerbehandlung hinzugefügt werden
    }
  };
  
  // Handler für das Löschen einer Rechnung
  const handleDeleteInvoice = async (id: string) => {
    try {
      // Importiere die deleteInvoice-Funktion dynamisch
      const { deleteInvoice } = await import("@/app/actions/wegAccounting");

      // Lösche die Rechnung
      await deleteInvoice(id);

      // Aktualisiere die lokale Liste
      onUpdateInvoices(invoices.filter(invoice => invoice.id !== id));
    } catch (error) {
      console.error("Fehler beim Löschen der Rechnung:", error);
      // Hier könnte eine Fehlerbehandlung hinzugefügt werden
    }
  };
  
  // Funktion zum Abrufen des Objektnamens anhand der ID
  const getPropertyName = (propertyId: string) => {
    const property = properties.find(p => p.id === propertyId);
    return property ? property.name : "Unbekanntes Objekt";
  };
  
  // Funktion zum Abrufen des Kategorienamens anhand der ID
  const getCategoryName = (categoryId: string) => {
    const category = expenseCategories.find(c => c.id === categoryId);
    return category ? category.name : "Unbekannte Kategorie";
  };
  
  // Funktion zum Formatieren eines Betrags als Währung
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "de" ? "de-AT" : "en-US", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };
  
  // Funktion zum Formatieren eines Datums
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === "de" ? "de-AT" : "en-US");
  };
  
  // Funktion zum Übersetzen des Status
  const translateStatus = (status: InvoiceStatus) => {
    switch (status) {
      case "pending":
        return language === "de" ? "Offen" : "Pending";
      case "paid":
        return language === "de" ? "Bezahlt" : "Paid";
      case "cancelled":
        return language === "de" ? "Storniert" : "Cancelled";
      default:
        return status;
    }
  };
  
  // Funktion zum Abrufen der Statusfarbe
  const getStatusColor = (status: InvoiceStatus) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "paid":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      case "cancelled":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
    }
  };
  
  // Berechne die Gesamtsumme der Rechnungen
  const totalAmount = filteredInvoices.reduce((total, invoice) => total + invoice.amount, 0);
  
  // Berechne die Anzahl der offenen Rechnungen
  const pendingInvoices = filteredInvoices.filter(invoice => invoice.status === "pending");
  const pendingAmount = pendingInvoices.reduce((total, invoice) => total + invoice.amount, 0);
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">
          {language === "de" ? "Rechnungen" : "Invoices"}
        </h2>
        
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder={language === "de" ? "Rechnungen suchen..." : "Search invoices..."}
              className="pl-8 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {language === "de" ? "Neue Rechnung" : "New Invoice"}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>
                  {language === "de" ? "Neue Rechnung hinzufügen" : "Add New Invoice"}
                </DialogTitle>
                <DialogDescription>
                  {language === "de" 
                    ? "Geben Sie die Details der neuen Rechnung ein." 
                    : "Enter the details of the new invoice."}
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="propertyId">
                      {language === "de" ? "Objekt" : "Property"}
                    </Label>
                    <Select
                      value={newInvoice.propertyId}
                      onValueChange={(value) => setNewInvoice({...newInvoice, propertyId: value})}
                    >
                      <SelectTrigger id="propertyId">
                        <SelectValue placeholder={language === "de" ? "Objekt auswählen" : "Select property"} />
                      </SelectTrigger>
                      <SelectContent>
                        {properties.map((property) => (
                          <SelectItem key={property.id} value={property.id}>
                            {property.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="categoryId">
                      {language === "de" ? "Kategorie" : "Category"}
                    </Label>
                    <Select
                      value={newInvoice.categoryId}
                      onValueChange={(value) => setNewInvoice({...newInvoice, categoryId: value})}
                    >
                      <SelectTrigger id="categoryId">
                        <SelectValue placeholder={language === "de" ? "Kategorie auswählen" : "Select category"} />
                      </SelectTrigger>
                      <SelectContent>
                        {expenseCategories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="number">
                      {language === "de" ? "Rechnungsnummer" : "Invoice Number"}
                    </Label>
                    <Input
                      id="number"
                      value={newInvoice.number}
                      onChange={(e) => setNewInvoice({...newInvoice, number: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="vendorName">
                      {language === "de" ? "Lieferant" : "Vendor"}
                    </Label>
                    <Input
                      id="vendorName"
                      value={newInvoice.vendorName}
                      onChange={(e) => setNewInvoice({...newInvoice, vendorName: e.target.value})}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">
                      {language === "de" ? "Rechnungsdatum" : "Invoice Date"}
                    </Label>
                    <Input
                      id="date"
                      type="date"
                      value={newInvoice.date}
                      onChange={(e) => setNewInvoice({...newInvoice, date: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="dueDate">
                      {language === "de" ? "Fälligkeitsdatum" : "Due Date"}
                    </Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={newInvoice.dueDate}
                      onChange={(e) => setNewInvoice({...newInvoice, dueDate: e.target.value})}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">
                    {language === "de" ? "Beschreibung" : "Description"}
                  </Label>
                  <Input
                    id="description"
                    value={newInvoice.description}
                    onChange={(e) => setNewInvoice({...newInvoice, description: e.target.value})}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">
                      {language === "de" ? "Betrag (€)" : "Amount (€)"}
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      value={newInvoice.amount}
                      onChange={(e) => setNewInvoice({...newInvoice, amount: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">
                      {language === "de" ? "Status" : "Status"}
                    </Label>
                    <Select
                      value={newInvoice.status}
                      onValueChange={(value: InvoiceStatus) => setNewInvoice({...newInvoice, status: value})}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder={language === "de" ? "Status auswählen" : "Select status"} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">
                          {language === "de" ? "Offen" : "Pending"}
                        </SelectItem>
                        <SelectItem value="paid">
                          {language === "de" ? "Bezahlt" : "Paid"}
                        </SelectItem>
                        <SelectItem value="cancelled">
                          {language === "de" ? "Storniert" : "Cancelled"}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="notes">
                    {language === "de" ? "Notizen" : "Notes"}
                  </Label>
                  <Input
                    id="notes"
                    value={newInvoice.notes || ""}
                    onChange={(e) => setNewInvoice({...newInvoice, notes: e.target.value})}
                  />
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  {language === "de" ? "Abbrechen" : "Cancel"}
                </Button>
                <Button onClick={handleAddInvoice}>
                  {language === "de" ? "Hinzufügen" : "Add"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      {/* Filter */}
      <div className="flex flex-wrap gap-2">
        <div className="flex items-center space-x-2">
          <Label htmlFor="filterProperty" className="text-sm whitespace-nowrap">
            {language === "de" ? "Objekt:" : "Property:"}
          </Label>
          <Select
            value={filterProperty}
            onValueChange={setFilterProperty}
          >
            <SelectTrigger id="filterProperty" className="h-8 w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-background/90 backdrop-blur-sm">
              <SelectItem value="all">
                {language === "de" ? "Alle Objekte" : "All Properties"}
              </SelectItem>
              {properties.map((property) => (
                <SelectItem key={property.id} value={property.id}>
                  {property.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center space-x-2">
          <Label htmlFor="filterStatus" className="text-sm whitespace-nowrap">
            {language === "de" ? "Status:" : "Status:"}
          </Label>
          <Select
            value={filterStatus}
            onValueChange={setFilterStatus}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={language === "de" ? "Status auswählen" : "Select status"} />
            </SelectTrigger>
            <SelectContent className="bg-background/90 backdrop-blur-sm">
              <SelectItem value="all">
                {language === "de" ? "Alle Status" : "All Statuses"}
              </SelectItem>
              <SelectItem value="pending">
                {language === "de" ? "Offen" : "Pending"}
              </SelectItem>
              <SelectItem value="paid">
                {language === "de" ? "Bezahlt" : "Paid"}
              </SelectItem>
              <SelectItem value="cancelled">
                {language === "de" ? "Storniert" : "Cancelled"}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Zusammenfassung */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Gesamtbetrag" : "Total Amount"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalAmount)}</div>
            <div className="text-sm text-muted-foreground mt-1">
              {language === "de" ? "Anzahl Rechnungen: " : "Number of invoices: "} {filteredInvoices.length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Offene Rechnungen" : "Pending Invoices"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(pendingAmount)}</div>
            <div className="text-sm text-muted-foreground mt-1">
              {language === "de" ? "Anzahl: " : "Count: "} {pendingInvoices.length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">
              {language === "de" ? "Aktionen" : "Actions"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="w-full">
                <Download className="h-4 w-4 mr-2" />
                {language === "de" ? "Exportieren" : "Export"}
              </Button>
              <Button variant="outline" size="sm" className="w-full">
                <FileText className="h-4 w-4 mr-2" />
                {language === "de" ? "Bericht" : "Report"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Rechnungsliste */}
      {filteredInvoices.length > 0 ? (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{language === "de" ? "Rechnungsnr." : "Invoice No."}</TableHead>
                <TableHead>{language === "de" ? "Datum" : "Date"}</TableHead>
                <TableHead>{language === "de" ? "Lieferant" : "Vendor"}</TableHead>
                <TableHead>{language === "de" ? "Beschreibung" : "Description"}</TableHead>
                <TableHead>{language === "de" ? "Objekt" : "Property"}</TableHead>
                <TableHead>{language === "de" ? "Status" : "Status"}</TableHead>
                <TableHead className="text-right">{language === "de" ? "Betrag" : "Amount"}</TableHead>
                <TableHead className="w-[100px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInvoices.map((invoice) => (
                <TableRow 
                  key={invoice.id}
                  className="cursor-pointer"
                  onClick={() => onSelectInvoice(invoice)}
                >
                  <TableCell className="font-medium">{invoice.number}</TableCell>
                  <TableCell>{formatDate(invoice.date)}</TableCell>
                  <TableCell>{invoice.vendorName}</TableCell>
                  <TableCell>{invoice.description}</TableCell>
                  <TableCell>{getPropertyName(invoice.propertyId)}</TableCell>
                  <TableCell>
                    <Badge className={`${getStatusColor(invoice.status)} border-none`}>
                      {translateStatus(invoice.status)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">{formatCurrency(invoice.amount)}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          className="h-8 w-8 p-0"
                          onClick={(e) => e.stopPropagation()} // Verhindert, dass das Klicken auf das Menü die Zeile auswählt
                        >
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>
                          {language === "de" ? "Aktionen" : "Actions"}
                        </DropdownMenuLabel>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onSelectInvoice(invoice);
                        }}>
                          <Eye className="h-4 w-4 mr-2" />
                          {language === "de" ? "Anzeigen" : "View"}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          // Hier könnte eine Funktion zum Exportieren der Rechnung aufgerufen werden
                        }}>
                          <Download className="h-4 w-4 mr-2" />
                          {language === "de" ? "Exportieren" : "Export"}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteInvoice(invoice.id);
                        }} className="text-red-600 dark:text-red-400">
                          <Trash2 className="h-4 w-4 mr-2" />
                          {language === "de" ? "Löschen" : "Delete"}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center py-10">
          <Receipt className="h-10 w-10 mx-auto text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">
            {language === "de" ? "Keine Rechnungen gefunden" : "No invoices found"}
          </h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {language === "de" 
              ? "Beginnen Sie damit, eine neue Rechnung hinzuzufügen." 
              : "Start by adding a new invoice."}
          </p>
        </div>
      )}
    </div>
  );
}
