import { NextResponse } from "next/server";
import { requireUser } from "@/app/utils/hooks";
import { WEGReportGenerator } from "@/app/utils/pdfGenerator";
import { getProperties } from "@/app/actions/wegAccounting";
import prisma from "@/app/utils/db";
import { Property, Owner, ExpenseCategory, AccountingPeriod, Expense } from "@/app/lib/wegTypes";

interface ReportRequest {
  propertyId: string;
  dateRange: {
    from: string;
    to: string;
  };
  reportType: string;
  language: string;
}

export async function POST(
  request: Request,
  { params }: { params: Promise<{ reportType: string }> }
) {
  try {
    const session = await requireUser();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    const { reportType } = await params;
    const body: ReportRequest = await request.json();
    const { propertyId, dateRange, language } = body;

    // Get property data
    const properties = await getProperties();
    const property = properties.find(p => p.id === propertyId);

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Get owners data
    let dbOwners: any[] = [];
    try {
      dbOwners = await prisma.owner.findMany({
        include: {
          units: {
            where: {
              propertyId: propertyId
            }
          }
        }
      });
    } catch (error) {
      console.log("No owners found or database not ready, using empty array");
      dbOwners = [];
    }

    const owners: Owner[] = dbOwners.map((owner: any) => ({
      id: owner.id,
      firstName: owner.firstName,
      lastName: owner.lastName,
      email: owner.email,
      phone: owner.phone || "",
      address: owner.address || "",
      units: owner.units.map((unit: any) => ({
        id: unit.id,
        propertyId: unit.propertyId,
        unitNumber: unit.unitNumber,
        area: unit.area,
        ownershipPercentage: unit.ownershipPercentage
      }))
    }));

    // Get expense categories
    let dbExpenseCategories: any[] = [];
    try {
      dbExpenseCategories = await prisma.expenseCategory.findMany({
        where: {
          userId: userId
        }
      });
    } catch (error) {
      console.log("No expense categories found or database not ready, using empty array");
      dbExpenseCategories = [];
    }

    const expenseCategories: ExpenseCategory[] = dbExpenseCategories.map(cat => ({
      id: cat.id,
      name: cat.name,
      isAllocatable: cat.isAllocatable,
      isHouseholdRelated: cat.isHouseholdRelated,
      isCraftsman: cat.isCraftsman
    }));

    // Get accounting periods and expenses
    let dbAccountingPeriods: any[] = [];
    try {
      dbAccountingPeriods = await prisma.accountingPeriod.findMany({
        where: {
          userId: userId,
          propertyId: propertyId,
          startDate: {
            gte: new Date(dateRange.from)
          },
          endDate: {
            lte: new Date(dateRange.to)
          }
        },
        include: {
          expenses: {
            include: {
              category: true
            }
          }
        }
      });
    } catch (error) {
      console.log("No accounting periods found or database not ready, using empty array");
      dbAccountingPeriods = [];
    }

    const accountingPeriods: AccountingPeriod[] = dbAccountingPeriods.map((period: any) => ({
      id: period.id,
      propertyId: period.propertyId,
      year: period.year,
      startDate: period.startDate.toISOString(),
      endDate: period.endDate.toISOString(),
      status: period.status as "draft" | "completed" | "approved",
      expenses: period.expenses.map((expense: any) => ({
        id: expense.id,
        accountingId: expense.accountingPeriodId,
        categoryId: expense.categoryId,
        description: expense.description,
        amount: expense.amount,
        date: expense.date.toISOString(),
        distributionKeyId: expense.distributionKeyId || "",
        householdRelatedAmount: expense.householdRelatedAmount,
        craftsmanAmount: expense.craftsmanAmount
      })),
      openingBalance: period.openingBalance,
      closingBalance: period.closingBalance,
      maintenanceReserveOpening: period.maintenanceReserveOpening,
      maintenanceReserveClosing: period.maintenanceReserveClosing,
      maintenanceReserveContribution: period.maintenanceReserveContribution
    }));

    const expenses: Expense[] = accountingPeriods.flatMap(period => period.expenses);

    // Prepare report data
    const reportData = {
      property,
      owners,
      expenseCategories,
      accountingPeriods,
      expenses,
      dateRange,
      language
    };

    // Generate PDF based on report type
    const generator = new WEGReportGenerator(language);
    let pdfBuffer: Buffer;

    switch (reportType) {
      case "property-overview":
        pdfBuffer = generator.generatePropertyOverview(reportData);
        break;
      case "owner-overview":
        pdfBuffer = generator.generateOwnerOverview(reportData);
        break;
      case "expense-overview":
        pdfBuffer = generator.generateExpenseOverview(reportData);
        break;
      case "property-distribution-keys":
        pdfBuffer = generator.generateDistributionKeysReport(reportData);
        break;
      case "owner-statements":
        pdfBuffer = generator.generateOwnerOverview(reportData);
        break;
      case "expense-comparison":
        pdfBuffer = generator.generateExpenseOverview(reportData);
        break;
      case "reserve-overview":
        pdfBuffer = generator.generateReserveOverview(reportData);
        break;
      case "reserve-development":
        pdfBuffer = generator.generateReserveOverview(reportData);
        break;
      case "annual-financial-statement":
        pdfBuffer = generator.generateAnnualFinancialStatement(reportData);
        break;
      case "annual-budget-comparison":
        pdfBuffer = generator.generateAnnualFinancialStatement(reportData);
        break;
      case "austrian-accounting-statement":
        pdfBuffer = generator.generateAustrianAccountingStatement(reportData);
        break;
      default:
        return NextResponse.json({ error: "Unknown report type" }, { status: 400 });
    }

    // Return PDF
    return new NextResponse(pdfBuffer, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `inline; filename=${reportType}_${property.name}.pdf`,
      },
    });

  } catch (error) {
    console.error("Error generating report:", error);
    return NextResponse.json({ error: "Failed to generate report" }, { status: 500 });
  }
}
