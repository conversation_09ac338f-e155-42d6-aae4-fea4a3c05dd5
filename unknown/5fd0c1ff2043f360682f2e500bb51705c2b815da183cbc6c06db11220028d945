"use client";

import React, { useState, useEffect } from "react";
import { useLanguage } from "@/app/contexts/LanguageContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  FileText,
  Building,
  Users,
  Calculator,
  PiggyBank,
  Calendar,
  BarChart,
  ChevronRight
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { Property } from "../properties/PropertyList";
import { Owner } from "../owners/OwnerList";
import { ExpenseCategory } from "../accounting/AccountingList";

interface Report {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  category: string;
}

interface ReportsTabProps {
  properties: Property[];
  owners?: Owner[];
  expenseCategories?: ExpenseCategory[];
}

export function ReportsTab({ properties }: ReportsTabProps) {
  const { language } = useLanguage();
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [selectedProperty, setSelectedProperty] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [previewPdfUrl, setPreviewPdfUrl] = useState<string | null>(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState<boolean>(false);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });



  // Generate PDF preview when all fields are filled
  const generatePreview = async () => {
    if (!selectedReport || !selectedProperty || !dateRange.from) return;

    setIsGeneratingPreview(true);
    try {
      const property = properties.find(p => p.id === selectedProperty);
      if (!property) return;

      const response = await fetch(`/api/weg-accounting/reports/${selectedReport.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          propertyId: selectedProperty,
          dateRange: {
            from: dateRange.from.toISOString(),
            to: dateRange.to?.toISOString() || dateRange.from.toISOString()
          },
          reportType: selectedReport.id,
          language
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        setPreviewPdfUrl(url);
      }
    } catch (error) {
      console.error('Error generating preview:', error);
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  // Auto-generate preview when conditions are met
  useEffect(() => {
    if (selectedReport && selectedProperty && dateRange.from) {
      generatePreview();
    } else {
      // Clean up previous preview
      if (previewPdfUrl) {
        window.URL.revokeObjectURL(previewPdfUrl);
        setPreviewPdfUrl(null);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedReport, selectedProperty, dateRange.from, dateRange.to]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (previewPdfUrl) {
        window.URL.revokeObjectURL(previewPdfUrl);
      }
    };
  }, [previewPdfUrl]);

  // Format date based on language
  const formatDate = (date: Date | undefined) => {
    if (!date) return "";

    return format(
      date,
      "PPP",
      { locale: language === "de" ? de : undefined }
    );
  };

  // Handle report generation
  const handleGenerateReport = async () => {
    if (!selectedReport || !selectedProperty || !dateRange.from || isGenerating) return;

    setIsGenerating(true);
    try {
      const property = properties.find(p => p.id === selectedProperty);
      if (!property) return;

      // Generate PDF report
      const response = await fetch(`/api/weg-accounting/reports/${selectedReport.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          propertyId: selectedProperty,
          dateRange: {
            from: dateRange.from.toISOString(),
            to: dateRange.to?.toISOString() || dateRange.from.toISOString()
          },
          reportType: selectedReport.id,
          language
        }),
      });

      if (response.ok) {
        // Download the PDF
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        const sanitizedTitle = selectedReport.title.replace(/[^a-zA-Z0-9]/g, '_');
        const sanitizedPropertyName = property.name.replace(/[^a-zA-Z0-9]/g, '_');
        const dateStr = dateRange.from.toISOString().split('T')[0];
        a.download = `${sanitizedTitle}_${sanitizedPropertyName}_${dateStr}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Reset selection after generating report
        setSelectedReport(null);
      } else {
        console.error('Failed to generate report');
      }
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // All available reports
  const allReports: Report[] = [
    // Property reports
    {
      id: "property-overview",
      title: language === "de" ? "Objektübersicht" : "Property Overview",
      description: language === "de"
        ? "Übersicht aller Objekte mit Eigentümern und Einheiten"
        : "Overview of all properties with owners and units",
      icon: Building,
      category: "property"
    },
    {
      id: "property-distribution-keys",
      title: language === "de" ? "Verteilerschlüssel" : "Distribution Keys",
      description: language === "de"
        ? "Übersicht aller Verteilerschlüssel pro Objekt"
        : "Overview of all distribution keys per property",
      icon: BarChart,
      category: "property"
    },

    // Owner reports
    {
      id: "owner-overview",
      title: language === "de" ? "Eigentümerübersicht" : "Owner Overview",
      description: language === "de"
        ? "Übersicht aller Eigentümer mit deren Einheiten"
        : "Overview of all owners with their units",
      icon: Users,
      category: "owner"
    },
    {
      id: "owner-statements",
      title: language === "de" ? "Eigentümerabrechnungen" : "Owner Statements",
      description: language === "de"
        ? "Abrechnungen für einzelne Eigentümer"
        : "Statements for individual owners",
      icon: FileText,
      category: "owner"
    },
    {
      id: "austrian-accounting-statement",
      title: language === "de" ? "Österreichische Abrechnung" : "Austrian Accounting Statement",
      description: language === "de"
        ? "Vollständige Abrechnung im österreichischen Format mit Vorlagen"
        : "Complete accounting statement in Austrian format with templates",
      icon: FileText,
      category: "owner"
    },

    // Financial reports
    {
      id: "expense-overview",
      title: language === "de" ? "Ausgabenübersicht" : "Expense Overview",
      description: language === "de"
        ? "Übersicht aller Ausgaben nach Kategorien"
        : "Overview of all expenses by category",
      icon: Calculator,
      category: "financial"
    },
    {
      id: "expense-comparison",
      title: language === "de" ? "Ausgabenvergleich" : "Expense Comparison",
      description: language === "de"
        ? "Vergleich der Ausgaben zwischen verschiedenen Abrechnungsperioden"
        : "Comparison of expenses between different accounting periods",
      icon: BarChart,
      category: "financial"
    },

    // Reserve reports
    {
      id: "reserve-overview",
      title: language === "de" ? "Rücklagenübersicht" : "Reserve Overview",
      description: language === "de"
        ? "Übersicht aller Rücklagen und deren aktueller Stand"
        : "Overview of all reserves and their current status",
      icon: PiggyBank,
      category: "reserve"
    },
    {
      id: "reserve-development",
      title: language === "de" ? "Rücklagenentwicklung" : "Reserve Development",
      description: language === "de"
        ? "Entwicklung der Rücklagen über die Zeit"
        : "Development of reserves over time",
      icon: BarChart,
      category: "reserve"
    },

    // Annual reports
    {
      id: "annual-financial-statement",
      title: language === "de" ? "Jahresabschluss" : "Annual Financial Statement",
      description: language === "de"
        ? "Vollständiger Jahresabschluss für eine Wohnungseigentümergemeinschaft"
        : "Complete annual financial statement for a property owner association",
      icon: FileText,
      category: "annual"
    },
    {
      id: "annual-budget-comparison",
      title: language === "de" ? "Budget-Vergleich" : "Budget Comparison",
      description: language === "de"
        ? "Vergleich zwischen Budget und tatsächlichen Ausgaben"
        : "Comparison between budget and actual expenses",
      icon: BarChart,
      category: "annual"
    }
  ];

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>{language === "de" ? "Berichte" : "Reports"}</CardTitle>
          <CardDescription>
            {language === "de"
              ? "Erstellen Sie Berichte und Auswertungen für Ihre Wohnungseigentümergemeinschaften"
              : "Generate reports and analyses for your property owner associations"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {selectedReport ? (
            <div className="space-y-4">
              <div className="flex items-center text-sm text-muted-foreground mb-4">
                <Button
                  variant="ghost"
                  className="h-8 px-2"
                  onClick={() => setSelectedReport(null)}
                >
                  {language === "de" ? "Berichte" : "Reports"}
                </Button>
                <ChevronRight className="h-4 w-4 mx-1" />
                <span>{selectedReport.title}</span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-1 block">
                    {language === "de" ? "Objekt auswählen" : "Select Property"}
                  </label>
                  <Select
                    value={selectedProperty}
                    onValueChange={setSelectedProperty}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={language === "de" ? "Objekt auswählen" : "Select a property"} />
                    </SelectTrigger>
                    <SelectContent>
                      {properties.map((property) => (
                        <SelectItem key={property.id} value={property.id}>
                          {property.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-1 block">
                    {language === "de" ? "Zeitraum auswählen" : "Select Date Range"}
                  </label>

                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left h-11 px-4 hover:bg-blue-600 hover:text-white border-blue-300 shadow-sm text-white"
                      >
                        <Calendar className="mr-3 h-4 w-4 text-white" />
                        <div className="flex-1">
                          {dateRange.from ? (
                            <div className="flex items-center justify-between">
                              <span className="text-white font-medium">
                                {dateRange.to ? (
                                  <>
                                    {formatDate(dateRange.from)} - {formatDate(dateRange.to)}
                                  </>
                                ) : (
                                  formatDate(dateRange.from)
                                )}
                              </span>
                            </div>
                          ) : (
                            <span className="text-white">
                              {language === "de" ? "Zeitraum auswählen..." : "Select date range..."}
                            </span>
                          )}
                        </div>
                        <ChevronRight className="h-4 w-4 text-white" />
                      </Button>
                    </PopoverTrigger>

                    <PopoverContent className="w-96 p-0 bg-slate-800 border border-slate-700 shadow-xl rounded-xl" align="start">
                      <div className="p-6 bg-slate-800">
                        <div className="space-y-6">
                          {/* Header */}
                          <div>
                            <h3 className="text-lg font-semibold text-white mb-1">
                              {language === "de" ? "Zeitraum auswählen" : "Select Date Range"}
                            </h3>
                            <p className="text-sm text-slate-300">
                              {language === "de"
                                ? "Wählen Sie einen Zeitraum für Ihren Bericht"
                                : "Choose a date range for your report"
                              }
                            </p>
                          </div>

                          {/* Quick Selection Buttons */}
                          <div>
                            <p className="text-sm font-medium text-slate-200 mb-3">
                              {language === "de" ? "Schnellauswahl:" : "Quick Select:"}
                            </p>
                            <div className="grid grid-cols-2 gap-2">
                              {[
                                { label: language === "de" ? "Letzter Monat" : "Last Month", days: 30 },
                                { label: language === "de" ? "Letztes Quartal" : "Last Quarter", days: 90 },
                                { label: language === "de" ? "Letztes Jahr" : "Last Year", days: 365 },
                                { label: language === "de" ? "Aktuelles Jahr" : "Current Year", days: 0 }
                              ].map((preset, index) => (
                                <button
                                  key={index}
                                  onClick={() => {
                                    const today = new Date();
                                    let from: Date;
                                    let to: Date = today;

                                    if (preset.days === 0) {
                                      from = new Date(today.getFullYear(), 0, 1);
                                      to = new Date(today.getFullYear(), 11, 31);
                                    } else {
                                      from = new Date(today.getTime() - preset.days * 24 * 60 * 60 * 1000);
                                    }

                                    setDateRange({ from, to });
                                  }}
                                  className="px-4 py-2.5 text-sm font-medium bg-slate-700 hover:bg-blue-600 hover:text-white text-slate-200 rounded-lg transition-all duration-200 border border-slate-600 hover:border-blue-500"
                                >
                                  {preset.label}
                                </button>
                              ))}
                            </div>
                          </div>

                          {/* Custom Date Inputs */}
                          <div>
                            <p className="text-sm font-medium text-slate-200 mb-3">
                              {language === "de" ? "Oder wählen Sie manuell:" : "Or select manually:"}
                            </p>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-xs font-medium text-slate-300 mb-2 block">
                                  {language === "de" ? "Von" : "From"}
                                </label>
                                <input
                                  type="date"
                                  value={dateRange.from ? dateRange.from.toISOString().split('T')[0] : ''}
                                  onChange={(e) => {
                                    const newDate = e.target.value ? new Date(e.target.value) : undefined;
                                    setDateRange(prev => ({ ...prev, from: newDate }));
                                  }}
                                  className="w-full px-3 py-2.5 border border-slate-600 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-slate-700 text-white"
                                />
                              </div>
                              <div>
                                <label className="text-xs font-medium text-slate-300 mb-2 block">
                                  {language === "de" ? "Bis" : "To"}
                                </label>
                                <input
                                  type="date"
                                  value={dateRange.to ? dateRange.to.toISOString().split('T')[0] : ''}
                                  onChange={(e) => {
                                    const newDate = e.target.value ? new Date(e.target.value) : undefined;
                                    setDateRange(prev => ({ ...prev, to: newDate }));
                                  }}
                                  className="w-full px-3 py-2.5 border border-slate-600 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-slate-700 text-white"
                                />
                              </div>
                            </div>
                          </div>

                          {/* Selected Range Display */}
                          {dateRange.from && (
                            <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="text-sm font-medium text-blue-200 mb-1">
                                    {language === "de" ? "Ausgewählter Zeitraum:" : "Selected Range:"}
                                  </p>
                                  <p className="text-sm text-blue-100 font-medium">
                                    {formatDate(dateRange.from)}
                                    {dateRange.to && ` - ${formatDate(dateRange.to)}`}
                                  </p>
                                </div>
                                <div className="bg-blue-600 p-2 rounded-full">
                                  <Calendar className="h-4 w-4 text-blue-100" />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <Button
                  onClick={handleGenerateReport}
                  disabled={!selectedProperty || !dateRange.from || isGenerating}
                >
                  <FileText className="mr-2 h-4 w-4" />
                  {isGenerating
                    ? (language === "de" ? "Generiere..." : "Generating...")
                    : (language === "de" ? "Bericht generieren" : "Generate Report")
                  }
                </Button>
              </div>

              {/* PDF Preview Section */}
              {selectedProperty && dateRange.from && selectedReport && (
                <div className="mt-8 p-6 border rounded-lg bg-gray-50">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">
                      {language === "de" ? "PDF-Vorschau" : "PDF Preview"}
                    </h3>
                    {isGeneratingPreview && (
                      <div className="flex items-center text-sm text-gray-600">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2"></div>
                        {language === "de" ? "Generiere Vorschau..." : "Generating preview..."}
                      </div>
                    )}
                  </div>

                  {previewPdfUrl ? (
                    <div className="bg-white border rounded-lg overflow-hidden shadow-sm">
                      <iframe
                        src={previewPdfUrl}
                        className="w-full h-[600px]"
                        title={language === "de" ? "PDF-Vorschau" : "PDF Preview"}
                      />
                      <div className="p-3 bg-gray-50 border-t text-center">
                        <p className="text-sm text-gray-600">
                          {language === "de"
                            ? "Vorschau des generierten PDFs mit Ihren aktuellen Daten"
                            : "Preview of the generated PDF with your current data"
                          }
                        </p>
                      </div>
                    </div>
                  ) : !isGeneratingPreview && (
                    <div className="bg-white border rounded-lg p-8 text-center text-gray-500">
                      <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>
                        {language === "de"
                          ? "PDF-Vorschau wird geladen..."
                          : "PDF preview is loading..."
                        }
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div>
              <Tabs defaultValue="all" className="space-y-4">
                <TabsList className="grid grid-cols-2 md:grid-cols-6 h-auto">
                  <TabsTrigger value="all" className="flex items-center gap-2 py-2">
                    <FileText className="h-4 w-4" />
                    <span className="hidden md:inline">{language === "de" ? "Alle" : "All"}</span>
                  </TabsTrigger>
                  <TabsTrigger value="property" className="flex items-center gap-2 py-2">
                    <Building className="h-4 w-4" />
                    <span className="hidden md:inline">{language === "de" ? "Objekte" : "Properties"}</span>
                  </TabsTrigger>
                  <TabsTrigger value="owner" className="flex items-center gap-2 py-2">
                    <Users className="h-4 w-4" />
                    <span className="hidden md:inline">{language === "de" ? "Eigentümer" : "Owners"}</span>
                  </TabsTrigger>
                  <TabsTrigger value="financial" className="flex items-center gap-2 py-2">
                    <Calculator className="h-4 w-4" />
                    <span className="hidden md:inline">{language === "de" ? "Finanzen" : "Finances"}</span>
                  </TabsTrigger>
                  <TabsTrigger value="reserve" className="flex items-center gap-2 py-2">
                    <PiggyBank className="h-4 w-4" />
                    <span className="hidden md:inline">{language === "de" ? "Rücklagen" : "Reserves"}</span>
                  </TabsTrigger>
                  <TabsTrigger value="annual" className="flex items-center gap-2 py-2">
                    <Calendar className="h-4 w-4" />
                    <span className="hidden md:inline">{language === "de" ? "Jahresberichte" : "Annual"}</span>
                  </TabsTrigger>
                </TabsList>

                {/* All Reports Tab */}
                <TabsContent value="all">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {allReports.map((report) => {
                      const ReportIcon = report.icon;
                      return (
                        <Card
                          key={report.id}
                          className="cursor-pointer hover:border-primary transition-colors"
                          onClick={() => setSelectedReport(report)}
                        >
                          <CardHeader className="p-4">
                            <div className="flex items-center space-x-2">
                              <ReportIcon className="h-5 w-5 text-muted-foreground" />
                              <CardTitle className="text-base">{report.title}</CardTitle>
                            </div>
                          </CardHeader>
                        </Card>
                      );
                    })}
                  </div>
                </TabsContent>

                {/* Category Tabs */}
                {["property", "owner", "financial", "reserve", "annual"].map((category) => (
                  <TabsContent key={category} value={category}>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {allReports
                        .filter(report => report.category === category)
                        .map((report) => {
                          const ReportIcon = report.icon;
                          return (
                            <Card
                              key={report.id}
                              className="cursor-pointer hover:border-primary transition-colors"
                              onClick={() => setSelectedReport(report)}
                            >
                              <CardHeader className="p-4">
                                <div className="flex items-center space-x-2">
                                  <ReportIcon className="h-5 w-5 text-muted-foreground" />
                                  <CardTitle className="text-base">{report.title}</CardTitle>
                                </div>
                              </CardHeader>
                              <CardContent className="p-4 pt-0">
                                <p className="text-sm text-muted-foreground">{report.description}</p>
                              </CardContent>
                            </Card>
                          );
                        })}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
