"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import { Advisor, UserAdvisor } from "../lib/definitions";
import { requireUser } from "@/app/utils/hooks";
import { randomBytes } from "crypto";

// Get all advisors for the current user
export async function getAdvisors(): Promise<Advisor[]> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const advisors = await prisma.advisor.findMany({
      where: {
        userId: session.user!.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return advisors;
  } catch (error) {
    console.error("Failed to fetch advisors:", error);
    throw new Error("Failed to fetch advisors");
  }
}

// Add a new advisor
export async function addAdvisor(formData: {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  notes?: string;
  canViewWEG: boolean;
  canViewInvoices: boolean;
  canViewEmployees: boolean;
  canViewAccounting: boolean;
  canViewReports: boolean;
  canViewReserves: boolean;
  canViewOwners: boolean;
  canViewProperties: boolean;
  canDownload: boolean;
  canEdit: boolean;
}): Promise<Advisor> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    // Create a transaction to ensure both advisor and userAdvisor are created
    const result = await prisma.$transaction(async (tx) => {
      // Create the advisor
      const advisor = await tx.advisor.create({
        data: {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          company: formData.company,
          notes: formData.notes,
          user: {
            connect: {
              id: session.user!.id,
            },
          },
        },
      });

      // Generate a unique access token
      const accessToken = randomBytes(32).toString('hex');

      // Create the user-advisor relationship with permissions
      await tx.userAdvisor.create({
        data: {
          user: {
            connect: {
              id: session.user!.id,
            },
          },
          advisor: {
            connect: {
              id: advisor.id,
            },
          },
          canViewWEG: formData.canViewWEG,
          canViewInvoices: formData.canViewInvoices,
          canViewEmployees: formData.canViewEmployees,
          canViewAccounting: formData.canViewAccounting,
          canViewReports: formData.canViewReports,
          canViewReserves: formData.canViewReserves,
          canViewOwners: formData.canViewOwners,
          canViewProperties: formData.canViewProperties,
          canDownload: formData.canDownload,
          canEdit: formData.canEdit,
          accessToken,
        },
      });

      return advisor;
    });

    revalidatePath("/dashboard/weg-accounting");
    return result;
  } catch (error) {
    console.error("Failed to add advisor:", error);
    throw new Error("Failed to add advisor");
  }
}

// Update an existing advisor
export async function updateAdvisor(
  id: string,
  formData: {
    name?: string;
    email?: string;
    phone?: string;
    company?: string;
    notes?: string;
    isActive?: boolean;
    canViewWEG?: boolean;
    canViewInvoices?: boolean;
    canViewEmployees?: boolean;
    canViewAccounting?: boolean;
    canViewReports?: boolean;
    canViewReserves?: boolean;
    canViewOwners?: boolean;
    canViewProperties?: boolean;
    canDownload?: boolean;
    canEdit?: boolean;
  }
): Promise<Advisor> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    console.log("Updating advisor in database:", id, formData);

    // Führe die Aktualisierung in einer Transaktion durch, um Konsistenz zu gewährleisten
    const result = await prisma.$transaction(async (tx) => {
      // Update the advisor
      const advisor = await tx.advisor.update({
        where: {
          id,
        },
        data: {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          company: formData.company,
          notes: formData.notes,
          isActive: formData.isActive,
        },
        include: {
          userAdvisors: {
            where: {
              userId: session.user!.id,
            },
          },
        },
      });

      // If permission changes are provided, update the UserAdvisor record
      if (
        formData.canViewWEG !== undefined ||
        formData.canViewInvoices !== undefined ||
        formData.canViewEmployees !== undefined ||
        formData.canDownload !== undefined ||
        formData.canViewAccounting !== undefined ||
        formData.canViewReports !== undefined ||
        formData.canViewReserves !== undefined ||
        formData.canViewOwners !== undefined ||
        formData.canViewProperties !== undefined ||
        formData.canEdit !== undefined
      ) {
        if (advisor.userAdvisors.length > 0) {
          await tx.userAdvisor.update({
            where: {
              id: advisor.userAdvisors[0].id,
            },
            data: {
              canViewWEG: formData.canViewWEG,
              canViewInvoices: formData.canViewInvoices,
              canViewEmployees: formData.canViewEmployees,
              canDownload: formData.canDownload,
              canViewAccounting: formData.canViewAccounting,
              canViewReports: formData.canViewReports,
              canViewReserves: formData.canViewReserves,
              canViewOwners: formData.canViewOwners,
              canViewProperties: formData.canViewProperties,
              canEdit: formData.canEdit,
            },
          });
        }
      }

      return advisor;
    });

    revalidatePath("/weg-accounting/dashboard/advisors");
    return result;
  } catch (error) {
    console.error("Failed to update advisor:", error);
    throw new Error("Failed to update advisor");
  }
}

// Delete an advisor
export async function deleteAdvisor(id: string): Promise<void> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    await prisma.advisor.delete({
      where: {
        id,
        userId: session.user!.id,
      },
    });

    revalidatePath("/dashboard/weg-accounting");
  } catch (error) {
    console.error("Failed to delete advisor:", error);
    throw new Error("Failed to delete advisor");
  }
}

// Get advisor permissions
export async function getAdvisorPermissions(advisorId: string): Promise<UserAdvisor | null> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    const userAdvisor = await prisma.userAdvisor.findUnique({
      where: {
        userId_advisorId: {
          userId: session.user!.id,
          advisorId,
        },
      },
      include: {
        advisor: true,
      },
    });

    return userAdvisor;
  } catch (error) {
    console.error("Failed to fetch advisor permissions:", error);
    throw new Error("Failed to fetch advisor permissions");
  }
}

// Regenerate access token for an advisor
export async function regenerateAccessToken(advisorId: string): Promise<string> {
  const session = await requireUser();

  if (!session?.user?.id) {
    throw new Error("Unauthorized");
  }

  try {
    // Generate a new access token
    const accessToken = randomBytes(32).toString('hex');

    // Update the UserAdvisor record with the new token
    await prisma.userAdvisor.update({
      where: {
        userId_advisorId: {
          userId: session.user!.id,
          advisorId,
        },
      },
      data: {
        accessToken,
      },
    });

    return accessToken;
  } catch (error) {
    console.error("Failed to regenerate access token:", error);
    throw new Error("Failed to regenerate access token");
  }
}
